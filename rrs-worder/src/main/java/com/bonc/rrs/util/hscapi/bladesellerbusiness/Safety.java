package com.bonc.rrs.util.hscapi.bladesellerbusiness;

import cn.hutool.crypto.SecureUtil;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @Description: 安全校验对象
 * @Author: liujunpeng
 * @Date: 2022/1/19 18:25
 * @Version: 1.0
 */
@Data
public class Safety implements Serializable {

    public Safety(String number, String methodName) {
        this.number = number;
        this.time = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
        this.sign = getSafetySign(number, this.time, methodName);
    }

    /**
     * 单据号
     */
    private String number;
    /**
     * 时间（格式：yyyyMMddHHmmss）
     */
    private String time;
    /**
     * 密文（生成规则请联系票税云工程师）
     */
    private String sign;

    /**
     * 密文生成方法
     *
     * @param xsddm
     * @param time
     * @param methodName
     * @return
     */
    public static String getSafetySign(String xsddm, String time, String methodName) {
        return SecureUtil.md5(xsddm + time + methodName);
    }
}
