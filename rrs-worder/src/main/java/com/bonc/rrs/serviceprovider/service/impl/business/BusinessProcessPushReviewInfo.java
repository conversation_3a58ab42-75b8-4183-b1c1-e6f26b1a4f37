package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.domain.PushReviewInfo;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;

/**
 * @Description CPIM 服务商报修订单审核信息回传
 * @Description /jumpto/openapi/sp/pushSubmitReviewInfo
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class BusinessProcessPushReviewInfo extends AbstractBusinessProcess {

    final IBydApiService iBydApiService;

    @Override
    public String getProcessCode() {
        return "pushReviewInfo";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        if (StringUtils.isBlank(businessProcessPo.getWorderNo())) {
            return Result.error("工单号不能为空");
        }
        try {
            WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(businessProcessPo.getWorderNo());
            if (worderInformationEntity == null) {
                return Result.error("非法工单号");
            }

            PushReviewInfo pushReviewInfo = new PushReviewInfo();
            pushReviewInfo.setOperatePerson(StringUtils.defaultIfBlank(businessProcessPo.getOperator(), OPERATE_PERSON));
            pushReviewInfo.setOrderCode(worderInformationEntity.getCompanyOrderNumber());
            pushReviewInfo.setCommitDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(System.currentTimeMillis()));
            //服务商提交审核
            OtherApiResponse otherApiResponse = iBydApiService.pushReviewInfo(pushReviewInfo);
            if (otherApiResponse.getErrno() != 0) {
                return Result.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }
        }catch (Exception e){
            log.error("服务商提交审核回传出现异常", e);
            return Result.error("服务商提交审核回传失败");
        }
        return Result.success();
    }
}
