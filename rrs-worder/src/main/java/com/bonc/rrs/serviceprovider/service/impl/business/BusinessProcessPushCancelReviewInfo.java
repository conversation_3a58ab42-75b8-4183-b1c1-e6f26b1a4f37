package com.bonc.rrs.serviceprovider.service.impl.business;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.baidumap.dao.DotSendsRecordMapper;
import com.bonc.rrs.baidumap.entity.DotSendsRecord;
import com.bonc.rrs.byd.domain.PushSubmitInfo;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.entity.DotInformationEntity;
import com.bonc.rrs.worder.entity.WorderInformationAttributeEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.DotInformationService;
import com.bonc.rrs.workManager.dao.RemarkLogMapper;
import com.bonc.rrs.workManager.dao.WorkMsgDao;
import com.bonc.rrs.workManager.entity.AttendantSendsRecord;
import com.bonc.rrs.workManager.entity.SendWorderRecord;
import com.bonc.rrs.workManager.service.AttendantSendsRecordService;
import com.bonc.rrs.workManager.service.SendWorderRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.activiti.engine.impl.util.CollectionUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @Description 服务商取消审核信息回传
 * @Description /jumpto/openapi/sp/pushCancelReviewInfo
 * @Date 2024/2/29 14:04
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class BusinessProcessPushCancelReviewInfo extends AbstractBusinessProcess {

    @Autowired
    private DotInformationService dotInformationService;

    @Autowired
    private SendWorderRecordService sendWorderRecordService;

    @Autowired
    private AttendantSendsRecordService attendantSendsRecordService;

    @Autowired(required = false)
    private RemarkLogMapper remarkLogMapper;

    @Resource
    private DotSendsRecordMapper dotSendsRecordMapper;

    final WorkMsgDao workMsgDao;

    final WorderInformationAttributeDao worderInformationAttributeDao;

    final IBydApiService iBydApiService;

    @Override
    public String getProcessCode() {
        return "pushCancelReviewInfo";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        try {
            //查询工单信息
            WorderInformationEntity worderInformationEntity = worderInformationService.getById(businessProcessPo.getWorderId());
            WorderInformationAttributeEntity attributeEntity = worderInformationAttributeDao.selectOne(new QueryWrapper<WorderInformationAttributeEntity>().eq("attribute_code","CPIMCancelOrder").eq("is_delete","0").eq("worder_id",worderInformationEntity.getWorderId()));
            PushSubmitInfo pushSubmitInfo = new PushSubmitInfo();
            pushSubmitInfo.setResult(businessProcessPo.getAuditType());
            if (!businessProcessPo.getAuditType().equals("1")){
                pushSubmitInfo.setRemark(businessProcessPo.getAuditRemark());
            }
            pushSubmitInfo.setOrderCode(worderInformationEntity.getCompanyOrderNumber());
            pushSubmitInfo.setExaminePerson(OPERATE_PERSON);
            //调用推送CPIM接口
            if (attributeEntity!=null){
                worderInformationAttributeDao.updateDelete(worderInformationEntity.getWorderId(),attributeEntity.getAttribute(),"CPIMCancelOrder");
            }
            // TODO: 服务商取消审核信息回传
            OtherApiResponse otherApiResponse = iBydApiService.pushSubmitInfo(pushSubmitInfo);
            if (otherApiResponse.getErrno() != 0) {
                return Result.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }
        }catch (Exception e) {
            log.error("服务商取消审核信息回传出现异常", e);
            return Result.error("服务商取消审核信息回传失败");
        }
        return Result.success();
    }

    //修改网点派单数量表
    public void updateDotSend(Integer sendNum, Integer id) {
        DotSendsRecord dotSendsRecord = new DotSendsRecord().setSendNum(sendNum).setId(id);
        dotSendsRecordMapper.updateById(dotSendsRecord);
    }
}
