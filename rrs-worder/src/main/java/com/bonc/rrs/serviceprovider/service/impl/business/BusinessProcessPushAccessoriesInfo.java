package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.domain.PushAccessoriesInfo;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.service.SysFilesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 安装附件信息回传
 * @Description /jumpto/openapi/sp/pushAccessoriesInfo
 * @Date 2024/2/28 14:04
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class BusinessProcessPushAccessoriesInfo extends AbstractBusinessProcess {

    final WorderExtFieldService worderExtFieldService;

    final SysFilesService sysFilesService;

    final IBydApiService iBydApiService;

    @Override
    public String getProcessCode() {
        return "pushAccessoriesInfo";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        if (StringUtils.isBlank(businessProcessPo.getWorderNo())) {
            return Result.error("工单号不能为空");
        }
        try {
            WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(businessProcessPo.getWorderNo());
            if (worderInformationEntity == null) {
                return Result.error("非法工单号");
            }
            // 查询工单扩展字段
            List<WorderExtFieldEntity> worderExtFieldEntities = worderExtFieldService.getFieldsByWorderNo(businessProcessPo.getWorderNo());
            Map<Integer, String> fileMap = new HashMap<>(16);
            // 获取文件类型的id，查询文件信息
            List<String> fieldIds = worderExtFieldEntities.stream().filter(item -> (item.getFieldType() == 3 || item.getFieldType() == 4) && item.getFieldPurpose() == 3 && StringUtils.isNotBlank(item.getFieldValue())).map(WorderExtFieldEntity::getFieldValue).collect(Collectors.toList());
            if (fieldIds.isEmpty()) {
                return Result.error("工单未上传任何附件信息");
            }
            List<SysFileEntity> fileList = sysFilesService.getSysFileByIds(String.join(",", fieldIds));
            if (!fileList.isEmpty()) {
                // 转Map
                fileList.forEach(item -> fileMap.put(item.getFileId(),item.getNewName()));
            }
            // 转Map
            Map<Integer, String> filedMap = new HashMap<>(16);
            worderExtFieldEntities.forEach(item -> filedMap.put(item.getFieldId(),item.getFieldValue()));

            PushAccessoriesInfo req = new PushAccessoriesInfo();
            req.setOrderCode(worderInformationEntity.getCompanyOrderNumber());
            req.setOperatePerson(OPERATE_PERSON);
            req.setConstructionImage(getFilePath(1641, filedMap, fileMap, false));
            req.setSequenceImage(getFilePath(1642, filedMap, fileMap, false));
//            req.setToolBlanketImage(getFilePath(1643, filedMap, fileMap, false));
            req.setZeroVoltageImage(getFilePath(1644, filedMap, fileMap, false));
//            req.setGroundVoltageImage(getFilePath(1645, filedMap, fileMap, false));
            req.setDisclaimersImage(getFilePath(1646, filedMap, fileMap, false));
            req.setLoadConfirmationImage(getFilePath(1647, filedMap, fileMap, false));
            req.setLineStartImage(getFilePath(1648, filedMap, fileMap, false));
            req.setLineEndImage(getFilePath(1649, filedMap, fileMap, false));
//            req.setPowerSupplyImage(getFilePath(1650, filedMap, fileMap, false));
//            req.setLayingPathImage(getFilePath(1651, filedMap, fileMap, true));
            req.setFireZeroResistanceImage(getFilePath(1652, filedMap, fileMap, false));
//            req.setFireGroundResistanceImage(getFilePath(1653, filedMap, fileMap, false));
//            req.setFireZeroVoltageImage(getFilePath(1654, filedMap, fileMap, false));
//            req.setFireGroundVoltageImage(getFilePath(1655, filedMap, fileMap, false));
            req.setZeroGroundVoltageImage(getFilePath(1656, filedMap, fileMap, false));

            String groundWireImagefilePath = getFilePath(1657, filedMap, fileMap, false);
            if (StringUtils.isBlank(getFilePath(1657, filedMap, fileMap, false))) {
                return Result.error("接地线或接地极照片必填");
            }
            req.setGroundWireImage(groundWireImagefilePath);

//            req.setConnectionImage(getFilePath(1658, filedMap, fileMap, false));
            req.setManPileImage(getFilePath(1659, filedMap, fileMap, false));
            req.setIncreaseChargeImage(getFilePath(1660, filedMap, fileMap, false));
            req.setConfirmationImage(getFilePath(1640, filedMap, fileMap, false));
            req.setTrialChargeImage(getFilePath(1661, filedMap, fileMap, false));
//            req.setImage1(getFilePath(1771, filedMap, fileMap, true));

            // 5.7 版本新增
//            req.setSideCodeImage(getFilePath(Arrays.asList(2004, 2008), filedMap, fileMap, false));
//            req.setNetworkResultImage(getFilePath(Arrays.asList(2005, 2009), filedMap, fileMap, false));
//            req.setBindStatusImage(getFilePath(Arrays.asList(2006, 2010), filedMap, fileMap, false));
            req.setPileNameplateImage(getFilePath(Arrays.asList(2007, 2011), filedMap, fileMap, false));

            OtherApiResponse otherApiResponse = iBydApiService.pushAccessoriesInfo(req);
            if (otherApiResponse.getErrno() != 0) {
                return Result.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }
        } catch (Exception e) {
            log.error("服务商安装附件信息回传出现异常", e);
            return Result.error("服务商安装附件信息回传失败");
        }
        return Result.success();
    }


    /**
     * 根据属性ID获取文件oss路径
     *
     * @param fieldId  属性ID
     * @param filedMap 属性ID与属性值的映射
     * @param fileMap  文件ID与文件路径的映射
     * @param splicing 是否需要拼接
     * @return 文件oss路径
     */
    String getFilePath(Integer fieldId, Map<Integer, String> filedMap, Map<Integer, String> fileMap, boolean splicing) {
        if (fieldId == null) {
            return null;
        }
        return getFilePath(Collections.singletonList(fieldId), filedMap, fileMap, splicing);
    }

    /**
     * 根据多个属性ID获取文件oss路径
     *
     * @param fieldIds 属性ID列表
     * @param filedMap 属性ID与属性值的映射
     * @param fileMap  文件ID与文件路径的映射
     * @param splicing 是否需要拼接
     * @return 文件oss路径，多个路径用逗号分隔
     */
    String getFilePath(List<Integer> fieldIds, Map<Integer, String> filedMap, Map<Integer, String> fileMap, boolean splicing) {
        if (fieldIds == null || fieldIds.isEmpty() || filedMap == null || fileMap == null) {
            return null;
        }

        List<String> allPaths = new ArrayList<>();
        for (Integer fieldId : fieldIds) {
            if (!filedMap.containsKey(fieldId)) {
                continue;
            }
            String fieldValue = filedMap.get(fieldId);
            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }

            String[] split = fieldValue.split(",");
            List<String> paths = new ArrayList<>();
            for (String value : split) {
                if (fileMap.containsKey(Integer.valueOf(value))) {
                    String path = FileUtils.copyImage(fileMap.get(Integer.valueOf(value)));
                    if (!splicing) {
                        return path;
                    }
                    paths.add(path);
                }
            }
            allPaths.addAll(paths);
        }

        return String.join(",", allPaths);
    }
}
