package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * CPIM 推送安装订单基本安装信息
 *
 * <AUTHOR>
 */
@Data
public class PushOrderBody implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 安装订单编号
     */
    @ApiModelProperty(value = "安装订单编号", required = true)
    private String orderCode;

    /**
     * 安装联系人姓名
     */
    @ApiModelProperty(value = "安装联系人姓名", required = true)
    private String contactName;

    /**
     * 安装联系人手机号
     */
    @ApiModelProperty(value = "安装联系人手机号", required = true)
    private String contactMobile;

    /**
     * 安装地址: 详细地址信息(例如 XXX 社区 XX 小区)，不包含省市区的信息
     */
    @ApiModelProperty(value = "安装地址: 详细地址信息(例如 XXX 社区 XX 小区)，不包含省市区的信息", required = true)
    private String contactAddress;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码", required = true)
    private String provinceCode;

    /**
     * 省名称
     */
    @ApiModelProperty(value = "省名称", required = true)
    private String provinceName;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    private String cityCode;

    /**
     * 市名称
     */
    @ApiModelProperty(value = "市名称")
    private String cityName;

    /**
     * 区编码
     */
    @ApiModelProperty(value = "区编码")
    private String areaCode;

    /**
     * 区名称
     */
    @ApiModelProperty(value = "区名称")
    private String areaName;

    /**
     * 充电桩名称
     */
    @ApiModelProperty(value = "充电桩名称", required = true)
    private String wallboxName;

    /**
     * 充电桩功率
     */
    @ApiModelProperty(value = "充电桩功率", required = true)
    private String wallboxPower;

    /**
     * 是否带桩上门  1 是 0 否
     */
    @ApiModelProperty(value = "是否带桩上门  1 是 0 否", required = true)
    private String bringWallbox;

    /**
     * 派单时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "派单时间 格式：yyyy-MM-dd HH:mm:ss", required = true)
    private String dispatchTime;

    /**
     * 订单类型 10 普通订单 20 大定订单
     */
    @ApiModelProperty(value = "订单类型 10 普通订单 20 大定订单", required = true)
    private String carOwnerType;

    /**
     * 财务归属  10 权益订单 20 权益PLUS订单  30 自费订单 40 对公订单
     */
    @ApiModelProperty(value = "财务归属  10 权益订单 20 权益PLUS订单  30 自费订单 40 对公订单", required = true)
    private String type;

    /**
     * 品牌 10-比亚迪(c 端整体上线后会移除)，20-腾势，30-仰望，40- 海洋，50-王朝
     */
    @ApiModelProperty(value = "品牌 10-比亚迪(c 端整体上线后会移除)，20-腾势，30-仰望，40- 海洋，50-王朝", required = true)
    private String carBrand;

    /**
     * 车系
     */
    @ApiModelProperty(value = "车系")
    private String carSeries;

    /**
     * 车型
     */
    @ApiModelProperty(value = "车型")
    private String carModel;

    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号")
    private String vin;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String contactRemark;

    /**
     * 枪线长度
     */
    @ApiModelProperty(value = "枪线长度")
    private String gunLineLength;

    /**
     * 11-一级风险、12-一级风险、20-二级风险，30-三级风险
     */
    @ApiModelProperty(value = "风险等级：11-一级风险、12-一级风险、20-二级风险，30-三级风险")
    private String riskGrade;

    /**
     * 风险原因
     */
    @ApiModelProperty(value = "风险原因")
    private String riskReason;
}
