package com.bonc.rrs.byd.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bonc.rrs.baidumap.dao.DotSendsRecordMapper;
import com.bonc.rrs.baidumap.entity.DotSendsRecord;
import com.bonc.rrs.byd.domain.*;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.response.PushApiResponse;
import com.bonc.rrs.byd.service.DmjServiceProvidersService;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.byd.util.Sha256SignUtils;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.common.IdempotentCheck;
import com.bonc.rrs.worder.constant.FlowConstant;
import com.bonc.rrs.worder.constant.IdempotentConstant;
import com.bonc.rrs.worder.constant.RedisConstant;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.dao.WorderRemarkLogDao;
import com.bonc.rrs.worder.entity.DotInformationEntity;
import com.bonc.rrs.worder.entity.WorderInformationAttributeEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.WorderRemarkLogEntity;
import com.bonc.rrs.worder.entity.po.ExecuteFlowResultPo;
import com.bonc.rrs.worder.entity.po.IdempotentPo;
import com.bonc.rrs.worder.service.DotInformationService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worderapp.dao.WorderOperationRecodeDao;
import com.bonc.rrs.worderapp.dao.WorderOrderDao;
import com.bonc.rrs.worderapp.entity.WorderOperationRecodeEntity;
import com.bonc.rrs.worderapp.entity.dto.WorderInformationDto;
import com.bonc.rrs.workManager.dao.RemarkLogMapper;
import com.bonc.rrs.workManager.dao.WorkMsgDao;
import com.bonc.rrs.workManager.entity.AttendantSendsRecord;
import com.bonc.rrs.workManager.entity.SendWorderRecord;
import com.bonc.rrs.workManager.service.AttendantSendsRecordService;
import com.bonc.rrs.workManager.service.SendWorderRecordService;
import com.bonc.rrs.xk.service.DmjXkServiceProvidersService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.activiti.engine.impl.util.CollectionUtil;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @Description: 到每家服务商处理业务
 * @Author: liujunpeng
 * @Date: 2024/2/27 10:10
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class DmjServiceProvidersServiceImpl implements DmjServiceProvidersService {

    @Value("${byd.AppSecret}")
    private String appSecret;

    @Autowired(required = false)
    private WorderInformationService worderInformationService;

    @Autowired(required = false)
    private RemarkLogMapper remarkLogMapper;

    @Autowired(required = false)
    private SendWorderRecordService sendWorderRecordService;

    @Autowired(required = false)
    private AttendantSendsRecordService attendantSendsRecordService;
    @Resource
    private DotSendsRecordMapper dotSendsRecordMapper;

    @Autowired
    private DotInformationService dotInformationService;

    @Autowired(required = false)
    WorderOrderDao worderOrderDao;

    @Autowired(required = false)
    private WorkMsgDao workMsgDao;

    final WorderInformationDao worderInformationDao;

    final WorderInformationAttributeDao worderInformationAttributeDao;

    final WorderOperationRecodeDao worderOperationRecodeDao;

    final WorderRemarkLogDao worderRemarkLogDao;

    final IdempotentCheck idempotentCheck;

    final FlowCommon flowCommon;

    final IBydApiService iBydApiService;

    final DmjXkServiceProvidersService dmjXkServiceProvidersService;

    @Override
    public boolean verificationOfSignatures(HttpServletRequest request, JSONObject reqBody) {
        String nonce=request.getHeader("Nonce");
        String curTime=request.getHeader("Cur_Time");
        String sign=request.getHeader("Sign");
        log.info("Header获取的参数：Nonce==>{},Cur_Time==>{},Sign==>{}",nonce,curTime,sign);
        log.info("推送安装订单基本安装信息:{}",reqBody);
        Map<String, Object> params = JSON.parseObject(reqBody.toJSONString(), Map.class);
        return Sha256SignUtils.verify(sign, appSecret,nonce,curTime,params);
    }

    @Override
    public boolean verificationArrayDataOfSignatures(HttpServletRequest request, JSONArray dataArray) {
        String nonce=request.getHeader("Nonce");
        String curTime=request.getHeader("Cur_Time");
        String sign=request.getHeader("Sign");
        log.info("Header获取的参数：Nonce==>{},Cur_Time==>{},Sign==>{}",nonce,curTime,sign);
        Map<String, Object> params=new HashMap<>();
        params.put("data", dataArray);
        log.info("推送安装订单基本安装信息:{}", params);
        return Sha256SignUtils.verify(sign, appSecret,nonce,curTime,params);
    }

    @Override
    public PushApiResponse orderAuditInfoPush(OrderAuditPushReq req) {
        // 获取时间并且校验时间是否合法
        Date examineDate = parseDate(req.getExamineDate());
        if (examineDate == null) {
            return new PushApiResponse("审核时间不合法");
        }
        try {
            // 根据车企订单号查询，工单
            LambdaQueryWrapper<WorderInformationEntity> worderInfoWrapper = Wrappers.lambdaQuery();
            worderInfoWrapper.eq(WorderInformationEntity::getCompanyOrderNumber, req.getOrderCode());
            List<WorderInformationEntity> worderInformationEntities = worderInformationDao.selectList(worderInfoWrapper);
            if (worderInformationEntities.isEmpty()) {
                return new PushApiResponse("根据订单编号未查询到订单信息");
            }
            WorderInformationEntity worderInformationEntity = worderInformationEntities.get(0);
            // 判断审核结果
            String result = req.getResult();
            if ("1".equals(result)) {
                setOperation(worderInformationEntity, "比亚迪审核工单", req.getExaminePerson() + "：审核通过", examineDate);

                return new PushApiResponse("success");
                // 通过，调用车企确认安装完成
            //    return ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).approved(worderInformationEntity, 2, "比亚迪审核工单", req.getExaminePerson() + "：审核通过", examineDate);
            } else if ("2".equals(result)) {
                // 不通过：调用流程
                return  ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).reviewFailed(worderInformationEntity, "比亚迪审核工单", req.getExaminePerson() + "：拒绝工单，原因：" + req.getRemark(), examineDate);
            }
        } catch (Exception e) {
            log.error("订单审核推送出现异常", e);
            return new PushApiResponse("订单审核推送失败");
        }
        return new PushApiResponse("success");
    }

    /**
     * 审核通过
     */
    @Transactional(rollbackFor = Exception.class)
    public PushApiResponse approved(WorderInformationEntity worderInformationEntity, Integer flag, String title, String content, Date examineDate) {
        setOperation(worderInformationEntity, title, content, examineDate);
        R r = confirmInstall(worderInformationEntity.getWorderId(), flag);
        if (!r.get("code").equals(0)) {
            return new PushApiResponse(r.get("msg") + "");
        }
        return new PushApiResponse("success");
    }

    /**
     * 审核不通过
     */
    @Transactional(rollbackFor = Exception.class)
    public PushApiResponse reviewFailed(WorderInformationEntity worderInformationEntity, String title, String content, Date examineDate) {
        // 记录操作记录
        setOperation(worderInformationEntity, "比亚迪审核工单", content, examineDate);
        // 拒绝，调用流程结束
        if (flowCommon.hasFlowByWorderNo(worderInformationEntity.getWorderNo())) {
            //调用网点已接单流程
            ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderInformationEntity.getWorderNo(), FlowConstant.ProcessCode.StatusUpdate, FlowConstant.ProcessStatus.N);
            // 流程调用失败直接返回
            if (!"0".equals(executeFlowResultPo.getCode())) {
                return new PushApiResponse(executeFlowResultPo.getMsg());
            }
        } else {
            // 如果未匹配到流程强制修改状态，2,23
            worderInformationEntity.setWorderStatus(2);
            worderInformationEntity.setWorderStatusValue("安装中");
            worderInformationEntity.setWorderExecStatus(23);
            worderInformationEntity.setWorderExecStatusValue("车企回退待客服确认");
            worderInformationDao.updateById(worderInformationEntity);
        }
        return new PushApiResponse("success");
    }

    /**
     * 车企确认安装完成
     *
     * @param worderId 工单ID
     * @param flag     标识
     * @return R
     */
    @Transactional(rollbackFor = Exception.class)
    public R confirmInstall(Integer worderId, Integer flag) {
        // 由于跳过登陆认证，需要设置固定的登陆信息
        SysUserEntity sysUserEntity = new SysUserEntity();
        sysUserEntity.setUserId(89L);
        sysUserEntity.setUsername("系统自动");
        // 创建一个Subject.Builder
        Subject.Builder builder = new Subject.Builder();
        // 设置身份信息
        PrincipalCollection principals = new SimplePrincipalCollection(sysUserEntity, "系统自动");
        builder.principals(principals);
        // 设置是否已经认证
        builder.authenticated(true);
        // 创建Subject实例
        Subject subject = builder.buildSubject();
        ThreadContext.bind(subject);

        // 重复校验获取执行状态
        IdempotentPo idempotentPo = idempotentCheck.functionBegin(RedisConstant.Prefix.BUSINESS
                , "confirm"
                , worderId
                , flag);

        // 首次执行完成将返回首次结果
        if (IdempotentConstant.Result.FINISH.equals(idempotentPo.getCode())) {
            return R.ok().put("result", idempotentPo.getValue());
            // 首次调用未执行完成放回重复调用
        } else if (!IdempotentConstant.Result.SUCCESS.equals(idempotentPo.getCode())) {
            return R.ok().put("result", IdempotentConstant.Result.getName(idempotentPo.getCode()));
        }

        //安装中增加校验勘测不增加校验
        if (flag == 2 && worderInformationService.checkBanlanRule(worderId)) {
            return R.error(201, "该工单未匹配到工程结算规则！");
        }
        String result = worderInformationService.updateStatus(worderId, flag);
        // 执行结束更新执行状态
        idempotentCheck.functionFinish(RedisConstant.Prefix.BUSINESS
                , result
                , "confirm"
                , worderId
                , flag);
        return R.ok().put("result", result);
    }


    @Override
    public PushApiResponse orderPuaseInfoPush(PushSusPendOrder req) {
        // 获取时间并且校验时间是否合法
        Date suspendDate = parseDate(req.getSuspendDate());
        if (suspendDate == null) {
            return new PushApiResponse("时间不合法");
        }
        try {
            // 根据车企订单号查询，工单
            LambdaQueryWrapper<WorderInformationEntity> worderInfoWrapper = Wrappers.lambdaQuery();
            worderInfoWrapper.eq(WorderInformationEntity::getCompanyOrderNumber, req.getOrderCode());
            List<WorderInformationEntity> worderInformationEntities = worderInformationDao.selectList(worderInfoWrapper);
            if (worderInformationEntities.isEmpty()) {
                return new PushApiResponse("根据订单编号未查询到订单信息");
            }
            WorderInformationEntity worderInformationEntity = worderInformationEntities.get(0);
            // TODO: 判断是否存在小咖的转单标识，如果有小咖的转单标识，需要调用小咖接口
            if(worderInformationService.checkTransferOrder(worderInformationEntity.getWorderNo())){
                OtherApiResponse otherApiResponse = dmjXkServiceProvidersService.orderPuaseInfoPush(req);
                if (otherApiResponse.getErrno() != 0) {
                    return new PushApiResponse(otherApiResponse.getErrmsg());
                }
            }
            // 更新工单属性
            ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).updateWorderAttribute(worderInformationEntity, "Suspend", "Suspend-flag", "1", suspendDate);
            // 设置操作记录
            ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).setOperation(worderInformationEntity, worderInformationEntity.getWorderNo() + "主状态："+ worderInformationEntity.getWorderStatusValue() +"，执行状态："+ worderInformationEntity.getWorderExecStatusValue() +" 暂停工单成功", req.getSuspendPerson() + "暂停了订单，暂停原因：" + req.getSuspendDesc(), suspendDate);
            return new PushApiResponse("success");
        } catch (Exception e) {
            log.error("订单暂停出现异常", e);
            return new PushApiResponse("订单暂停失败");
        }
    }

    @Override
    public PushApiResponse orderRestoreInfoPush(PushRestoreOrder req) {
        // 获取时间并且校验时间是否合法
        Date modifyDate = parseDate(req.getModifyDate());
        if (modifyDate == null) {
            return new PushApiResponse("时间不合法");
        }
        // 设置操作记录
        try {
            // 根据车企订单号查询，工单
            LambdaQueryWrapper<WorderInformationEntity> worderInfoWrapper = Wrappers.lambdaQuery();
            worderInfoWrapper.eq(WorderInformationEntity::getCompanyOrderNumber, req.getOrderCode());
            List<WorderInformationEntity> worderInformationEntities = worderInformationDao.selectList(worderInfoWrapper);
            if (worderInformationEntities.isEmpty()) {
                return new PushApiResponse("根据订单编号未查询到订单信息");
            }
            WorderInformationEntity worderInformationEntity = worderInformationEntities.get(0);
            // TODO: 判断是否存在小咖的转单标识，如果有小咖的转单标识，需要调用小咖接口
            if (worderInformationService.checkTransferOrder(worderInformationEntity.getWorderNo())) {
                OtherApiResponse otherApiResponse = dmjXkServiceProvidersService.orderRestoreInfoPush(req);
                if (otherApiResponse.getErrno() != 0) {
                    return new PushApiResponse(otherApiResponse.getErrmsg());
                }
            }
            // 更新工单属性
            ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).updateWorderAttribute(worderInformationEntity, "Suspend", "Suspend-flag", "0", modifyDate);
            // 设置操作记录
            ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).setOperation(worderInformationEntity, worderInformationEntity.getWorderNo() + "主状态："+ worderInformationEntity.getWorderStatusValue() +"，执行状态："+ worderInformationEntity.getWorderExecStatusValue() +" 恢复工单成功", req.getOperatePerson() + "恢复了订单", modifyDate);
            return new PushApiResponse("success");
        } catch (Exception e) {
            log.error("订单恢复出现异常", e);
            return new PushApiResponse("订单恢复失败");
        }
    }

    /**
     * 更新工单的属性表信息
     *
     * @param worderInformationEntity 工单对象
     * @param attribute               属性
     * @param attributeCode           属性编码
     * @param attributeValue          属性值
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateWorderAttribute(WorderInformationEntity worderInformationEntity, String attribute, String attributeCode, String attributeValue, Date optionDate) {
        // 查询是否存在订单属性信息
        LambdaQueryWrapper<WorderInformationAttributeEntity> worderInfoAttrWrapper = Wrappers.lambdaQuery();
        worderInfoAttrWrapper.eq(WorderInformationAttributeEntity::getWorderId, worderInformationEntity.getWorderId());
        worderInfoAttrWrapper.eq(WorderInformationAttributeEntity::getAttribute, attribute);
        worderInfoAttrWrapper.eq(WorderInformationAttributeEntity::getAttributeCode, attributeCode);
        List<WorderInformationAttributeEntity> worderInformationAttributeEntities = worderInformationAttributeDao.selectList(worderInfoAttrWrapper);
        // 存在更新，不存在插入
        if (worderInformationAttributeEntities.isEmpty()) {
            WorderInformationAttributeEntity worderInformationAttributeEntity = new WorderInformationAttributeEntity(null, worderInformationEntity.getWorderId(), attributeCode, attribute, attributeValue, 0, attribute, optionDate, optionDate);
            worderInformationAttributeDao.insert(worderInformationAttributeEntity);
        } else {
            WorderInformationAttributeEntity worderInformationAttributeEntity = worderInformationAttributeEntities.get(0);
            worderInformationAttributeEntity.setAttributeValue(attributeValue);
            worderInformationAttributeEntity.setUpdateTime(optionDate);
            worderInformationAttributeDao.updateById(worderInformationAttributeEntity);
        }
    }

    /**
     * 设置操作记录
     *
     * @param worderInformationEntity 工单对象
     * @param title                   标题
     * @param content                 内容
     * @param optionDate              操作时间
     */
    @Transactional(rollbackFor = Exception.class)
    public void setOperation(WorderInformationEntity worderInformationEntity, String title, String content, Date optionDate) {
        // 保存操作记录
        WorderOperationRecodeEntity worderOperationRecodeEntity = new WorderOperationRecodeEntity();
        worderOperationRecodeEntity.setUserId(ConstantPool.BYD_OPERATOR);
        worderOperationRecodeEntity.setOperationUser(ConstantPool.BYD_OPERATOR_NAME);
        worderOperationRecodeEntity.setRecord(content);
        worderOperationRecodeEntity.setWorderNo(worderInformationEntity.getWorderNo());
        worderOperationRecodeEntity.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(optionDate));
        worderOperationRecodeEntity.setWorderStatus(String.valueOf(worderInformationEntity.getWorderStatus()));
        worderOperationRecodeEntity.setWorderExecStatus(String.valueOf(worderInformationEntity.getWorderExecStatus()));
        worderOperationRecodeDao.insert(worderOperationRecodeEntity);

        // 保存工单备注
        WorderRemarkLogEntity worderRemarkLogEntity = new WorderRemarkLogEntity();
        worderRemarkLogEntity.setWorderNo(worderInformationEntity.getWorderNo());
        worderRemarkLogEntity.setUserId(ConstantPool.BYD_OPERATOR);
        worderRemarkLogEntity.setUserName(ConstantPool.BYD_OPERATOR_NAME);
        worderRemarkLogEntity.setTitle(title);
        worderRemarkLogEntity.setContent(content);
        worderRemarkLogEntity.setCreateTime(optionDate);
        worderRemarkLogDao.insert(worderRemarkLogEntity);
    }


    public Date parseDate(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return simpleDateFormat.parse(date);
        } catch (ParseException e) {
            return null;
        }
    }

    @Override
    public String validateRequiredFields(Object obj) {
        try {
            // 获取反射对象
            Class<?> clazz = obj.getClass();
            // 拿到所有对象属性
            Field[] fields = clazz.getDeclaredFields();
            // 遍历所有属性
            for (Field field : fields) {
                // 判断是否存在ApiModelProperty注解
                if (field.isAnnotationPresent(ApiModelProperty.class)) {
                    ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
                    // 如果对象时必填属性判断是否为空
                    if (apiModelProperty.required()) {
                        field.setAccessible(true);
                        Object value;
                        try {
                            value = field.get(obj);
                        } catch (Exception e) {
                            value = null;
                        }
                        if (value == null) {
                            return "缺少" + apiModelProperty.value() + "参数";
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("参数校验出现异常",e);
            return "参数不合法";
        }

        return "";
    }

    /**
     * CPIM 用户报修订单取消信息推送服务商业务
     *
     * @param req
     * @return
     */
    @Override
    public PushApiResponse repairOrderCancel(PushRepairOrderCancel req) {
        try {
            //查询工单信息
            WorderInformationEntity worderInformationEntity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("company_order_number", req.getOrderCode()));
            if (worderInformationEntity == null) {
                return new PushApiResponse("未找到报修订单编号对应单号!");
            }
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
            String dateStr = df.format(new Date());// new Date()为获取当前系统时间
            String username = workMsgDao.selectUserNameById(89);
            String str = dateStr + " " + username;

            String desc = req.getCancelDesc();
            String reason = req.getCancelReason();
            String content = (desc != null ? desc : "") + "," + (reason != null ? reason : "");
            remarkLogMapper.addOperationLog(worderInformationEntity.getWorderNo(), str + "取消服务", content, 89);
            remarkLogMapper.updateWordeInfoState(worderInformationEntity.getWorderId(), 3, "取消服务", 21, "取消服务", 6);
            //判断当前工单是否存在服务兵并且是当天派单的，需要减一
            Integer serviceId = worderInformationEntity.getServiceId();
            if (serviceId != null) {
                //查询当前服务兵是否是当天派单的
                List<SendWorderRecord> sendWorderRecords = sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq("worder_id", worderInformationEntity.getWorderId()).eq("send_worder_type", 3).eq("accept_worder_user", serviceId).eq("delete_state", 0).ge("create_time", LocalDate.now()));
                if (sendWorderRecords != null && !sendWorderRecords.isEmpty()) {
                    List<AttendantSendsRecord> attendantSendsRecords = attendantSendsRecordService.getBaseMapper().selectList(new QueryWrapper<AttendantSendsRecord>().eq("service_id", serviceId).eq("present_date", LocalDate.now()).eq("delete_state", 0));
                    if (attendantSendsRecords != null && !attendantSendsRecords.isEmpty()) {
                        AttendantSendsRecord attendantSendsRecord = attendantSendsRecords.get(0);
                        if (attendantSendsRecord.getOverflow() > 0) {
                            attendantSendsRecord.setOverflow(attendantSendsRecord.getOverflow() - 1);
                        }
                        if (attendantSendsRecord.getSendNum() > 0) {
                            attendantSendsRecord.setSendNum(attendantSendsRecord.getSendNum() - 1);
                        }
                        attendantSendsRecord.setUpdateTime(LocalDateTime.now()).setUpdateUser((long) 89);
                        attendantSendsRecordService.updateById(attendantSendsRecord);
                    }
                }
            }
            Integer dotId = worderInformationEntity.getDotId();
            if (dotId != null) {
                //判断当前网点是否今天派单的
                List<SendWorderRecord> sendWorderRecords = sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq("worder_id", worderInformationEntity.getWorderId()).eq("delete_state", 0).eq("send_worder_type", 2).eq("accept_worder_user", dotId).ge("create_time", LocalDate.now()));
                if (sendWorderRecords != null && !sendWorderRecords.isEmpty()) {
                    //原网点派单数量-1
                    QueryWrapper<DotInformationEntity> before = new QueryWrapper<>();
                    before.eq("dot_id", dotId);
                    DotInformationEntity dotInformationEntity = dotInformationService.getOne(before);
                    if (dotInformationEntity != null && dotInformationEntity.getCount() > 0) {
                        dotInformationEntity.setCount(dotInformationEntity.getCount() - 1);
                        dotInformationService.updateById(dotInformationEntity);
                    }
                    //当天日期的原网点Id-1
                    List<DotSendsRecord> dotSendsRecordList = dotSendsRecordMapper.selectList(new QueryWrapper<DotSendsRecord>().eq("dot_id", dotId).eq("present_date", LocalDate.now()));
                    if (CollectionUtil.isNotEmpty(dotSendsRecordList) && dotSendsRecordList.get(0).getSendNum() != null && dotSendsRecordList.get(0).getSendNum() > 0) {
                        updateDotSend(dotSendsRecordList.get(0).getSendNum() - 1, dotSendsRecordList.get(0).getId());
                    }
                }
            }
            //如果分配中工单取消更新工单状态后直接返回成功
            if (worderInformationEntity.getWorderStatus() == 0 || worderInformationEntity.getWorderStatus() == 1) {
                return new PushApiResponse("success");
            }
            //勘测物料以及后续结算处理
            worderInformationService.materielBalanceAccounts(worderInformationEntity.getWorderId());
            return new PushApiResponse("success");
        } catch (Exception e) {
            log.error("用户报修订单取消信息推送服务商业务处理异常", e);
            return new PushApiResponse("用户报修订单取消信息推送服务商业务处理异常");
        }
    }

    /**
     * CPIM 用户报修订单关闭信息推送服务商业务
     *
     * @param req
     * @return
     */
    @Override
    public PushApiResponse repairOrderClose(PushRepairOrderClose req) {
        try {
            //查询工单信息
            WorderInformationEntity worderInformationEntity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("company_order_number", req.getOrderCode()));
            if (worderInformationEntity == null) {
                return new PushApiResponse("未找到报修订单编号对应单号!");
            }
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
            String dateStr = df.format(new Date());// new Date()为获取当前系统时间
            String username = workMsgDao.selectUserNameById(89);
            String str = dateStr + " " + username;

            String content = req.getModifyPerson() + "关闭订单,原因:" + req.getRemark();
            remarkLogMapper.addOperationLog(worderInformationEntity.getWorderNo(), str + "关闭工单", content, 89);
            remarkLogMapper.updateWordeInfoState(worderInformationEntity.getWorderId(), 3, "关闭工单", 21, "取消服务", 6);
            //判断当前工单是否存在服务兵并且是当天派单的，需要减一
            Integer serviceId = worderInformationEntity.getServiceId();
            if (serviceId != null) {
                //查询当前服务兵是否是当天派单的
                List<SendWorderRecord> sendWorderRecords = sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq("worder_id", worderInformationEntity.getWorderId()).eq("send_worder_type", 3).eq("accept_worder_user", serviceId).eq("delete_state", 0).ge("create_time", LocalDate.now()));
                if (sendWorderRecords != null && !sendWorderRecords.isEmpty()) {
                    List<AttendantSendsRecord> attendantSendsRecords = attendantSendsRecordService.getBaseMapper().selectList(new QueryWrapper<AttendantSendsRecord>().eq("service_id", serviceId).eq("present_date", LocalDate.now()).eq("delete_state", 0));
                    if (attendantSendsRecords != null && !attendantSendsRecords.isEmpty()) {
                        AttendantSendsRecord attendantSendsRecord = attendantSendsRecords.get(0);
                        if (attendantSendsRecord.getOverflow() > 0) {
                            attendantSendsRecord.setOverflow(attendantSendsRecord.getOverflow() - 1);
                        }
                        if (attendantSendsRecord.getSendNum() > 0) {
                            attendantSendsRecord.setSendNum(attendantSendsRecord.getSendNum() - 1);
                        }
                        attendantSendsRecord.setUpdateTime(LocalDateTime.now()).setUpdateUser((long) 89);
                        attendantSendsRecordService.updateById(attendantSendsRecord);
                    }
                }
            }
            Integer dotId = worderInformationEntity.getDotId();
            if (dotId != null) {
                //判断当前网点是否今天派单的
                List<SendWorderRecord> sendWorderRecords = sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq("worder_id", worderInformationEntity.getWorderId()).eq("delete_state", 0).eq("send_worder_type", 2).eq("accept_worder_user", dotId).ge("create_time", LocalDate.now()));
                if (sendWorderRecords != null && !sendWorderRecords.isEmpty()) {
                    //原网点派单数量-1
                    QueryWrapper<DotInformationEntity> before = new QueryWrapper<>();
                    before.eq("dot_id", dotId);
                    DotInformationEntity dotInformationEntity = dotInformationService.getOne(before);
                    if (dotInformationEntity != null && dotInformationEntity.getCount() > 0) {
                        dotInformationEntity.setCount(dotInformationEntity.getCount() - 1);
                        dotInformationService.updateById(dotInformationEntity);
                    }
                    //当天日期的原网点Id-1
                    List<DotSendsRecord> dotSendsRecordList = dotSendsRecordMapper.selectList(new QueryWrapper<DotSendsRecord>().eq("dot_id", dotId).eq("present_date", LocalDate.now()));
                    if (CollectionUtil.isNotEmpty(dotSendsRecordList) && dotSendsRecordList.get(0).getSendNum() != null && dotSendsRecordList.get(0).getSendNum() > 0) {
                        updateDotSend(dotSendsRecordList.get(0).getSendNum() - 1, dotSendsRecordList.get(0).getId());
                    }
                }
            }
            //如果分配中工单取消更新工单状态后直接返回成功
            if (worderInformationEntity.getWorderStatus() == 0 || worderInformationEntity.getWorderStatus() == 1) {
                return new PushApiResponse("success");
            }
            //勘测物料以及后续结算处理
            worderInformationService.materielBalanceAccounts(worderInformationEntity.getWorderId());
            return new PushApiResponse("success");
        } catch (Exception e) {
            log.error("用户报修订单关闭信息推送服务商业务处理异常", e);
            return new PushApiResponse("用户报修订单关闭信息推送服务商业务处理异常");
        }
    }

    /**
     * CPIM 关闭订单后推送关闭订单信息
     *
     * @param req
     * @return
     */
    @Override
    public PushApiResponse installOrderClose(PushCloseOrder req) {
        try {
            Date examineDate = parseDate(req.getModifyDate());
            //查询工单信息
            WorderInformationEntity worderInformationEntity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("company_order_number", req.getOrderCode()));
            if (worderInformationEntity == null) {
                return new PushApiResponse("未找到安装订单编号对应单号!");
            }
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
            String dateStr = df.format(new Date());// new Date()为获取当前系统时间
            String username = workMsgDao.selectUserNameById(89);
            String str = dateStr + " " + username;
            String content = req.getModifyPerson() + "关闭订单,原因:" + req.getRemark();
            remarkLogMapper.addOperationLog(worderInformationEntity.getWorderNo(), str + "关闭工单", content, 89);
            remarkLogMapper.updateWordeInfoState(worderInformationEntity.getWorderId(), 3, "关闭工单", 21, "取消服务", 6);
            //判断当前工单是否存在服务兵并且是当天派单的，需要减一
            Integer serviceId = worderInformationEntity.getServiceId();
            if (serviceId != null) {
                //查询当前服务兵是否是当天派单的
                List<SendWorderRecord> sendWorderRecords = sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq("worder_id", worderInformationEntity.getWorderId()).eq("send_worder_type", 3).eq("accept_worder_user", serviceId).eq("delete_state", 0).ge("create_time", LocalDate.now()));
                if (sendWorderRecords != null && !sendWorderRecords.isEmpty()) {
                    List<AttendantSendsRecord> attendantSendsRecords = attendantSendsRecordService.getBaseMapper().selectList(new QueryWrapper<AttendantSendsRecord>().eq("service_id", serviceId).eq("present_date", LocalDate.now()).eq("delete_state", 0));
                    if (attendantSendsRecords != null && !attendantSendsRecords.isEmpty()) {
                        AttendantSendsRecord attendantSendsRecord = attendantSendsRecords.get(0);
                        if (attendantSendsRecord.getOverflow() > 0) {
                            attendantSendsRecord.setOverflow(attendantSendsRecord.getOverflow() - 1);
                        }
                        if (attendantSendsRecord.getSendNum() > 0) {
                            attendantSendsRecord.setSendNum(attendantSendsRecord.getSendNum() - 1);
                        }
                        attendantSendsRecord.setUpdateTime(LocalDateTime.now())
                                .setUpdateUser((long) 89);
                        attendantSendsRecordService.updateById(attendantSendsRecord);
                    }
                }
            }
            Integer dotId = worderInformationEntity.getDotId();
            if (dotId != null) {
                //判断当前网点是否今天派单的
                List<SendWorderRecord> sendWorderRecords = sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq("worder_id", worderInformationEntity.getWorderId()).eq("delete_state", 0).eq("send_worder_type", 2).eq("accept_worder_user", dotId).ge("create_time", LocalDate.now()));
                if (sendWorderRecords != null && !sendWorderRecords.isEmpty()) {
                    //原网点派单数量-1
                    QueryWrapper<DotInformationEntity> before = new QueryWrapper<>();
                    before.eq("dot_id", dotId);
                    DotInformationEntity dotInformationEntity = dotInformationService.getOne(before);
                    if (dotInformationEntity != null && dotInformationEntity.getCount() > 0) {
                        dotInformationEntity.setCount(dotInformationEntity.getCount() - 1);
                        dotInformationService.updateById(dotInformationEntity);
                    }
                    //当天日期的原网点Id-1
                    List<DotSendsRecord> dotSendsRecordList = dotSendsRecordMapper.selectList(new QueryWrapper<DotSendsRecord>().eq("dot_id", dotId).eq("present_date", LocalDate.now()));
                    if (CollectionUtil.isNotEmpty(dotSendsRecordList) && dotSendsRecordList.get(0).getSendNum() != null && dotSendsRecordList.get(0).getSendNum() > 0) {
                        updateDotSend(dotSendsRecordList.get(0).getSendNum() - 1, dotSendsRecordList.get(0).getId());
                    }
                }
            }
            //如果分配中工单取消更新工单状态后直接返回成功
            if (worderInformationEntity.getWorderStatus() == 0 || worderInformationEntity.getWorderStatus() == 1) {
                if (worderInformationService.checkTransferOrder(worderInformationEntity.getWorderNo())) {
                    dmjXkServiceProvidersService.pushCloseOrder(req);
                    // 记录操作记录
                    ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).setOperation(worderInformationEntity, "比亚迪推送小咖工单关闭", "关闭工单", examineDate);
                }
                return new PushApiResponse("success");
            }
            //勘测物料以及后续结算处理
            worderInformationService.materielBalanceAccounts(worderInformationEntity.getWorderId());
            if (worderInformationService.checkTransferOrder(worderInformationEntity.getWorderNo())) {
                // 记录操作记录
                ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).setOperation(worderInformationEntity, "比亚迪推送小咖工单关闭", "关闭工单", examineDate);
                dmjXkServiceProvidersService.pushCloseOrder(req);
            }
            return new PushApiResponse("success");
        } catch (Exception e) {
            log.error("关闭订单后推送关闭订单信息业务处理异常", e);
            return new PushApiResponse("关闭订单后推送关闭订单信息业务处理异常");
        }
    }

    /**
     * CPIM 厂端审核后推送审核信息
     * @param req
     * @return
     */
    @Override
    public PushApiResponse companyAuditInformation(PushSubmitInfo req) {
        try {
            Date examineDate = parseDate(req.getExamineDate());
            if (examineDate == null) {
                return new PushApiResponse("审核时间不合法");
            }
            List<Object> data = new ArrayList<>();
            //进行批量车企确认完成
            String orderCodes = req.getOrderCode();
            String[] orderCodeArray = orderCodes.split(",");

            List<String> bydCompanyList = new ArrayList();

            List<String> xkCompanyList = new ArrayList();

            Map<String,DataInfo> xkErrorMap = new HashMap<>();

            for (String orderCompanyNumber : orderCodeArray) {
                //查询工单信息
                WorderInformationEntity worderInformationEntity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>()
                        .eq("company_order_number", orderCompanyNumber)
                        .ne("worder_exec_status", 21).ne("worder_status", 6)
                );

                if (worderInformationEntity == null) {
                    Map<String, String> error = new HashMap<>();
                    error.put("orderCode", orderCompanyNumber);
                    error.put("reason", "根据订单编号未查询到订单信息");
                    data.add(error);
                    continue;
                }
                if (worderInformationService.checkTransferOrder(worderInformationEntity.getWorderNo())){
                    xkCompanyList.add(orderCompanyNumber);
                }else{
                    bydCompanyList.add(orderCompanyNumber);
                }
            }

            if (xkCompanyList.size()>0) {
                String orderCode = "";
                for (int i = 0; i < xkCompanyList.size(); i++) {
                    orderCode = orderCode + xkCompanyList.get(i);
                    if (i+1<xkCompanyList.size()){
                        orderCode= orderCode+",";
                    }
                }

                PushSubmitInfo pushSubmitInfo = new PushSubmitInfo();
                pushSubmitInfo.setOrderCode(orderCode);
                pushSubmitInfo.setResult(req.getResult());
                pushSubmitInfo.setRemark(req.getRemark());
                pushSubmitInfo.setExaminePerson(req.getRemark());
                pushSubmitInfo.setExamineDate(req.getExamineDate());
                PushApiResponse pushApiResponse = dmjXkServiceProvidersService.pushSubmitInfo(req);
                if (!pushApiResponse.getMessage().equals("success")){
                    List<Object> list = pushApiResponse.getData();
                    ObjectMapper objectMapper = new ObjectMapper();
                    for (Object obj : list) {
                        JsonNode jsonNode = objectMapper.valueToTree(obj);
                        DataInfo dataInfo = new DataInfo();
                        dataInfo.setOrderCode(jsonNode.get("orderCode").asText());
                        dataInfo.setReason(jsonNode.get("reason").asText());
                        xkErrorMap.put(jsonNode.get("orderCode").asText(),dataInfo);
                    }
                }
            }

            if (req.getResult().equals("1")) {
                for (String orderCode : bydCompanyList) {
                    //查询工单信息
                    WorderInformationEntity worderInformationEntity = worderInformationService.getOne(
                            new QueryWrapper<WorderInformationEntity>().eq("company_order_number", orderCode)
                                    .ne("worder_exec_status", 21).ne("worder_status", 6)
                    );
                    // 记录操作记录
                    ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).setOperation(worderInformationEntity, "比亚迪审核工单", req.getExaminePerson() + "：审核通过", examineDate);
                    // 通过，调用车企确认安装完成
//                    R r = ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).confirmInstall(worderInformationEntity.getWorderId(), 2);
//                    if (!r.get("code").equals(0)) {
//                        Map<String, String> error = new HashMap<>();
//                        error.put("orderCode", orderCode);
//                        error.put("reason", String.valueOf(r.get("msg")));
//                        data.add(error);
//                    }
                }
                for (String orderCode : xkCompanyList) {
                    if (xkErrorMap.containsKey(orderCode)){
                        Map<String, String> error = new HashMap<>();
                        error.put("orderCode", orderCode);
                        error.put("reason", String.valueOf(xkErrorMap.get(orderCode).getReason()));
                        data.add(error);
                        continue;
                    }
                    //查询工单信息
                    WorderInformationEntity worderInformationEntity = worderInformationService.getOne(
                            new QueryWrapper<WorderInformationEntity>().eq("company_order_number", orderCode)
                                    .ne("worder_exec_status", 21).ne("worder_status", 6)
                    );
                    // 记录操作记录
                    ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).setOperation(worderInformationEntity, "比亚迪审核小咖工单", req.getExaminePerson() + "：审核通过", examineDate);
                    // 通过，调用车企确认安装完成
                    R r = ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).confirmInstall(worderInformationEntity.getWorderId(), 2);
                    if (!r.get("code").equals(0)) {
                        Map<String, String> error = new HashMap<>();
                        error.put("orderCode", orderCode);
                        error.put("reason", String.valueOf(r.get("msg")));
                        data.add(error);
                    }
                }
                if (data != null && data.size() <= 0) {
                    return new PushApiResponse("success");
                } else {
                    PushApiResponse pushApiResponse = new PushApiResponse();
                    pushApiResponse.setMessage("partially success");
                    pushApiResponse.setData(data);
                    return pushApiResponse;
                }
            } else if (req.getResult().equals("2")) {
                for (String orderCode : bydCompanyList) {
                    //查询工单信息
                    WorderInformationEntity worderInformationEntity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("company_order_number", orderCode)
                            .ne("worder_exec_status", 21).ne("worder_status", 6)
                    );
                    ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).setOperation(worderInformationEntity, "比亚迪审核工单", req.getExaminePerson() + "：拒绝工单，原因：" + req.getRemark(), examineDate);
                    WorderInformationDto worderInformationDto = new WorderInformationDto();
                    worderInformationDto.setWorderNo(worderInformationEntity.getWorderNo());
                    //安装资料待客服审核
                    worderInformationDto.setWorderExecStatus(15);
                    // 拒绝，调用流程结束
                    if (flowCommon.hasFlowByWorderNo(worderInformationEntity.getWorderNo())){
                        //调用网点已接单流程
                        ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderInformationEntity.getWorderNo(), FlowConstant.ProcessCode.StatusUpdate, FlowConstant.ProcessStatus.N);
                        // 流程调用失败直接返回
                        if (!"0".equals(executeFlowResultPo.getCode())) {
                            Map<String, String> error = new HashMap<>();
                            error.put("orderCode", orderCode);
                            error.put("reason", executeFlowResultPo.getMsg());
                            data.add(error);
                        }
                    }else{
                        worderOrderDao.updateWorderInformation(worderInformationDto);
                    }
                }
                for (String orderCode : xkCompanyList) {
                    if (xkErrorMap.containsKey(orderCode)){
                        Map<String, String> error = new HashMap<>();
                        error.put("orderCode", orderCode);
                        error.put("reason", String.valueOf(xkErrorMap.get(orderCode).getReason()));
                        data.add(error);
                        continue;
                    }
                    //查询工单信息
                    WorderInformationEntity worderInformationEntity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("company_order_number", orderCode)
                            .ne("worder_exec_status", 21).ne("worder_status", 6)
                    );
                    //记录操作记录
                    ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).setOperation(worderInformationEntity, "比亚迪审核小咖工单", req.getExaminePerson() + "：拒绝工单，原因：" + req.getRemark(), examineDate);
                    WorderInformationDto worderInformationDto = new WorderInformationDto();
                    worderInformationDto.setWorderNo(worderInformationEntity.getWorderNo());
                    //安装资料整改中
                    worderInformationDto.setWorderExecStatus(14);
                    worderOrderDao.updateWorderInformation(worderInformationDto);
                    flowCommon.updateFlowStatus(worderInformationEntity.getWorderId(),"anzhuangziliaozhenggaizhong");
                }
                if (data != null && data.size() <= 0) {
                    return new PushApiResponse("success");
                } else {
                    PushApiResponse pushApiResponse = new PushApiResponse();
                    pushApiResponse.setMessage("partially success");
                    pushApiResponse.setData(data);
                    return pushApiResponse;
                }
            } else {
                return new PushApiResponse("审核结果不合法");
            }
        }catch (Exception e){
            log.error("厂端审核后推送审核信息业务处理异常",e);
            return new PushApiResponse("厂端审核后推送审核信息业务处理异常");
        }
    }

    /**
     * CPIM 用户取消安装后推送取消信息至服务商
     * @param req
     * @return
     */
    @Override
    public PushApiResponse installOrderCancel(PushCancelOrder req) {
        try{
            Date examineDate = parseDate(req.getCancelDate());
            //查询工单信息
            WorderInformationEntity worderInformationEntity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("company_order_number", req.getOrderCode()));
            if (worderInformationEntity == null) {
                return new PushApiResponse("未找到安装订单编号对应单号!");
            }
            if (worderInformationEntity.getWorderExecStatus().equals("21")){
                return new PushApiResponse("该单号已经取消!");
            }
            WorderInformationAttributeEntity attributeEntity = worderInformationAttributeDao.selectOne(new QueryWrapper<WorderInformationAttributeEntity>().eq("attribute_code","CPIMCancelOrder").eq("is_delete","0").eq("worder_id",worderInformationEntity.getWorderId()));
            if (attributeEntity!=null){
                //有值
                return new PushApiResponse("该单号已经待取消,请勿重复推送!");
            }
            if (worderInformationService.checkTransferOrder(worderInformationEntity.getWorderNo())){
                //推送小咖
                PushApiResponse pushApiResponse = dmjXkServiceProvidersService.pushCancelOrder(req);
                if (!pushApiResponse.getMessage().equals("success")){
                    return pushApiResponse;
                }
                // 记录操作记录
                ((DmjServiceProvidersServiceImpl) AopContext.currentProxy()).setOperation(worderInformationEntity, "比亚迪推送小咖工单取消", "取消工单", examineDate);
            }else{
                worderInformationAttributeDao.insertCPIMCancelOrder(worderInformationEntity.getWorderId(),req.getCancelDate(),req.getCancelReason(),req.getCancelDesc());
            }
            return new PushApiResponse("success");
        }catch (Exception e){
            log.error("厂端审核后推送审核信息业务处理异常",e);
            return new PushApiResponse("厂端审核后推送审核信息业务处理异常");
        }
    }

    //修改网点派单数量表
    public void updateDotSend(Integer sendNum, Integer id) {
        DotSendsRecord dotSendsRecord = new DotSendsRecord().setSendNum(sendNum).setId(id);
        dotSendsRecordMapper.updateById(dotSendsRecord);
    }

    @Override
    public String validateTransferPileRequiredFields(PushRepairOrderData pushRepairOrderData) {
        if (StringUtils.isBlank(pushRepairOrderData.getTransferProvinceCode())) {
            return "移装订单缺少移桩省编码参数";
        } else if (StringUtils.isBlank(pushRepairOrderData.getTransferProvince())) {
            return "移装订单缺少移桩省名称参数";
        } else if (StringUtils.isBlank(pushRepairOrderData.getTransferCityCode())) {
            return "移装订单缺少移桩市编码参数";
        } else if (StringUtils.isBlank(pushRepairOrderData.getTransferCity())) {
            return "移装订单缺少移桩市名称参数";
        } else if (StringUtils.isBlank(pushRepairOrderData.getTransferAreaCode())) {
            return "移装订单缺少移桩区编码参数";
        } else if (StringUtils.isBlank(pushRepairOrderData.getTransferArea())) {
            return "移装订单缺少移桩区名称参数";
        } else if (StringUtils.isBlank(pushRepairOrderData.getTransferAddress())) {
            return "移装订单缺少移桩详细地址参数";
        }
        return null;
    }
}
