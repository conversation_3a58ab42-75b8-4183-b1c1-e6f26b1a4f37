package com.bonc.rrs.byd.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.byd.domain.*;
import com.bonc.rrs.byd.response.PushApiResponse;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 到每家服务商处理业务
 * @Author: liujunpeng
 * @Date: 2024/2/27 10:09
 * @Version: 1.0
 */
public interface DmjServiceProvidersService {

    /**
     * 验签
     *
     * @param request 请求头
     * @param reqBody 请求参数
     * @return 验签结果
     */
    boolean verificationOfSignatures(HttpServletRequest request, JSONObject reqBody);

    /**
     * 数组入参验签
     *
     * @param request
     * @param dataArray
     * @return
     */
    boolean verificationArrayDataOfSignatures(HttpServletRequest request, JSONArray dataArray);

    /**
     * CPIM 报修订单审核信息推送服务商接口业务处理
     *
     * @param req 入参
     * @return 响应
     */
    PushApiResponse orderAuditInfoPush(OrderAuditPushReq req);


    /**
     * CPIM 用户报修订单取消信息推送服务商业务处理
     *
     * @param req
     * @return
     */
    PushApiResponse repairOrderCancel(PushRepairOrderCancel req);

    /**
     * CPIM 用户报修订单取关闭息推送服务商业务处理
     *
     * @param req
     * @return
     */
    PushApiResponse repairOrderClose(PushRepairOrderClose req);

    /**
     * CPIM 关闭订单后推送关闭订单信息业务处理
     *
     * @param req
     * @return
     */
    PushApiResponse installOrderClose(PushCloseOrder req);

    /**
     * CPIM 厂端审核后推送审核信息
     *
     * @param req
     * @return
     */
    PushApiResponse companyAuditInformation(PushSubmitInfo req);

    /**
     * CPIM 用户取消安装后推送取消信息至服务商
     *
     * @param req
     * @return
     */
    PushApiResponse installOrderCancel(PushCancelOrder req);


    /**
     * CPIM 安装订单暂停信息推送服务商接口业务处理
     *
     * @param req 入参
     * @return 响应
     */
    PushApiResponse orderPuaseInfoPush(PushSusPendOrder req);

    /**
     * CPIM 安装订单恢复执行信息推送服务商业务处理
     *
     * @param req 入参
     * @return 响应
     */
    PushApiResponse orderRestoreInfoPush(PushRestoreOrder req);

    /**
     * 对象参数校验
     *
     * @param obj 请求参数
     * @return 校验结果
     */
    String validateRequiredFields(Object obj);

    /**
     * 报修移装订单额外必填字段校验
     *
     * @param pushRepairOrderData
     * @return
     */
    String validateTransferPileRequiredFields(PushRepairOrderData pushRepairOrderData);
}
