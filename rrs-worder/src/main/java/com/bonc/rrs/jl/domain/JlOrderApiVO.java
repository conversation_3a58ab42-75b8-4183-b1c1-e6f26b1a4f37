/**
 * Copyright (C), 2024, 山东亚微软件股份有限公司
 */
package com.bonc.rrs.jl.domain;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/19 10:05
 * @Version 1.0.0
 */

@Data
public class JlOrderApiVO implements Serializable {
    private static final long serialVersionUID = -5693603291766207808L;
    /**
     *  厂商工单唯一识别ID，用于调用厂商内部api接口及获取news工单数据凭证使用
     */
    @NotNull(message = "厂商id不能为空")
    private String servOrderId;
    /**
     *厂商页面显示的服务工单号，用于对照查询
     */
    @NotNull(message = "车企订单号不能为空")
    private String servOrderNo;
    /**
     * 客户姓名
     */
    @NotNull(message = "客户姓名不能为空")
    private String contactName;
    /**
     * 客户电话
     */
    @NotNull(message = "客户电话不能为空")
    private String contactPhone;
    /**
     *  服务地区编码
     */
    @NotNull(message = "服务地区编码不能为空")
    private String servAdCode;
    /**
     * 服务地址经纬度用半角逗号分割形如'经度,纬度'，若厂商工单中无此信息则留空
     */
    private String servLngLat;
    /**
     *  服务所在地址
     */
    @NotNull(message = "服务所在地址不能为空")
    private String servAddress;
    /**
     * 车辆VIN码
     */
    private String vinNo;
    /**
     * 车辆品牌
     */
    @NotNull(message = "车辆品牌不能为空")
    private String carBrand;
    /**
     * 车辆型号
     */
    private String carModel;
    /**
     * 服务备注
     */
    private String installRemark;
    /**
     * 工单备注
     */
    private String orderRemark;
    /**
     *  购车人姓名
     */
    private String ownerName;
    /**
     * 购车人电话
     */
    private String ownerPhone;
    /**
     * 管理APP账户
     */
    private String appAccount;
    /**
     * 车位
     */
    private String parkSpace;

}