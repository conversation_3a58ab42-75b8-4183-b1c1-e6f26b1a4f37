
package com.bonc.rrs.pay.service.imp;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.efficiencyarearule.dao.EfficiencyAreaRuleMapper;
import com.bonc.rrs.efficiencyarearule.entity.EfficiencyAreaRuleEntity;
import com.bonc.rrs.pay.entity.entity.ChangeTemplateDto;
import com.bonc.rrs.pay.entity.entity.DotDispatchRuleDto;
import com.bonc.rrs.pay.entity.entity.EfficiencyAreaRuleDto;
import com.bonc.rrs.pay.entity.entity.WorderDotDto;
import com.bonc.rrs.pay.entity.entity.*;
import com.bonc.rrs.pay.service.FunctionExportService;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.worder.dto.dto.WorderImportBatchDTO;
import com.bonc.rrs.worder.entity.BizRegionEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.listener.ChangeTemplateListener;
import com.bonc.rrs.worder.listener.CommonImportListener;
import com.bonc.rrs.worder.listener.DotDispatchRuleDtoListener;
import com.bonc.rrs.worder.listener.EfficiencyAreaRuleImportListener;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.listener.WorderImportPayBtchListener;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worderapp.entity.WorderInformation;
import com.bonc.rrs.worderinvoice.dao.WorderInvoiceMapper;
import com.bonc.rrs.workManager.controller.ApportionController;
import com.bonc.rrs.workManager.dao.BizRegionMapper;
import com.bonc.rrs.workManager.dao.BrandDotScoreMapper;
import com.bonc.rrs.workManager.dao.DotDispatchMapper;
import com.bonc.rrs.workManager.entity.BrandDotScoreBean;
import com.bonc.rrs.workManager.entity.DotDispatch;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.SpringContextUtils;
import com.youngking.lenmoncore.common.utils.StringUtils;
import lombok.extern.log4j.Log4j2;
import net.sf.cglib.core.CollectionUtils;
import org.activiti.engine.impl.util.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.PipedReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/9 14:20
 * @Version 1.0.0
 */
@Service
@Log4j2
public class FunctionExportServiceImpl implements FunctionExportService {
    @Autowired
    private EfficiencyAreaRuleMapper efficiencyAreaRuleMapper;
    @Autowired
    private BizRegionMapper bizRegionMapper;
    @Autowired
    private DotDispatchMapper dotDispatchMapper;

    @Autowired
    private BrandDotScoreMapper brandDotScoreMapper;
    @Autowired
    private WorderInformationService worderInformationService;

    @Autowired
    private WorderInvoiceMapper worderInvoiceMapper;


    @Override
    public R efficiencyAreaRuleExport(MultipartFile file, String name) {
        log.info("｛｝ 维护区域品牌", name);
        ArrayList<String> errorMsgList = new ArrayList<String>();
        ArrayList<EfficiencyAreaRuleDto> list = new ArrayList<EfficiencyAreaRuleDto>();
        try {
            EasyExcel.read(file.getInputStream(), EfficiencyAreaRuleDto.class, new EfficiencyAreaRuleImportListener(list)).sheet().doRead();
            log.info(list);

            if (!list.isEmpty()) {
                List<EfficiencyAreaRuleDto> dtos = list.stream().filter(d -> StringUtils.isBlank(d.getRegcode())).collect(Collectors.toList());
                List<BizRegionEntity> bizRegionEntities = null;
                //查找区域编码
                if (!dtos.isEmpty()) {
//                    List<String> names = dtos.stream().map(EfficiencyAreaRuleDto::getRegName).collect(Collectors.toList());
//                    QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
//                    queryWrapper.in("name", names);
//                    bizRegionEntities = bizRegionMapper.selectList(queryWrapper);
                }
                for (EfficiencyAreaRuleDto dto : list) {
                    if (StringUtils.isBlank(dto.getRegcode())) {
                        // BizRegionEntity bizRegionEntity = bizRegionEntities.stream().filter(b -> b.getName().equals(dto.getRegName())).findFirst().orElse(null);
                        QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
                        queryWrapper.like("name", dto.getRegName());
                        List<BizRegionEntity> bizRegionEntitys = bizRegionMapper.selectList(queryWrapper);
                        if (bizRegionEntitys != null && !bizRegionEntitys.isEmpty()) {
                            dto.setRegcode(bizRegionEntitys.get(0).getRegcode());
                        } else {
                            errorMsgList.add(dto.getRegName() + "地区名称未找到地区编码");
                            list.remove(dto);
                        }
                    }
                    if (StringUtils.isBlank(dto.getBrandId())) {
                        errorMsgList.add(dto.getBrandName() + "品牌id不能为空");
                    }
                    if (StringUtils.isBlank(dto.getExecrule())) {
                        errorMsgList.add("区域名称不能为空");
                    }
                }
                if (!errorMsgList.isEmpty()) {
                    return R.error().put("msg", errorMsgList);
                }
                Map<String, Map<String, List<EfficiencyAreaRuleDto>>> groupedOrders = list.stream()
                        .collect(Collectors.groupingBy(EfficiencyAreaRuleDto::getBrandId,
                                Collectors.groupingBy(EfficiencyAreaRuleDto::getExecrule)));
                groupedOrders.forEach((brandId, map) -> {
                    List<String> brandIds = new ArrayList<>();
                    brandIds.add(brandId);
                    QueryWrapper<EfficiencyAreaRuleEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.in("brand", brandIds);
                    efficiencyAreaRuleMapper.delete(queryWrapper);
                    map.forEach((execrule, dtoList) -> {
                        //
                        List<String> regcodes = dtoList.stream().map(EfficiencyAreaRuleDto::getRegcode).collect(Collectors.toList());
                        Map<String, Object> params = new HashMap<>();
                        params.put("brandIds", brandIds);
                        params.put("execrule", "'" + execrule + "'");
                        params.put("regcodes", regcodes);
                        efficiencyAreaRuleMapper.addEfficiencyAreaRule(params);
                    });
                });
            }
        } catch (Exception e) {
            return R.error().put("msg", "导入数据异常," + e.getMessage());
        }
        return R.ok();
    }

    @Override
    public R updateDotDispatchRule(MultipartFile file, String brandIds, String cycle) {
        if (StringUtils.isBlank(brandIds)) {

            return R.error("请输入品牌");
        }
        if (StringUtils.isBlank(cycle)) {
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            cycle = currentDate.format(formatter);
        }
        ArrayList<DotDispatchRuleDto> list = new ArrayList<DotDispatchRuleDto>();
        try {
            List<String> brandIdList = Arrays.stream(brandIds.split(",")).collect(Collectors.toList());
            EasyExcel.read(file.getInputStream(), DotDispatchRuleDto.class, new DotDispatchRuleDtoListener(list)).sheet().doRead();
            log.info(list);
            List<Integer> numbers = new ArrayList<>();
            if (!list.isEmpty()) {
                //根据城市分组
                Map<Integer, List<DotDispatchRuleDto>> map = list.stream().filter(d -> d.getDotId() != null && d.getCityId() != null).collect(Collectors.groupingBy(DotDispatchRuleDto::getCityId));

                for (Integer cityId : map.keySet()) {
                    List<DotDispatchRuleDto> dtoList = map.get(cityId);
                    // 删除原有dot_dispatch_rule
                    if (dtoList.size() > 1) {
                        QueryWrapper<DotDispatch> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("city_id", cityId).eq("ser_num", dtoList.size()).in("brand_id", brandIdList);
                        dotDispatchMapper.delete(queryWrapper);
                        //新增dot_dispatch_rule
                        numbers.clear();
                        List<Integer> integers = dtoList.stream().map(DotDispatchRuleDto::getScore).sorted(Comparator.reverseOrder()).collect(Collectors.toList());
                        numbers.addAll(integers);
                        int result = findGCD(numbers);
                        StringBuilder sb = new StringBuilder();
                        for (Integer integer : integers) {
                            sb.append(integer / result + ":");
                        }
                        String string = sb.toString();
                        String dispatchRatio = string.substring(0, string.length() - 1);
                        //新增 dot_dispatch_rule
                        addDispatchRatio(brandIdList, cityId, dispatchRatio, integers.size());
                        QueryWrapper<BrandDotScoreBean> qw = new QueryWrapper<>();
                        qw.eq("cycle", cycle).eq("city", cityId).in("brand_id", brandIdList);
                        //删除rrs_brand_dot_score
                        brandDotScoreMapper.delete(qw);

                        addBrandDotScoreBean(dtoList, brandIdList, cityId, cycle);
                    }
                }
            }
        } catch (IOException e) {
            return R.error().put("msg", "导入数据异常," + e.getMessage());
        }

        return R.ok();
    }

    @Override
    public R updateDotInformation(MultipartFile file) {
        ArrayList<WorderDotDto> list = new ArrayList<WorderDotDto>();
        try {
            EasyExcel.read(file.getInputStream(), WorderDotDto.class, new CommonImportListener<WorderDotDto>(list)).sheet().doRead();
        } catch (IOException e) {
            return R.error().put("msg", "导入数据异常," + e.getMessage());
        }
        List<String> errorMsgList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        ApportionController apportionController = SpringContextUtils.getBean("apportionController", ApportionController.class);
        for (WorderDotDto dto : list) {
            if (StringUtils.isBlank(dto.getCompanyOrderNumber()) && StringUtils.isBlank(dto.getWorderNo())) {
                String msg = "工单号和车企订单号不能同时为空";
                errorMsgList.add(msg);
                continue;
            }
            if (StringUtils.isBlank(dto.getDotName())) {
                String msg = "网点名不能为空";
                errorMsgList.add(msg);
                continue;
            }
            Results results = null;
            if (StringUtils.isNotBlank(dto.getWorderNo())) {
                results = apportionController.sendDotPointAuditWorderNo(dto.getWorderNo(), dto.getDotName());
            } else {
                results = apportionController.sendDotPointAuditCompanyOrderNumber(dto.getCompanyOrderNumber(), dto.getDotName());
            }

            if (!results.getCode().equals(0)) {
                String msg =  (StringUtils.isNotBlank(dto.getWorderNo()) ? dto.getWorderNo() : dto.getCompanyOrderNumber() ) + " 改派失败 " + results.getMsg();
                errorMsgList.add(msg);
            }
        }
        if (!errorMsgList.isEmpty()) {
            return R.error().put("errorinfo",JSON.toJSONString(errorMsgList));
        }
        return R.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R changeTemplate(MultipartFile file) {
        List<ChangeTemplateDto> list = new ArrayList<>();
        try {
            EasyExcel.read(
                    file.getInputStream()
                    , ChangeTemplateDto.class
                    , new ChangeTemplateListener(list)
            ).sheet().doRead();
        } catch (IOException e) {
            log.error("读取文件失败", e);
            return R.error().put("failList","读取文件失败");
        }

        // log.info(list);
        // list.forEach(e -> log.info("update  worder_information set template_id = {} where worder_no = '{}'", e.getTemplateId(), e.getWorderNo()));

        List<String> allWorderNos = list.stream().map(ChangeTemplateDto::getWorderNo).collect(Collectors.toList());
        if (allWorderNos.isEmpty()) {
            return R.error().put("failList","工单号不能为空");
        }
        List<WorderInformationEntity> invoicedWorderList = worderInformationService.getInvoicedWordersByWorderNoList(allWorderNos);
        List<String> failList = invoicedWorderList.stream().map(e -> e.getWorderNo() + "已开票不能回退").collect(Collectors.toList());
        List<String> invoicedWorderNos = invoicedWorderList.stream().map(WorderInformationEntity::getWorderNo).collect(Collectors.toList());

        // 去掉已开票的工单
        allWorderNos.removeAll(invoicedWorderNos);
        list.removeIf(o -> invoicedWorderNos.contains(o.getWorderNo()));
        if (list.isEmpty() || allWorderNos.isEmpty()) {
            return R.error().put("failList","工单号不能为空");
        }

        List<WorderInformationEntity> finishedWorders = worderInformationService.getFinishedWorders(allWorderNos);

        // 回退
        worderInformationService.rollbackWorderStatusTo16(finishedWorders);

        list.stream()
                .collect(Collectors.groupingBy(ChangeTemplateDto::getTemplateId, Collectors.mapping(ChangeTemplateDto::getWorderNo, Collectors.toList())))
                .forEach(worderInformationService::updateWorderTemplate);
        if (!failList.isEmpty()) {
            return R.error().put("failList", JSON.toJSONString(failList));
        }
        return R.ok();
    }

    @Override
    public R revertWorderStatus(MultipartFile file) {
        List<String> worderNos = new ArrayList<>();
        try {
            EasyExcel.read(
                    file.getInputStream()
                    , WorderImportBatchDTO.class
                    , new AnalysisEventListener<WorderImportBatchDTO>() {
                        public void invoke(WorderImportBatchDTO dto, AnalysisContext context) {
                            worderNos.add(dto.getA());
                            if (worderNos.size() > 2000) {
                                throw new RRException("文件最大限制为2000行");
                            }
                        }

                        public void doAfterAllAnalysed(AnalysisContext context) {
                            log.info("所有数据解析完成,total size:{}", worderNos.size());
                        }
                    }
            ).sheet().doRead();
        } catch (IOException e) {
            log.error("读取文件失败", e);
            return R.error().put("failList","读取文件失败");
        }
        List<WorderInformationEntity> invoicedWorders = worderInformationService.getInvoicedWordersByWorderNoList(worderNos);
        List<String> failList = invoicedWorders.stream()
                .map(entity -> entity.getWorderNo() + "已开票不能回退")
                .collect(Collectors.toList());

        // 去掉已开票的工单
        worderNos.removeAll(invoicedWorders.stream().map(WorderInformationEntity::getWorderNo).collect(Collectors.toList()));
        List<WorderInformationEntity> finishedWorders = worderInformationService.getFinishedWorders(worderNos);
        for (WorderInformationEntity entity : finishedWorders) {
            if (entity.getInvoiceId() != null) {
                failList.add(entity.getWorderNo() + "已开票不能回退");
            }
        }
        worderInformationService.rollbackWorderStatusTo16(finishedWorders);
        if (!failList.isEmpty()) {
            return R.error().put("failList", JSON.toJSONString(failList));
        }
        return R.ok();
    }

    @Override
    public R updateIncre(MultipartFile file) {
        ArrayList<IncreDto> list = new ArrayList<IncreDto>();
        try {
            EasyExcel.read(file.getInputStream(), IncreDto.class, new CommonImportListener<IncreDto>(list)).sheet().doRead();
        } catch (IOException e) {
            return R.error().put("msg", "导入数据异常," + e.getMessage());
        }
        if(CollectionUtil.isEmpty(list)){
            return R.error("不能为空");
        }
        List<String> worderNos = list.stream().map(d -> d.getWorderNo()).collect(Collectors.toList());
        List<IncreDto> dtoList =  worderInvoiceMapper.listIncreInfoByWorderNo(worderNos);
        List<String> errorMsgList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        for (IncreDto dto : list) {
            if (StringUtils.isBlank(dto.getWorderNo())) {
                String msg = "工单号不能同时为空";
                errorMsgList.add(msg);
                continue;
            }
            if (StringUtils.isBlank(dto.getCustomerCode())) {
                String msg = "税号不能为空";
                errorMsgList.add(msg);
                continue;
            }
            if (StringUtils.isBlank(dto.getCustomerName())) {
                String msg = "发票抬头不能为空";
                errorMsgList.add(msg);
                continue;
            }
            try {
                IncreDto increDto = dtoList.stream().filter(d->d.getWorderNo().equals(dto.getWorderNo())).findFirst().orElse(null);
                if(increDto != null){
                    increDto.setCustomerCode(dto.getCustomerCode());
                    increDto.setCustomerName(dto.getCustomerName());
                    //更改
                    worderInvoiceMapper.updateByInvoiceId(increDto);
                    QueryWrapper<WorderInformationEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("worder_no", dto.getWorderNo());
                    WorderInformationEntity w = new WorderInformationEntity();
                    w.setWorderIncreStatus(0);
                    worderInformationService.update(w, queryWrapper);
                }
            } catch (Exception e) {
                log.error(e);
                errorMsgList.add(dto.getWorderNo() + "发生未知异常");
            }
        }
        if (!errorMsgList.isEmpty()) {
            return R.error().put("errorinfo",JSON.toJSONString(errorMsgList));
        }
        return R.ok();
    }

    private void addBrandDotScoreBean(List<DotDispatchRuleDto> dtoList, List<String> brandIdList, Integer cityId, String finalCycle) {
        dtoList.forEach(dto -> {
            for (String brandId : brandIdList) {
                BrandDotScoreBean brandDotScoreBean = new BrandDotScoreBean();
                brandDotScoreBean.setCity(dto.getCityId());
                brandDotScoreBean.setCycle(finalCycle);
                brandDotScoreBean.setBrandId(Integer.parseInt(brandId));
                brandDotScoreBean.setDotId(dto.getDotId());
                brandDotScoreBean.setPerformance(BigDecimal.ZERO);
                brandDotScoreBean.setServiceAbility(BigDecimal.ZERO);
                brandDotScoreBean.setAddedPerformance(BigDecimal.ZERO);
                brandDotScoreBean.setMissionCritical(BigDecimal.ZERO);
                brandDotScoreBean.setDeduct(BigDecimal.ZERO);
                brandDotScoreBean.setScore(BigDecimal.ZERO);
                brandDotScoreBean.setManualScore(new BigDecimal(dto.getScore()));
                brandDotScoreBean.setCreateTime(LocalDateTime.now());
                brandDotScoreBean.setDeleteState(0);
                brandDotScoreMapper.insert(brandDotScoreBean);
            }
        });
    }

    private void addDispatchRatio(List<String> brandIdList, Integer cityId, String dispatchRatio, Integer serNum) {
        for (String brandId : brandIdList) {
            dotDispatchMapper.insert(new DotDispatch(null, serNum, dispatchRatio, 0, new Date(), Integer.parseInt(brandId), cityId));
        }
    }


    public int findGCD(List<Integer> numbers) {
        if (numbers == null || numbers.size() == 0) {
            throw new IllegalArgumentException("输入的数字列表不能为空");
        }

        int gcd = numbers.get(0);
        for (int i = 1; i < numbers.size(); i++) {
            gcd = gcd(gcd, numbers.get(i));
        }
        return gcd;
    }

    // 计算两个整数的最大公约数
    private int gcd(int a, int b) {
        while (b != 0) {
            int temp = b;
            b = a % b;
            a = temp;
        }
        return a;
    }
}