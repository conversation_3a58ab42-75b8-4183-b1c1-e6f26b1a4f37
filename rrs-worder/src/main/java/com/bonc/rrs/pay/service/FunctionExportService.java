
package com.bonc.rrs.pay.service;

import com.youngking.lenmoncore.common.utils.R;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/9 14:18
 * @Version 1.0.0
 */
public interface FunctionExportService {


    R efficiencyAreaRuleExport(MultipartFile file,String name);

    R updateDotDispatchRule(MultipartFile file, String brandIds,String cycle);

    R updateDotInformation(MultipartFile file);

    R changeTemplate(MultipartFile file);

    R revertWorderStatus(MultipartFile file);

    R updateIncre(MultipartFile file);
}