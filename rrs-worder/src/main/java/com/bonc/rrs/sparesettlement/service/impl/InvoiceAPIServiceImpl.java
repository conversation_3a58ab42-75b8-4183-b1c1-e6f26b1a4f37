package com.bonc.rrs.sparesettlement.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.invoice.enterprises.util.InvoiceBusiness;
import com.bonc.rrs.pay.dao.WorderOrderLogMapper;
import com.bonc.rrs.pay.manage.utils.SpringUtils;
import com.bonc.rrs.pay.model.entity.WorderOrderLogDTO;
import com.bonc.rrs.sparesettlement.config.TicketConfigs;
import com.bonc.rrs.sparesettlement.dao.BillingOrderRecodeMapper;
import com.bonc.rrs.sparesettlement.dao.BillingRecodeMapper;
import com.bonc.rrs.sparesettlement.dao.InvoiceOrderItemsMapper;
import com.bonc.rrs.sparesettlement.dao.InvoiceRecodeMapper;
import com.bonc.rrs.sparesettlement.enums.BillingRespStatusEnum;
import com.bonc.rrs.sparesettlement.model.entity.*;
import com.bonc.rrs.sparesettlement.model.request.BillingOperRequestDTO;
import com.bonc.rrs.sparesettlement.model.request.BookkeepingRequest;
import com.bonc.rrs.sparesettlement.service.InvoiceAPIService;
import com.bonc.rrs.sparesettlement.utils.CertificateUtils;
import com.bonc.rrs.sparesettlement.utils.HttpUtils;
import com.bonc.rrs.util.hscapi.bladesellerbusiness.getsellerinvoice.RespData;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.bonc.rrs.worder.entity.dto.WorderInfoDTO;
import com.bonc.rrs.worderinformationaccount.entity.InvoiceBusinessTypeDetailEntity;
import com.bonc.rrs.worderinformationaccount.service.InvoiceBusinessTypeDetailService;
import com.bonc.rrs.worderinformationaccount.service.WorderInformationAccountService;
import com.bonc.rrs.workManager.dao.WorkMsgDao;
import com.bonc.rrs.workManager.entity.OperationRecord;
import com.common.pay.common.enums.PayTypeEnum;
import com.common.pay.common.utils.ResponseResult;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.Constant;
import com.youngking.lenmoncore.common.utils.InvoiceUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

import javax.annotation.Resource;
import javax.xml.transform.Result;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
/**
 * 发票API业务逻辑接口实现
 * <AUTHOR> 2020-4-11
 * @description
 */
@Transactional(rollbackFor = {Exception.class,RuntimeException.class})
@Service("invoiceAPIService")
@Log4j2
public class InvoiceAPIServiceImpl implements InvoiceAPIService {

    private Logger logger = LoggerFactory.getLogger(InvoiceAPIServiceImpl.class);

    @Resource
    private WorderOrderLogMapper worderOrderLogMapper;
    @Resource
    private WorderInformationDao worderInformationDao;
    @Resource
    private BillingOrderRecodeMapper billingOrderRecodeMapper;
    @Resource
    private BillingRecodeMapper billingRecodeMapper;
    @Resource
    private InvoiceRecodeMapper invoiceRecodeMapper;
    @Resource
    private InvoiceOrderItemsMapper invoiceOrderItemsMapper;

    @Autowired
    private InvoiceBusinessTypeDetailService invoiceBusinessTypeDetailService;

    @Resource
    WorkMsgDao workMsgDao;
    @Autowired
    private InvoiceBusiness invoiceBusiness;
    @Autowired
    private WorderInformationAccountService worderInformationAccountService;

    private  final String pattern = "yyyy-MM-dd HH:mm:ss";

    /**
     * 用户申请开票处理操作
     * @param billingOperRequest
     * */
    @Override
    public R billingOper(BillingOperRequestDTO billingOperRequest, BindingResult bindingResult, SysUserEntity userEntity) {
        String worderStatus = null;
        String worderExecStatus = null;
        String result = null;
        /** 数据验证 */
        if(bindingResult.hasErrors()){
            List<String> errors = new ArrayList<>();
            List<FieldError> fieldErrors = bindingResult.getFieldErrors();
            fieldErrors.forEach(fieldError -> {
                //日志打印不符合校验的字段名和错误提示
                logger.error("error field is : {} ,message is : {}", fieldError.getField(), fieldError.getDefaultMessage());
                errors.add(String.format("error field is : %s ,message is : %s", fieldError.getField(), fieldError.getDefaultMessage()));
            });
            result = "数据验证出错";
            return R.error().putList(errors);
        }

        /*List<WorderOrderLogDTO>  worderOrderLogs =  worderOrderLogMapper.findOrderLogInfoList(billingOperRequest.getWorderNo(),Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS);
        if(worderOrderLogs == null || worderOrderLogs.size() < 1 ){
            logger.error("未支付完成,无法生成发票订单,请求信息：{}",billingOperRequest);
            result = "未支付完成,无法生成发票订单";
            return R.error(result);
        }

        WorderOrderLogDTO  worderOrderLog = worderOrderLogs.get(0);
        worderOrderLog.setApply(true);*/


        List<WorderOrderLogDTO>  worderOrderLogs =  worderOrderLogMapper.findOrderLogInfoList(billingOperRequest.getWorderNo(),Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS);
        if(worderOrderLogs == null || worderOrderLogs.size() < 1 ){
            logger.error("未支付完成,无法生成发票订单,请求信息：{}",billingOperRequest);
            result = "未支付完成,无法生成发票订单";
            return R.error(result);
        }
        // 通过工单编号获取开票订单的客户信息
        BillingOrderRecordDTO billingOrderRecord =   billingOrderRecodeMapper.findBillingOrderRecordByoOrderNo(billingOperRequest.getWorderNo());
        if(billingOrderRecord == null){
            logger.error("客户还没有申请开票,工单编号:{}",billingOperRequest.getWorderNo());
            throw new RuntimeException("客户还没有申请开票");
        }

        //获取开票的记录
        BillingRecodeDTO   billingRecord = billingRecodeMapper.findBillingRecordBySerialNo(billingOrderRecord.getSerialNo());
        if(billingRecord==null){
            logger.error("发票记录不存在");
            return R.error();
        }

//        if(!userEntity.getUserId().equals(billingRecord.getOperUserId())){
//            logger.error("生成发票和开票不是同一个人,用户ID:{}",userEntity.getUserId());
//            result = "生成发票和开票不是同一个人,用户ID:{}" + userEntity.getUserId();
//            return R.error("操作权限不足");
//        }
        // 判断是否真在开票或者已经开票完成
        if(billingRecord.getBillingStatus() == Constant.INVOICERECODESTATUS.STATUS_LOADING
                || billingRecord.getBillingStatus() == Constant.INVOICERECODESTATUS.STATUS_SUCCSESS){
            logger.error("不可重复开票,用户ID:{}",userEntity.getUserId()+",当前状态："+billingRecord.getBillingStatus());
            result = "不可重复开票,用户ID:6"+userEntity.getUserId()+",当前状态："+billingRecord.getBillingStatus();
            return R.error("不可重复开票");
        }
        WorderOrderLogDTO  worderOrderLog = worderOrderLogs.get(0);
        OperationRecord operationRecord = new OperationRecord(userEntity.getUserId(), userEntity.getUsername(), result, billingOperRequest.getWorderNo());
        operationRecord.setWorderStatus(worderStatus);
        operationRecord.setWorderExecStatus(worderExecStatus);
        //用户申请开票
        worderOrderLog.setApply(true);

        InvoiceRecodeDTO invoiceRecodeDTO = invoiceRecodeMapper.findInvoiceRecodeBySerialNo(billingOrderRecord.getSerialNo());

        InvoiceDataUpdateDTO invoiceDataUpdateDTO = new InvoiceDataUpdateDTO();
        invoiceDataUpdateDTO.setFinanceInvoiceType(billingOperRequest.getInvoiceType());
        invoiceDataUpdateDTO.setSerialNo(billingOrderRecord.getSerialNo());
        invoiceRecodeMapper.updateInvoiceRecordBySerialNo(invoiceDataUpdateDTO);

        //增项发票未推送财务中台时，可多次修改开票信息，先删除历史增项信息，重新插入数据
        invoiceBusinessTypeDetailService.deleteByInvoiceId(invoiceRecodeDTO.getInvoiceId());
        InvoiceBusinessTypeDetailEntity invoiceBusinessTypeDetailEntity = new InvoiceBusinessTypeDetailEntity();
        invoiceBusinessTypeDetailEntity.setInvoiceId(invoiceRecodeDTO.getInvoiceId());
        invoiceBusinessTypeDetailEntity.setJzfwfsd(billingOperRequest.getJzfwfsd());
        invoiceBusinessTypeDetailEntity.setFsdxxdz(billingOperRequest.getFsdxxdz());
        invoiceBusinessTypeDetailEntity.setJzxmmc(billingOperRequest.getJzxmmc());
        invoiceBusinessTypeDetailEntity.setKdsbz(billingOperRequest.getKdsbz());
        invoiceBusinessTypeDetailEntity.setTdzzsxmbh("无");
        invoiceBusinessTypeDetailService.save(invoiceBusinessTypeDetailEntity);

        if(judgeEnableBilling(worderOrderLog)){
            // 对能够开票的数据进行处理
            billingOrderRecord.setEmail(billingOperRequest.getEmail());
            billingRecord.setBillingOrderRecord(billingOrderRecord);
//            R r = userEnableBillingPostProccessor(  billingOperRequest, billingRecord);
//            operationRecord.setRecord("开票成功");
//            workMsgDao.insertOperation(operationRecord);
        }
        workMsgDao.insertOperation(operationRecord);
        // 处理还不能达到开票的数据
       return noBookkeepingPostProcessor( billingOperRequest, billingOrderRecord,worderOrderLog);
        /*if(worderOrderLogMapper.updateApplyStatus(worderOrderLog.getOrderNo(),true) < 1){
            logger.error("更新支付流水的用户申请状态失败");
            throw new RuntimeException("更新支付流水的用户申请状态失败");
        }
        if(worderInformationDao.updateWorderInformation(billingOperRequest.getWorderNo())<1){
            logger.error("更新工单增项结算状态失败");
            throw new RuntimeException("更新工单增项结算状态失败");
        }
        return R.ok();*/
    }

    /**
     * 处理还没有记账成功
     * */
    R noBookkeepingPostProcessor(BillingOperRequestDTO billingOperRequest,BillingOrderRecordDTO billingOrderRecord,WorderOrderLogDTO  worderOrderLog ){
        /** 更新支付流水的用户申请状态 */
        if(worderOrderLogMapper.updateApplyStatus(worderOrderLog.getOrderNo(),true) < 1){
            logger.error("更新支付流水的用户申请状态失败");
            throw new RuntimeException("更新支付流水的用户申请状态失败");
        }

        /** 更新订单信息 */
        if(!StringUtils.isNullOrEmpty(billingOperRequest.getEmail())){
            if( billingOrderRecodeMapper.updateEmailInfo(billingOrderRecord.getSerialNo(),billingOperRequest.getEmail()) < 1){
                logger.error("订单信息更新失败");
                throw new RuntimeException("订单信息更新失败");
            }
        }
        /** 更新发票记录信息 */
        InvoiceDataUpdateDTO invoiceData = new InvoiceDataUpdateDTO();
        invoiceData.setCustomerName(billingOperRequest.getCustomerName());
        /** 角色 */
        invoiceData.setInvoiceType(billingOperRequest.getRole());
        invoiceData.setCustomerCode(billingOperRequest.getCustomerCode());
        invoiceData.setInvoiceStatus(0);
        invoiceData.setSerialNo(billingOrderRecord.getSerialNo());
        if( invoiceRecodeMapper.updateInvoiceRecord(invoiceData) < 1){
            throw  new RuntimeException("更新发票记录信息更新失败");
        }
        return R.ok();
    }
    /**
     * 用户对申请能够进行开票的处理
     * */
    R userEnableBillingPostProccessor( BillingOperRequestDTO billingOperRequest,BillingRecodeDTO billingRecord){
        /** 处理开票请求报文 */
        BillingDTO billing = new BillingDTO();
        R result =   billingRequestBodyPostProcessor( billing, billingOperRequest,   billingRecord);
        if(result!=null){
            return result;
        }
        /** 开票请求处理 */
        String respData = "";
        try {
            String requestJson = JSONObject.toJSONString(billing);
            respData = transmitDate(requestJson);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("开票失败，原因"+e.getCause()+","+e.getMessage());
            return R.error("申请开票失败");
        }
        return dealWithReturnDataPostProccessor( billingOperRequest,billing,respData);
    }
    /**
     *
     * */

    /**
     *接口回调
     * */
    @Override
    public Object billingCallback(String requestBody) {
       logger.info("接口回调开始————————————————————————————————————————");
        if(StringUtils.isNullOrEmpty(requestBody)){
            return R.Result("error","返回消息错误");
        }
        /** 结果处理 */
        return  billingCallbackPostProcessor(requestBody);
    }
    /**
     * 生成发票记录
     * */
    @Override
    public R generateBillingRecord(String worderNo, Long userId) {
        return generateBillingRecord( worderNo, userId, false);
    }

    @Override
    public R generateBillingRecord(String worderNo, Long userId, Boolean update) {
        return payCompleteGenerateInvoiceInfoPostProccessor( worderNo, userId, update);
    }

    @Override
    public R generateBillingRecord(Integer worderId, Long userId, Boolean update) {
        WorderInfoEntity worderInfoEntity = worderInformationDao.getByWorderId(worderId);
        return payCompleteGenerateInvoiceInfoPostProccessor( worderInfoEntity.getWorderNo(), userId, update);
    }
    /**
     *记账处理
     * @param  bookkeepingRequest
     * */
    @Override
    public Object bookkeepingPostProccessor(BookkeepingRequest bookkeepingRequest) {
        /** 获取当前的支付结果 */
        List<WorderOrderLogDTO>  worderOrderLogs =  worderOrderLogMapper.findOrderLogInfoList(bookkeepingRequest.getWorderNo(),Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS);
        if(worderOrderLogs == null || worderOrderLogs.size() < 1 ){
            logger.error("未支付完成,无法生成发票订单");
            return R.error();
        }
        WorderOrderLogDTO worderOrderLog = worderOrderLogs.get(0);
        if(worderOrderLog.getBookkeepingStatus()==Constant.BOOKKEEPINGSTATUS.SUCCESS){
            return R.error("不可重复记账");
        }
        worderOrderLog.setBookkeepingStatus(bookkeepingRequest.getStatus());
        R result = R.ok();
        if(judgeEnableBilling(worderOrderLog)){
            /** 用户已经申请，直接进行开票 */
            result =  bookkeepingBillingPostProccessor( bookkeepingRequest.getWorderNo());
        }

        /** 记录记账状态 */
        if(worderOrderLogMapper.updateBookkeepingStatus(worderOrderLog.getOrderNo(), Constant.BOOKKEEPINGSTATUS.SUCCESS) < 1) {
            logger.error("更新记账状态失败，操作流水:{}",worderOrderLog.getOrderNo());
            return R.error("操作失败");
        }
        return result;
    }
    /**
     *开票查询接口
     * @param serialNo
     * @param orderNo
     * @return Object
     * */
    @Override
    public Object billingQuery(String serialNo,String orderNo) {
        /** 获取工单信息发票的操作流水号 */
        SortedMap<String,Object> sortedMap = new TreeMap();
        sortedMap.put("serialNo",serialNo);
        String postTime = new SimpleDateFormat(pattern)
                .format(new Date(System.currentTimeMillis()));
        sortedMap.put("postTime", postTime);
        Map<String,String> criteria = new HashMap<>();
        criteria.put("orderNo",orderNo);
        sortedMap.put("criteria",criteria);
        String requestJson = JSONObject.toJSONString(sortedMap);
        String respData  = "";
        try {
            respData = transmitDate(requestJson);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return  billingCallbackPostProcessor(respData);
    }

    @Override
    public Object doInvoiceAfterQuery() {
        List<String> list =   worderInformationDao.findWoderNoByIncreStatus(10);
         if(CollectionUtils.isEmpty(list)){
             return ResponseResult.success();
         }
         for(String orderNo : list){
             //String response =  queryInvoiceInfo( constructEntry(seriualNo));
             //调用开票申请结果查询接口
             String resp = invoiceBusiness.getSellerInvoice(orderNo);
             //开票结果查询接口调用异常
             if(resp == null){
                log.error("=============单据号：{}=查询开票结果失败===========",orderNo);
                continue;
             }
             //开票结果后置处理
             newDoBilling(resp);
         }
        return ResponseResult.success();
    }

    @Override
    public Object test() {
        String response =  queryInvoiceInfo( constructEntry("b6705d09-78f6-406e-97a8-ff9dd70c7a80"));
        System.out.printf(response);
        return null;
    }

    /**
     * 任务调度进行处理申请开票和已经记账而未进行开票的数据进行处理
     * @return
     */
    @Override
    public Object doHadBilledAndInvoicedData() {
        /** 获取所有已记账和已经申请开票的记录 */
        List<Integer> list =new ArrayList<>();
        list.add(1);
        list.add(4);
        //查询未开票和开票失败的
        Set<String>  worderNos =  worderOrderLogMapper.findWorderNoWithNoBilled(1,1,list);
        logger.info("订单数据:{}",worderNos);
        if(CollectionUtils.isEmpty(worderNos)){
            return ResponseResult.success("暂时没有数据可处理");
        }
        /** 进行开票处理 */
        worderNos.forEach(worderNo->{
          Result result = null;
            try {
                bookkeepingBillingPostProccessor(worderNo);
            }catch (Exception e){
                logger.error("开票失败，工单编号:{},处理结果:{}",worderNo,result);
            }finally {
                workMsgDao.insertOperation(new OperationRecord(0L, "", JSONObject.toJSONString(result),worderNo));
            }
        });
        return ResponseResult.success();
    }

    @Override
    public String queryInvoice(String serialNo) {
        String response =  queryInvoiceInfo( constructEntry(serialNo));
        return response;
    }


    String constructEntry(String serialNo){
        String postTime = new SimpleDateFormat(pattern)
                .format(new Date(System.currentTimeMillis()));
        SortedMap sortedMap = new TreeMap();
        sortedMap.put("serialNo",serialNo);
        sortedMap.put("postTime",postTime);
        List<Object> criterias = new ArrayList<>();
        SortedMap criteriaContent = new TreeMap();
        criterias.add(criteriaContent);
        criteriaContent.put("name","singlesSerialNo");
        criteriaContent.put("value",serialNo);
        sortedMap.put("criteria",criterias);
        return JSONObject.toJSONString(sortedMap);
    }


    /**
     * 用户已经申请，直接进行开票后置处理
     * */
    R bookkeepingBillingPostProccessor(String worderNo){
        logger.info("执行开票操作");
        /** 通过工单编号获取开票订单的客户信息 */
        BillingOrderRecordDTO billingOrderRecord =   billingOrderRecodeMapper.findBillingOrderRecordByoOrderNo(worderNo);
        if(billingOrderRecord == null){
            logger.error("客户还没有申请开票,工单编号:{}",worderNo);
            throw new RuntimeException("客户还没有申请开票");
        }
        /** 获取开票的记录 */
        BillingRecodeDTO   billingRecord = billingRecodeMapper.findBillingRecordBySerialNo(billingOrderRecord.getSerialNo());
        if(billingRecord==null){
            logger.error("发票记录不存在");
            return R.error();
        }
        /** 处理开票请求报文 */
        BillingDTO billing = new BillingDTO();
        R result = constituteBillingSucObj( billing,   billingRecord, billingOrderRecord);
        if(result!=null){
            return result;
        }
        /** 开票请求处理 */
        String respData = "";
        try {
            //String requestJson = JSONObject.toJSONString(billing);
            //respData = transmitDate(requestJson);
            //税票云开票申请接口
            respData = invoiceBusiness.setIncreXwsqZzsHead(billing);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("开票失败，原因"+e.getCause()+","+e.getMessage());
            return R.error("申请开票失败");
        }
        /** 对开票后的数据进行处理 */
        return  biilingReturnDataPostProccessor(billing, respData);
    }

    /**
     *对记账成功且用户已经申请开票的申请开票的返回数据进行处理
     * */
    R biilingReturnDataPostProccessor( BillingDTO billing,  String respData){

        JSONObject jsonObject;

        if(StringUtils.isNotBlank(respData)) {
            jsonObject = JSON.parseObject(respData);
        }else{
            return R.error("开票失败!");
        }

        int ticketStatus;
        if(jsonObject.containsKey("code") && InvoiceBusiness.SUCCESS.equals(jsonObject.getInteger("code"))){
            ticketStatus = Constant.INVOICERECODESTATUS.STATUS_LOADING;
        }else {
            ticketStatus = Constant.INVOICERECODESTATUS.STATUS_FAIL;
        }

        if(billingRecodeMapper.updateBillingRecodeInfo(billing.getPostTime(),ticketStatus,billing.getSerialNo()) < 1){
            throw new RRException("开票信息更新失败");
        }
        List<String> list = Collections.singletonList(billing.getInvoice().getItems().get(0).getCode());
        if(worderInformationDao.updateMoreVoteCountingStatus(list,ticketStatus) < 1){
            throw  new RRException("工单的开票状态更新失败");
        }

        if(!jsonObject.containsKey("code") || !InvoiceBusiness.SUCCESS.equals(jsonObject.getInteger("code"))){
            return R.error(jsonObject.getString("msg"));
        }

        InvoceResultLog invoceResultLog = new InvoceResultLog();
        invoceResultLog.setOrderNo(billing.getOrder().getOrderNo());
        invoceResultLog.setSerialNo(billing.getSerialNo());
        invoceResultLog.setResultMsg(respData.getBytes());
        invoiceRecodeMapper.insertInvoceResultLog(invoceResultLog);
        return R.ok("申请开票成功");
    }


    /**
     *   记账成功用户已经申请，直接进行开票组装对象
     * */
    public R constituteBillingSucObj(BillingDTO billing,BillingRecodeDTO billingRecord,BillingOrderRecordDTO billingOrderRecord){
        String postTime = new SimpleDateFormat(pattern)
                .format(new Date(System.currentTimeMillis()));
        /** 操作流水号。传入重复的操作流水号则认为是重复操作 */
        billing.setSerialNo(billingRecord.getSerialNo());
        /** 请求发送时间。格式为yyyy-MM-dd HH:mm:ss。 */
        billing.setPostTime(postTime);

        /** 瑞宏开票回调 **/
        Map<String,String> map = new HashMap<String,String>();
        map.put("callbackUrl",TicketConfigs.getCallbackUrl());

        /** 获取invoice信息 */
        InvoiceDTO invoiceDTO = invoiceRecodeMapper.findInvoiceRecodeWithItemsBySerialNo(billingRecord.getSerialNo());
        if(invoiceDTO == null){
            logger.error("获取invoice信息不存在，操作流水号：{}",billingRecord.getSerialNo());
            return R.error();
        }
        invoiceDTO.getItems().get(0).setQuantity(TicketConfigs.getNums());
        invoiceDTO.getItems().get(0).setUom(TicketConfigs.getUnit());
        invoiceDTO.getItems().get(0).setPrice(invoiceDTO.getItems().get(0).getAmount());
        /** 插入发票订单信息 */
        billing.setOrder(billingOrderRecord);
        /** 发票信息 */
        billing.setInvoice(invoiceDTO);
        /** 自定义参数是 */
        billing.setDynamicParams(map);
        /** 返回消息 */
        List<NoticesDTO> notices = new ArrayList<>();
        NoticesDTO notice = new NoticesDTO();
        notice.setType("email");
        notice.setValue(billingOrderRecord.getEmail());
        notices.add(notice);
        billing.setNotices(notices);
        return null;
    }

    /**
     *判断用户申请/记账状态
     * */
    boolean judgeEnableBilling(WorderOrderLogDTO orderLog){
        if(Constant.BOOKKEEPINGSTATUS.SUCCESS.equals(orderLog.getBookkeepingStatus()) && orderLog.isApply()){
            return true;
        }
        return false;
    }

    /** 获取的结果的后置处理  */
    Object doBilling(String requestBody){
        JSONObject jsonObject =  JSONObject.parseObject(requestBody);
        logger.info("接口异步调用返回报文:"+requestBody);
        if(jsonObject!=null && jsonObject.size()>0){
            /** 记录申请开票的日志 */

            if(jsonObject.containsKey("code") && "0".equals(jsonObject.getString("code"))){
                /** 对数据进行处理 */
                try {
                    JSONObject invoice =  jsonObject.getJSONArray("invoices").getJSONObject(0);
                    List<BillingRecodeDTO>  billingRecodes = billingRecodeMapper.getBillingRecordByWorderNo(invoice.getString("orderNo"));
                    if(CollectionUtils.isEmpty(billingRecodes)){
                        return   R.Result("error","发票信息不存在");
                    }
                    BillingRecodeDTO  billingRecodeDTO = billingRecodes.get(0);
                    if(billingRecodeDTO.getChargeNums().intValue() > 0){
                        /** 如果已经记收的不插入日志 */
                        InvoceResultLog invoceResultLog = new InvoceResultLog();
                        invoceResultLog.setOrderNo(invoice.getString("orderNo"));
                        invoceResultLog.setSerialNo(jsonObject.getString("serialNo"));
                        invoceResultLog.setResultMsg(requestBody.getBytes());
                        invoiceRecodeMapper.insertInvoceResultLog(invoceResultLog);
                    }
                    return voteCountingDataProcessor(jsonObject,Constant.INVOICERECODESTATUS.STATUS_SUCCSESS);
                } catch (Exception e) {
                    logger.error("开票失败"+e.getMessage()+"原因:"+e.getCause());
                    return  R.Result("erro",String.format("开票失败异常原因:",e.getCause()));
                }
            }else if(jsonObject.containsKey("code") && ("508".equals(jsonObject.getString("code")) || "6".equals(jsonObject.getString("code")) )){
                return R.Result("error","请重试");
            }else if(jsonObject.containsKey("code") && "507".equals(jsonObject.getString("code"))){
                return R.Result("success","返回信息成功");
            }else{
                /** 对数据进行处理 */
                try {
                    return   voteCountingDataProcessor(jsonObject,Constant.INVOICERECODESTATUS.STATUS_FAIL);
                } catch (Exception e) {
                    return  R.Result("erro",String.format("开票失败异常原因:",e.getCause()));
                }
            }
        }
        return R.Result("success","返回信息成功");
    }

    /**
     * （新）
     * 调用税票云开票结果查询结果后置处理
     * @param resp
     */
    private void newDoBilling(String resp) {
        //准成JSON对象
        JSONObject respJson = JSON.parseObject(resp);
        if (respJson.containsKey("code") && InvoiceBusiness.SUCCESS.equals(respJson.getInteger("code"))) {
            try {
                //获取承载数据
                RespData respData = respJson.containsKey("data") ? JSON.parseObject(respJson.getJSONArray("data").getJSONObject(0).toJSONString(), RespData.class) : null;
                if (respData == null) {
                    log.error("===================开票返回的承载数据为空！！==================");
                    return;
                }
                //获取单据号
                String orderNo = respData.getBillNumber();
                //判断开票是否成功
                String makeInvoiceState = respData.getMakeInvoiceState();
                //开票状态（0 待开 1发送开票 2已开 3 开票失败）
                if (!"2".equals(makeInvoiceState) && !"3".equals(makeInvoiceState)) {
                    log.error("===================单据号：{}=开票失败！！=====================", orderNo);
                    return;
                }
                //开票失败处理
                if("3".equals(makeInvoiceState)){
                    newVoteCountingDataProcessor(respData, Constant.INVOICERECODESTATUS.STATUS_FAIL);
                }
                //查询开票记录
                List<BillingRecodeDTO> billingRecodes = billingRecodeMapper.getBillingRecordByWorderNo(orderNo);
                if (billingRecodes == null || billingRecodes.isEmpty()) {
                    log.error("===================单据号：{}=未查询到开票信息！！==================", orderNo);
                    return;
                }
                BillingRecodeDTO billingRecodeDTO = billingRecodes.get(0);
                if (billingRecodeDTO.getChargeNums() > 0) {
                    //保存日志
                    InvoceResultLog invoceResultLog = new InvoceResultLog();
                    invoceResultLog.setOrderNo(orderNo);
                    invoceResultLog.setSerialNo("");
                    invoceResultLog.setResultMsg(resp.getBytes());
                    invoiceRecodeMapper.insertInvoceResultLog(invoceResultLog);
                }
                newVoteCountingDataProcessor(respData, Constant.INVOICERECODESTATUS.STATUS_SUCCSESS);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("===================开票后置处理异常！==================");
            }
        }else{
            log.error("=======================开票结果查询接口返回失败：{}==============",respJson.getString("msg"));
        }
    }

    /** 获取的结果的后置处理  */
    Object billingCallbackPostProcessor(String requestBody){
        JSONObject jsonObject =  JSONObject.parseObject(requestBody);
        logger.info("接口异步调用返回报文:"+requestBody);
        if(jsonObject!=null && jsonObject.size()>0){
            /** 记录申请开票的日志 */

            BillingRecodeDTO billingRecodeDTO = billingRecodeMapper.findBillingRecordBySerialNo(jsonObject.getString("serialNo"));

            InvoceResultLog invoceResultLog = new InvoceResultLog();
            BillingOrderRecordDTO brd =  billingOrderRecodeMapper.findBillingOrderRecordByserialNo(jsonObject.getString("serialNo"));
            invoceResultLog.setOrderNo(brd==null?"":brd.getOrderNo());
            invoceResultLog.setSerialNo(jsonObject.getString("serialNo"));
            invoceResultLog.setResultMsg(requestBody.getBytes());
            invoiceRecodeMapper.insertInvoceResultLog(invoceResultLog);

            if( billingRecodeDTO.getBillingStatus().intValue() == 4){
                logger.warn("开票失败");
                return  R.Result("error","开票失败");
            }

            if(jsonObject.containsKey("code") && "0".equals(jsonObject.getString("code"))){
                /** 对数据进行处理 */
                try {
                    return voteCountingDataProcessor(jsonObject,Constant.INVOICERECODESTATUS.STATUS_SUCCSESS);
                } catch (Exception e) {
                    logger.error("开票失败"+e.getMessage()+"原因:"+e.getCause());
                }
            }else if(jsonObject.containsKey("code") && ("508".equals(jsonObject.getString("code")) || "6".equals(jsonObject.getString("code")) )){
                return R.Result("error","请重试");
            }else if(jsonObject.containsKey("code") && "507".equals(jsonObject.getString("code"))){
                return R.Result("success","返回信息成功");
            }else{
                /** 对数据进行处理 */
                try {
                    return   voteCountingDataProcessor(jsonObject,Constant.INVOICERECODESTATUS.STATUS_FAIL);
                } catch (Exception e) {
                    logger.error("开票失败"+e.getMessage()+"原因:"+e.getCause());
                }
            }
            return R.Result("success","返回信息成功");
        }
        return R.Result("success","返回信息成功");
    }

    public static void main(String[] args) {
        String str = "{\t\"postTime\":\"2020-09-03 17:14:05\",\t\"code\":\"0\",\t\"invoices\":[\t\t{\t\t\t\"taxpayerAddress\":\"上海市普陀区真北路958号21幢6层\",\t\t\t\"generateTime\":\"2020-08-31 17:18:36\",\t\t\t\"viewUrl\":\"https://www.chinaeinv.com/p.jspa?c=FCD68817380F356BB417\",\t\t\t\"orderNo\":\"SHSSXQ2020-07-200003\",\t\t\t\"customerName\":\"上海三巍建设工程有限公司\",\t\t\t\"checkCode\":\"60083250532820089050\",\t\t\t\"totalAmount\":2610.00,\t\t\t\"taxpayerTel\":\"021-62381939\",\t\t\t\"taxAmount\":215.50,\t\t\t\"items\":[\t\t\t\t{\t\t\t\t\t\"amount\":2610.00,\t\t\t\t\t\"code\":\"SHSSXQ2020-07-200003\",\t\t\t\t\t\"quantity\":1,\t\t\t\t\t\"catalogCode\":\"3050200000000000000\",\t\t\t\t\t\"noTaxAmount\":2394.50,\t\t\t\t\t\"type\":\"0\",\t\t\t\t\t\"zeroTaxRateFlg\":\"\",\t\t\t\t\t\"taxRate\":0.09,\t\t\t\t\t\"uom\":\"EA\",\t\t\t\t\t\"price\":2610.00,\t\t\t\t\t\"name\":\"*建筑服务*安装费\",\t\t\t\t\t\"addedValueTaxFlg\":\"\",\t\t\t\t\t\"taxAmount\":215.50,\t\t\t\t\t\"preferentialPolicyFlg\":\"0\"\t\t\t\t}\t\t\t],\t\t\t\"status\":\"1\",\t\t\t\"pdfUnsignedUrl\":\"https://www.chinaeinv.com/pdfUnsigned.jspa?c=FCD68817380F356BB417\",\t\t\t\"code\":\"03100190051153923446\",\t\t\t\"customerCode\":\"91310115MA1H7T4232\",\t\t\t\"noTaxAmount\":2394.50,\t\t\t\"fiscalCode\":\"FCD68817380F356BB417\",\t\t\t\"taxpayerName\":\"日日顺科技服务（上海）有限公司\",\t\t\t\"drawer\":\"电子发票\",\t\t\t\"taxpayerBankAccount\":\"31050174400000000657\",\t\t\t\"classificationVersion\":\"33.0\",\t\t\t\"taxpayerBankName\":\"中国建设银行股份有限公司上海番禺路支行\",\t\t\t\"taxpayerCode\":\"91310117MA1J31BJ0W\"\t\t}\t],\t\"message\":\"处理成功。\",\t\"serialNo\":\"daadb1b2-cbef-40b7-a89b-edeb4f914e0b\"}";
        String viewUrl = InvoiceUtils.getViewUrl(str);
        System.out.println(viewUrl);
        JSONObject invoice = InvoiceUtils.getInvoice(str);
        System.out.println(invoice.getString("pdfUnsignedUrl"));
    }

    /**
     * 对数据进行处理
     * @param ticketStatus 开票表的订单状态
     *        worderTicketStatus 订单的票据状态
     * */
    public R voteCountingDataProcessor(JSONObject jsonObject,Integer ticketStatus) throws Exception {

        if(jsonObject != null && jsonObject.size() > 0){
            /** 获取发票编号 */
            String serialNo = "";
            if(jsonObject.containsKey("serialNo")&&!"".equals(jsonObject.getString("serialNo"))){
                serialNo = jsonObject.getString("serialNo");
            }else{
                logger.error("返回的发票编号不存在");
                return R.Result("error","请重试");
            }
            /** 通过操作流水号获取订单编号 */
            List<String> list =   invoiceOrderItemsMapper.findInvoices(serialNo);
            JSONObject invoice = InvoiceUtils.getInvoice(jsonObject);
            String viewUrl = invoice.getString("viewUrl");
            String pdfUnsignedUrl = invoice.getString("pdfUnsignedUrl");
            if(worderInformationDao.updateMoreVoteCounting(list,ticketStatus,viewUrl,pdfUnsignedUrl) < 1){
                logger.error("订单状态数据更新异常");
                throw new Exception("开票状态数据更新异常");
            }
            if(billingRecodeMapper.updateBillingRecodeInfo(null, ticketStatus, serialNo) < 1){
                logger.error("开票状态billing_status更新异常");
                throw new Exception("开票状态billing_status更新异常");
            }
            /**  成功则调用acs接口 */
            if(Constant.INVOICERECODESTATUS.STATUS_SUCCSESS.equals(ticketStatus)){
                /** 更新开票数据数据 */
                boolean flag =  jsonObject.containsKey("invoices")&& jsonObject.getJSONArray("invoices").getJSONObject(0).containsKey("code");
                if(!flag){
                    logger.error("返回信息不正确");
                    throw new RuntimeException("返回信息不正确");
                }
                InvoiceRecodeDTO invoiceRecode = new InvoiceRecodeDTO();
                invoiceRecode.setSerialNo(serialNo);
                invoiceRecode.setInvoiceCode(jsonObject.getJSONArray("invoices").getJSONObject(0).getString("code"));
                if(invoiceRecodeMapper.updateInvoiceRecordByRespData(invoiceRecode) < 1){
                    logger.error("开票状态数据更新异常");
                    throw new Exception("开票状态数据更新异常");
                };

                /** 更新开票状态 */
                if(billingRecodeMapper.updateBillingRecodeChargeStatus(1,serialNo) < 1){
                    logger.error("回调开票信息更新失败");
                    throw new RuntimeException("开票信息更新失败");
                }

//                AsyncManager.instance().execute(AsyncFactory.completeBiilingOper(serialNo));
                invoiceReturn(serialNo);
            }

            return R.Result("success","返回信息成功");
        }
        return R.Result("error","请重试");
    }

    /**
     * 针对切换税票云开票结果查询接口，从写后置处理方法
     * @param respData
     * @param ticketStatus
     * @return
     * @throws Exception
     */
    public void newVoteCountingDataProcessor(RespData respData,Integer ticketStatus) throws Exception {

        String orderNo = respData.getBillNumber();
        if (StringUtils.isNotBlank(orderNo)) {
            //根据工单获取流水号
            String serialNo = invoiceOrderItemsMapper.findSerialNoByWorderNo(orderNo);
            //通过操作流水号获取订单编号
            List<String> list = invoiceOrderItemsMapper.findInvoices(serialNo);
            //获取发票下载地址
            String invoiceUrl = respData.getInvoiceUrl();
            //更新地址
            if (worderInformationDao.updateMoreVoteCounting(list, ticketStatus, invoiceUrl, invoiceUrl) < 1) {
                logger.error("==============订单状态数据更新异常===================");
                throw new RRException("开票状态数据更新异常");
            }
            //更新状态
            if (billingRecodeMapper.updateBillingRecodeInfo(null, ticketStatus, serialNo) < 1) {
                logger.error("==============开票状态billing_status更新异常==========");
                throw new RRException("开票状态billing_status更新异常");
            }

            //  成功则调用acs接口
            if (Constant.INVOICERECODESTATUS.STATUS_SUCCSESS.equals(ticketStatus)) {
                // 更新开票数据数据
                InvoiceRecodeDTO invoiceRecode = new InvoiceRecodeDTO();
                invoiceRecode.setSerialNo(serialNo);
                // 发票代码＋发票号码。
                invoiceRecode.setInvoiceCode(respData.getInvoiceCode() + respData.getInvoiceNo());
                if (invoiceRecodeMapper.updateInvoiceRecordByRespData(invoiceRecode) < 1) {
                    logger.error("开票状态数据更新异常");
                    throw new RRException("开票状态数据更新异常");
                }
                // 更新开票状态
                if (billingRecodeMapper.updateBillingRecodeChargeStatus(1, serialNo) < 1) {
                    logger.error("回调开票信息更新失败");
                    throw new RRException("开票信息更新失败");
                }

                //记账记成本
                /** 获取订单信息 */
                logger.info("------------------开票完成后的操作---------------------");
                WorderInfoDTO worderInfoDTO =  worderInformationDao.findWorderInfoBySerialNo(serialNo);
                if(worderInfoDTO==null){
                    logger.error("工单不存在，发票流水：{}",serialNo);
                    throw new RuntimeException(String.format("工单不存在，发票流水:%s",serialNo));
                }
                logger.info("------------------ 调用pushAcsAccountIncre接口 ---------------------");
                worderInformationAccountService.pushAcsAccountIncre(worderInfoDTO.getWorderId());
            }
        }
    }

    public void invoiceReturn(String serialNo){
        /** 获取订单信息 */
        logger.info("------------------开票完成后的操作---------------------");
        WorderInformationDao worderInformationDao =  (WorderInformationDao) SpringUtils.getBean("worderInformationDao");
        WorderInfoDTO worderInfoDTO =  worderInformationDao.findWorderInfoBySerialNo(serialNo);
        if(worderInfoDTO==null){
            logger.error("工单不存在，发票流水：{}",serialNo);
            throw new RuntimeException(String.format("工单不存在，发票流水:%s",serialNo));
        }
        logger.info("------------------ 调用pushAcsAccountIncre接口 ---------------------");
        WorderInformationAccountService worderInformationAccountService =  (WorderInformationAccountService) SpringUtils.getBean("worderInformationAccountService");
        worderInformationAccountService.pushAcsAccountIncre(worderInfoDTO.getWorderId());
    }


    /**
     *对返回的报文进行处理
     * */
    R  dealWithReturnDataPostProccessor( BillingOperRequestDTO billingOperRequest,BillingDTO billing,  String respData){
        JSONObject jsonObject =  null;
        if(StringUtils.isNotBlank(respData)) {
            jsonObject = JSONObject.parseObject(respData);
        }
        int ticketStatus = 0;

        if(jsonObject.containsKey("code")&&"0".equals(jsonObject.getString("code")) || "6".equals(jsonObject.getString("code"))){
            ticketStatus = Constant.INVOICERECODESTATUS.STATUS_LOADING;
        }else {
            ticketStatus = Constant.INVOICERECODESTATUS.STATUS_FAIL;
        }
        if(billingRecodeMapper.updateBillingRecodeInfo(billing.getPostTime(),ticketStatus,billing.getSerialNo()) < 1){
            logger.error("开票信息更新失败");
            throw new RuntimeException("开票信息更新失败");
        }
        /** 更新订单信息 */
        if(!StringUtils.isNullOrEmpty(billing.getOrder().getEmail())){
            if( billingOrderRecodeMapper.updateEmailInfo(billing.getSerialNo(),billing.getOrder().getEmail()) < 1){
                logger.error("订单信息更新失败");
                throw new RuntimeException("订单信息更新失败");
            }
        }
        /** 更新发票记录信息 */
        InvoiceDataUpdateDTO invoiceData = new InvoiceDataUpdateDTO();
        invoiceData.setCustomerName(billing.getInvoice().getCustomerName());
        //角色
        invoiceData.setInvoiceType(billingOperRequest.getRole());
        invoiceData.setCustomerCode(billing.getInvoice().getCustomerCode());
        invoiceData.setInvoiceStatus(0);
        invoiceData.setSerialNo(billing.getSerialNo());

        if(invoiceRecodeMapper.updateInvoiceRecord(invoiceData) < 1){
            throw new RuntimeException("发票记录详情更新失败");
        }

        if(worderInformationDao.updateMoreVoteCountingStatus(Arrays.asList(billing.getInvoice().getItems().get(0).getCode()),ticketStatus) < 1){
            throw  new RuntimeException("工单的开票状态更新失败");
        }
        if(!jsonObject.getString("code").equals(BillingRespStatusEnum.SUCCESS.getStatus())){
            return R.error(BillingRespStatusEnum.getDes(jsonObject.getInteger("code")));
        }

        /** 记录日志 */
        try{
            InvoceResultLog invoceResultLog = new InvoceResultLog();
            invoceResultLog.setOrderNo(billing.getOrder().getOrderNo());
            invoceResultLog.setSerialNo(billing.getSerialNo());
            invoceResultLog.setResultMsg(respData.getBytes());
            invoiceRecodeMapper.insertInvoceResultLog(invoceResultLog);
        }catch (Exception e){
            logger.error("工单 [" + billingOperRequest.getWorderNo() + "] 申请开票（已经记账）返回结果保存失败");
            e.printStackTrace();
        }
        return R.ok("申请开票成功");
    }

    String queryInvoiceInfo(String requestJson){
        try {
            String sign = CertificateUtils.signToBase64(requestJson.getBytes("UTF-8"),TicketConfigs.getCertificatePasswordPath()
                    ,TicketConfigs.getCertificateAlias(), TicketConfigs.getCertificatePassword());
            Map vars = new HashMap();
            vars.put("appCode", URLEncoder.encode(TicketConfigs.getAppCode(), "UTF-8"));
            vars.put("cmdName", URLEncoder.encode(TicketConfigs.getQueryCmdName()   .replace(" ",""), "UTF-8"));
            vars.put("sign", URLEncoder.encode(sign, "UTF-8"));
            return HttpUtils.doPost(TicketConfigs.getBillingUrl(), vars, requestJson, TicketConfigs.getConnectTimeOut(), TicketConfigs.getReadTimeOut());
        } catch (Exception e) {
            logger.error("查询发票失败,异常信息：{}",e);
            throw new RuntimeException("查询发票失败",e);
        }
    }

    /**
     * 待开发票调用接口传输数据
     * Created by cxh on 2017/8/7.
     */
    public String transmitDate(String requestJson) throws Exception {
        logger.info("请求报文：" + requestJson);
        logger.info("配置参数"+JSONObject.toJSONString(TicketConfigs.getCertificatePasswordPath()));

        String sign = CertificateUtils.signToBase64(requestJson.getBytes("UTF-8"),TicketConfigs.getCertificatePasswordPath()
                ,TicketConfigs.getCertificateAlias(), TicketConfigs.getCertificatePassword());

        logger.info("签名字符串：" + sign);
        logger.info("cmdName:"+TicketConfigs.getCmdName());
        Map vars = new HashMap();
        vars.put("appCode", URLEncoder.encode(TicketConfigs.getAppCode(), "UTF-8"));
        vars.put("cmdName", URLEncoder.encode(TicketConfigs.getCmdName().replace(" ",""), "UTF-8"));
        vars.put("sign", URLEncoder.encode(sign, "UTF-8"));
        String responseJson = HttpUtils.doPost(TicketConfigs.getBillingUrl(), vars, requestJson, TicketConfigs.getConnectTimeOut(), TicketConfigs.getReadTimeOut());
        logger.info("响应报文：" + responseJson);
        return responseJson;
    }
    /**
     * 处理开票请求报文
     * */
    R  billingRequestBodyPostProcessor( BillingDTO billingDTO,BillingOperRequestDTO billingOperRequest,BillingRecodeDTO   billingRecord){
        String postTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(System.currentTimeMillis()));
        /** 获取invoice信息 */
        InvoiceRecodeDTO invoiceRecodeDTO = invoiceRecodeMapper.findInvoiceRecodeBySerialNo(billingRecord.getSerialNo());
        if(invoiceRecodeDTO == null){
            logger.error("获取invoice信息不存在，操作流水号：{}",billingRecord.getSerialNo());
            return R.error();
        }
        InvoiceDTO invoice = new InvoiceDTO();

        if("1".equals(String.valueOf(billingOperRequest.getRole()))){
            //个人
            invoice.setCustomerName("个人");
        }else if("2".equals(String.valueOf(billingOperRequest.getRole()))){
            if(StringUtils.isEmpty(billingOperRequest.getCustomerName())){
                R.error("发票抬头不能为空");
            }
            //购货方名称，即发票抬头
            invoice.setCustomerName(billingOperRequest.getCustomerName());
        }else{
            R.error("发票类型参数不正确");
        }
        //购货方纳税人识别号或者个人身份证号
        invoice.setCustomerCode(billingOperRequest.getCustomerCode());
        //开票人
        invoice.setDrawer(invoiceRecodeDTO.getDrawer());
        // 销货方纳税人识别号
        invoice.setTaxpayerCode(invoiceRecodeDTO.getTaxpayerCode());
        //发票总金额
        invoice.setTotalAmount(invoiceRecodeDTO.getInvoiceAmount());
        /** 对象的组装  */
        //	操作流水号。传入重复的操作流水号则认为是重复操作
        billingDTO.setSerialNo(billingRecord.getSerialNo());
        //	请求发送时间。格式为yyyy-MM-dd HH:mm:ss。
        billingDTO.setPostTime(postTime);
        Map<String,String> map = new HashMap<String,String>();
        map.put("callbackUrl",TicketConfigs.getCallbackUrl());
        //自定义参数是
        billingDTO.setDynamicParams(map);
        billingDTO.setInvoice(invoice);
        billingDTO.setOrder(billingRecord.getBillingOrderRecord());
        BillingOrderRecordDTO billingOrderRecord = billingRecord.getBillingOrderRecord();
        /** 返回消息 */
        List<NoticesDTO> notices = new ArrayList<>();
        NoticesDTO notice = new NoticesDTO();
        notice.setType("email");
        notice.setValue(billingOrderRecord.getEmail());
        notices.add(notice);
        billingDTO.setNotices(notices);
        /** 发票记录详情信息 */
        List<ItemsDTO> items = invoiceOrderItemsMapper.findInvoiceOrderItemsListByInvoiceNo(invoiceRecodeDTO.getInvoiceNo());
        if(items == null || items.size() < 1){
            logger.error("发票记录详情信息不存在，操作流水号：{}",billingRecord.getSerialNo());
            return R.error();
        }
        invoice.setItems(items);
        return null;
    }


    /**
     * 支付完成成功后后置处理
     * */
    R payCompleteGenerateInvoiceInfoPostProccessor(String worderNo,Long userId, Boolean update){
        /** 获取订单编号 */
        List<WorderOrderLogDTO> orderLogs =  worderOrderLogMapper.findConstantStatusOrderLogInfo(Constant.PAYRESULTORDERSTATUS.ORDER_PAYMENT_SUCCESS, Arrays.asList(worderNo));
        if(orderLogs == null || orderLogs.size() < 1){
            logger.info("未支付完成,无法生成发票订单");
            return R.error();
        }
        /** 获取订单信息 */
        WorderInfoDTO worderInfoDTO =  worderInformationDao.findWorderInfo(worderNo);
        if(worderInfoDTO == null){
            logger.info("工单信息不存在");
            return R.error();
        }
        /** 判断工单是否已经处理 */
        if(worderInfoDTO.getTicketStatus() == Constant.INVOICERECODESTATUS.STATUS_LOADING || worderInfoDTO.getTicketStatus() == Constant.INVOICERECODESTATUS.STATUS_SUCCSESS){
            logger.info("发票处于正在开票中或者已经完成开票");
            return R.error();
        }
        /** 处理新增发票信息 */
        R result =   billingInfoPostProccessor( worderInfoDTO ,  orderLogs, userId, update);
        return result;
    }
    /**
     * 处理开票记录信息
     * */
    public  R billingInfoPostProccessor(WorderInfoDTO worderInfoDTO , List<WorderOrderLogDTO> orderLogs,Long userId, Boolean update){
        /** 处理开票信息记录信息 */
        BillingRecodeDTO billingRecodeDTO = new BillingRecodeDTO();
        gennerateBillingRecode( billingRecodeDTO);
        billingRecodeDTO.setOperUserId(userId);
        /** 处理订单信息 */
        BillingOrderRecordDTO billingOrderRecordDTO = new BillingOrderRecordDTO();
        generateBillingOrderRecordObj(worderInfoDTO,billingOrderRecordDTO);
        billingRecodeDTO.setBillingOrderRecord(billingOrderRecordDTO);
        /** 发票记录详情信息 */
        InvoiceRecodeDTO invoiceRecode = new InvoiceRecodeDTO();
        generateBillingInvoiceRecord(orderLogs,invoiceRecode,worderInfoDTO);
        /** 操作流水号的处理 */
        billingOrderRecordDTO.setSerialNo(billingRecodeDTO.getSerialNo());
        invoiceRecode.setSerialNo(billingRecodeDTO.getSerialNo());
        billingRecodeDTO.setInvoiceRecode(invoiceRecode);
        /** 开票信息后置处理 */
        R result = null;
        if(update){
            // 更新，需要判断是否已经添加
            result = billingUpdateOrInsertPostProcessor( billingRecodeDTO, billingOrderRecordDTO,worderInfoDTO);
        }else{
            // 直接插入
            result = billingPostProcessor( billingRecodeDTO, billingOrderRecordDTO,worderInfoDTO);
        }
        if(result !=null){
            return result;
        }
        return R.ok("操作成功");
    }

    /**
     * 开票信息后置处理
     * */
    R billingPostProcessor(BillingRecodeDTO billingRecodeDTO,BillingOrderRecordDTO billingOrderRecordDTO,WorderInfoDTO worderInfoDTO){

        /** 开票信息主体信息的插入 */
        if(billingRecodeMapper.insertBillingRecodeInfo(billingRecodeDTO) < 1){
            logger.error(" 开票信息主体信息插入失败");
            return R.error(" 开票信息主体信息插入失败");
        }
        /** 订单信息的插入  */
        if(billingOrderRecodeMapper.inserBillingOrderRecord(billingOrderRecordDTO) < 1){
            logger.error("订单信息插入失败");
            return R.error("订单信息插入失败");
        }
        /** 发票记录详情的新插入 */
        List<InvoiceOrderItemsDTO>  invoiceOrderItems = billingRecodeDTO.getInvoiceRecode().getInvoiceOrderItems();
        if(invoiceOrderItemsMapper.insertInvoiceOrderItems(invoiceOrderItems) < 1){
            logger.error("订单信息插入失败");
            return R.error("订单信息插入失败");
        }
        // billingRecodeDTO.getInvoiceRecode().setInvoiceOrderItems(null);//减少对象大小
        /** 发票记录信息插入 */
        if(invoiceRecodeMapper.insertInvoiceRecode(billingRecodeDTO.getInvoiceRecode()) < 1){
            logger.error(" 发票记录信息插入失败");
            return R.error(" 发票记录体信息插入失败");
        }
        /** 开票状态更新 */
        if(worderInformationDao.updateOpenTicketStatus(worderInfoDTO.getWorderNo(),Constant.INVOICERECODESTATUS.STATUS_LOADING) < 1){
            logger.error(" 发票记录信息插入失败");
            return R.error(" 发票记录体信息插入失败");
        }
        return null;
    }

    /**
     * 开票信息后置处理
     * */
    R billingUpdateOrInsertPostProcessor(BillingRecodeDTO billingRecodeDTO,BillingOrderRecordDTO billingOrderRecordDTO, WorderInfoDTO worderInfoDTO){
        /** 开票信息主体信息的插入 */
        BillingRecodeDTO billingRecord = billingRecodeMapper.findBillingRecordBySerialNo(billingRecodeDTO.getSerialNo());
        if(billingRecord == null){
            if(billingRecodeMapper.insertBillingRecodeInfo(billingRecodeDTO) < 1){
                logger.error(" 开票信息主体信息插入失败");
                return R.error(" 开票信息主体信息插入失败");
            }
        }
        /** 订单信息的插入  */
        BillingOrderRecordDTO billingOrderRecord = billingOrderRecodeMapper.findBillingOrderRecordByserialNo(billingOrderRecordDTO.getSerialNo());
        if(billingOrderRecord == null){
            if(billingOrderRecodeMapper.inserBillingOrderRecord(billingOrderRecordDTO) < 1){
                logger.error("订单信息插入失败");
                return R.error("订单信息插入失败");
            }
        }

        /** 发票记录详情的新插入 */
        List<InvoiceOrderItemsDTO>  invoiceOrderItems = billingRecodeDTO.getInvoiceRecode().getInvoiceOrderItems();
        List<ItemsDTO> invoiceOrderItemsList = invoiceOrderItemsMapper.findInvoiceOrderItemsListByInvoiceNo(invoiceOrderItems.get(0).getInvoiceNo());
        if(invoiceOrderItemsList.size() == 0){
            if(invoiceOrderItemsMapper.insertInvoiceOrderItems(invoiceOrderItems) < 1){
                logger.error("订单信息插入失败");
                return R.error("订单信息插入失败");
            }
        }
        // billingRecodeDTO.getInvoiceRecode().setInvoiceOrderItems(null);//减少对象大小
        /** 发票记录信息插入 */
        InvoiceRecodeDTO invoiceRecode = invoiceRecodeMapper.findInvoiceRecodeBySerialNo(billingRecodeDTO.getSerialNo());
        if(invoiceRecode == null){
            if(invoiceRecodeMapper.insertInvoiceRecode(billingRecodeDTO.getInvoiceRecode()) < 1){
                logger.error(" 发票记录信息插入失败");
                return R.error(" 发票记录体信息插入失败");
            }
        }
        /** 开票状态更新 */
        if(worderInformationDao.updateOpenTicketStatus(worderInfoDTO.getWorderNo(),Constant.INVOICERECODESTATUS.STATUS_LOADING) < 1){
            logger.error(" 发票记录信息插入失败");
            return R.error(" 发票记录体信息插入失败");
        }
        return null;
    }

    /**
     * 生成开票信息记录信息
     *
     */
    void gennerateBillingRecode(BillingRecodeDTO billingRecodeDTO){
        billingRecodeDTO.setSerialNo(UUID.randomUUID().toString());//操作流水号
        billingRecodeDTO.setBillingStatus(Constant.INVOICERECODESTATUS.STATUS_NO);//开票状态 1：未开票
//        Map<String,String> map = new HashMap<String,String>();
//        map.put("callbackUrl",TicketConfigs.getCallbackUrl());
//        billingRecodeDTO.setDynamicParams(JSONObject.toJSONString(map));//回调函数
    }
    /**
     *位数不足后面补0
     * */
    String addZeroForNum(String str, int strLength){
        int strLen = str.length();
        StringBuffer sb = new StringBuffer(str);
        while (sb.length() <strLength){
            sb.append("0");
        }
        return sb.toString();
    }
     /**
     * 发票记录信息处理
     * */
    void generateBillingInvoiceRecord(List<WorderOrderLogDTO> orderLogs, InvoiceRecodeDTO invoiceRecode, WorderInfoDTO worderInfoDTO){

        WorderOrderLogDTO  orderLog =  orderLogs.get(0);
        //开票人
        invoiceRecode.setDrawer(TicketConfigs.getDrawer());
        //发票编号
        invoiceRecode.setInvoiceNo(UUID.randomUUID().toString());
        //销货方纳税人识别号
        invoiceRecode.setTaxpayerCode(TicketConfigs.getTaxpayerCode());
        //支付流水
        invoiceRecode.setPayOrderNo(orderLog.getOrderNo());
        /** 支付方式 1:支付宝 2:微信 */
        invoiceRecode.setPayTypeCode(orderLog.getPayType());
        /** 支付方式 微信 /支付宝 */
        invoiceRecode.setPayType(PayTypeEnum.getDes(orderLog.getPayType()));

        /** 总金额 */
        BigDecimal totalAmount = new BigDecimal(0);
        /** 发票记录信息 */
        List<InvoiceOrderItemsDTO> InvoiceOrderItems = new ArrayList<>();
        String preTemplateId = String.valueOf(worderInfoDTO.getTemplateId());
        String templateId  = addZeroForNum(preTemplateId,19);
        InvoiceOrderItemsDTO invoiceOrderItem = null;
        for(int i=0;i<orderLogs.size();i++){
            invoiceOrderItem = new InvoiceOrderItemsDTO();
            orderLog = orderLogs.get(i);
            //商品编号（工单编号）
            invoiceOrderItem.setOrderItemsCode(orderLog.getWorderNo());
            //商品名称
            invoiceOrderItem.setOrderItemsName(TicketConfigs.getName());
            //商品价格(元)
            invoiceOrderItem.setOrderItemsAmount(orderLog.getPayActualAmount());
            //税率
            invoiceOrderItem.setOrderItemsTaxRate(TicketConfigs.getTaxRate());
            //区域名称
            invoiceOrderItem.setAreaName(orderLog.getPayAreaName());
            //支付方式
            invoiceOrderItem.setPayType(orderLog.getPayType());
            //支付流水
            invoiceOrderItem.setPayOrderNo(orderLog.getOrderNo());
            invoiceOrderItem.setInvoiceNo(invoiceRecode.getInvoiceNo());
            invoiceOrderItem.setOrderItemsCatalogCode(TicketConfigs.getCode());
            totalAmount =  totalAmount.add( orderLog.getPayActualAmount());
            InvoiceOrderItems.add(invoiceOrderItem);
        }
        invoiceRecode.setInvoiceAmount(totalAmount);//发票总金额
        invoiceRecode.setInvoiceOrderItems(InvoiceOrderItems);
    }

    /**
     * 插入工单信息
     * */
    void generateBillingOrderRecordObj(WorderInfoDTO worderInformationEntity, BillingOrderRecordDTO billingOrderRecordDTO){
        billingOrderRecordDTO.setOrderNo(worderInformationEntity.getWorderNo());//工单编号
        billingOrderRecordDTO.setAccount(worderInformationEntity.getUserName());//消费者用户名
        if (StringUtils.isNotBlank(worderInformationEntity.getUserPhone()) && worderInformationEntity.getUserPhone().length()>=11){
            billingOrderRecordDTO.setTel(worderInformationEntity.getUserPhone().substring(0,11));//电话
        }else {
            billingOrderRecordDTO.setTel(worderInformationEntity.getUserPhone());//电话
        }
    }
}
