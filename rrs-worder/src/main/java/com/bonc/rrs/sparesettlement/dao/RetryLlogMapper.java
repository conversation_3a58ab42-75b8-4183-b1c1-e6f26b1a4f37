package com.bonc.rrs.sparesettlement.dao;

/**
 * <AUTHOR>
 * @description
 * @see
 */

import com.bonc.rrs.sparesettlement.model.entity.RetryLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RetryLlogMapper {
     /**
      * 查询重发列表
      * */
     List<RetryLogEntity> findRetryLogList(@Param("retryType") String retryTypem,@Param("operResultCode") String operResultCode);
     /**
      * 新增信息
      * */
     int insertIntoRetryLog(RetryLogEntity retryLogEntity);

     /**
      * 更新发送状态
      * @return
      */
     int updateRetryLog();
}
