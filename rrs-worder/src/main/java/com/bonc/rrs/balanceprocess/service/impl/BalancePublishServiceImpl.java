package com.bonc.rrs.balanceprocess.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.balanceprocess.dao.BalancePublishDao;
import com.bonc.rrs.balanceprocess.dao.WorderChildInformationDao;
import com.bonc.rrs.balanceprocess.entity.*;
import com.bonc.rrs.balanceprocess.service.*;
import com.bonc.rrs.balanceprocess.vo.*;
import com.bonc.rrs.invoice.dot.hcsp.DotHcspService;
import com.bonc.rrs.invoice.enterprises.finance.business.newsDataAccess.req.ReqNewsDataAccess;
import com.bonc.rrs.invoice.enterprises.finance.business.newsDataAccess.req.SettleOrderInfoForHscpsInfo;
import com.bonc.rrs.invoice.enterprises.finance.business.newsDataAccess.resp.RespNewsDataAccess;
import com.bonc.rrs.invoice.enterprises.finance.business.settlementCancel.req.ReqSettlementCancel;
import com.bonc.rrs.invoice.enterprises.finance.business.settlementCancel.resp.RespSettlementCancel;
import com.bonc.rrs.invoice.enterprises.util.FinanceBusiness;
import com.bonc.rrs.worder.dao.DotInformationDao;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.DotInformationEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worderinformationaccount.constant.BalanceProperties;
import com.bonc.rrs.worderinformationaccount.dao.CvpBillDao;
import com.bonc.rrs.worderinformationaccount.dao.WorderInformationAccountDao;
import com.bonc.rrs.worderinformationaccount.dao.WorderPmStimulateDao;
import com.bonc.rrs.worderinformationaccount.dto.PublishDetail;
import com.bonc.rrs.worderinformationaccount.entity.*;
import com.bonc.rrs.worderinformationaccount.service.*;
import com.bonc.rrs.workManager.entity.DotBank;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.validator.Assert;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysDictionaryDetailDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.BrandService;
import com.youngking.renrenwithactiviti.modules.sys.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("balancePublishService")
public class BalancePublishServiceImpl extends ServiceImpl<BalancePublishDao, BalancePublishEntity> implements BalancePublishService {
    @Autowired
    private BalanceFileService balanceFileService;
    @Autowired
    private BalancePublishFileService balancePublishFileService;
    @Autowired
    private SerialNoVersionService serialNoVersionService;
    @Autowired(required = false)
    private WorderInformationAccountDao worderInformationAccountDao;
    @Autowired
    private DotHcspService dotHcspService; //商户通服务
    @Autowired
    private WorderPmStimulateService worderPmStimulateService;
    @Autowired
    private CostInformationService costInformationService;
    @Autowired
    private BalanceCvpRecordService balanceCvpRecordService;
    @Autowired
    private BalanceCvpDetailService balanceCvpDetailService;
    @Autowired
    private CvpBillDetailService cvpBillDetailService;
    @Autowired(required = false)
    private CvpBillDao cvpBillDao;
    @Autowired
    private BalanceProperties balanceProperties;
    @Autowired
    private BalanceAcsAccountingStatusService balanceAcsAccountingStatusService;
    @Autowired(required = false)
    private BalancePublishDao balancePublishDao;
    @Autowired(required = false)
    private WorderPmStimulateDao worderPmStimulateDao;
    @Autowired
    private WorderChildInformationDao worderChildInformationDao;
    @Autowired
    private WorderInformationDao worderInformationDao;
    @Autowired
    private WorderChildInformationService worderChildInformationService;
    @Autowired
    private BrandService brandService;
    @Autowired
    private FinanceBusiness financeBusiness;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private AdvanceMoneyInfoService advanceMoneyInfoService;
    @Autowired
    private CompanyInvoiceService companyInvoiceService;

    @Autowired
    private SysDictionaryDetailDao sysDictionaryDetailDao;

    @Autowired
    private DotInformationDao dotInformationDao;
    /**
     * 提交发布信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitPublish(BalancePublishEntity balancePublishEntity, MultipartFile[] files) throws IOException {
        Date date = new Date();
        //获取登录信息
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        int userId = user.getUserId().intValue();
        /* ------------------第一步：数据校验--------------------*/
        //校验是否选择工单
        List<Integer> worderChildIds = this.idStr2idList(balancePublishEntity.getWorderIds());
        List<Integer> worderIds = new ArrayList<>();
        List<Integer> increIds = this.idStr2idList(balancePublishEntity.getIncreIds());
        List<Integer> stimulateWorderChildIds = this.idStr2idList(balancePublishEntity.getStimulateIds());
        List<Integer> stimulateIds = new ArrayList<>();

        if (CollectionUtils.isEmpty(worderChildIds) && CollectionUtils.isEmpty(increIds)) {
            throw new RRException("没有选择要发布的结算信息");
        }
        //校验总结算金额
        BigDecimal publishFee = BigDecimal.ZERO;
        List<PublishWorder> publishWorderList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(worderChildIds)) {
            List<PublishWorder> list = this.listPublishWorderByIds(1, worderChildIds);
            for (PublishWorder e : list) {
                publishFee = publishFee.add(e.getFee());
                worderIds.add(e.getWorderId());
            }
            publishWorderList.addAll(list);
        }
        if (CollectionUtils.isNotEmpty(increIds)) {
            List<PublishWorder> list = this.listPublishWorderByIds(2, increIds);
            for (PublishWorder e : list) {
                publishFee = publishFee.add(e.getFee());
            }
            publishWorderList.addAll(list);
        }
        if (CollectionUtils.isNotEmpty(stimulateWorderChildIds)) {
            List<PublishWorder> list = this.listPublishWorderByIds(3, stimulateWorderChildIds);
            for (PublishWorder e : list) {
                if (e.getFee() != null) {
                    publishFee = publishFee.add(e.getFee());
                }
                stimulateIds.add(e.getStimulateId());
            }
            publishWorderList.addAll(list);
        }

        if (publishFee.compareTo(balancePublishEntity.getPublishFee()) != 0) {
            throw new RRException("发布的结算总金额有误，应为" + publishFee + ",实为" + balancePublishEntity.getPublishFee());
        }
//        if (publishFee.compareTo(BigDecimal.ZERO) <= 0) {
//            throw new RRException("发布的结算总金额必须大于0");
//        }
        //校验网点和品牌
        for (PublishWorder publishWorder : publishWorderList) {
            if (!publishWorder.getDotId().equals(balancePublishEntity.getDotId())) {
                throw new RRException("发布单的结算网点不一致");
            }
            if (!publishWorder.getBrandId().equals(balancePublishEntity.getBrandId())) {
                throw new RRException("发布单的品牌不一致");
            }
        }

        /* ------------------第二步：数据入库--------------------*/
        if (balancePublishEntity.getId() != null) {
            //重新提交的话，校验用户
            Integer publishId = balancePublishEntity.getId();
            BalancePublishEntity entity = this.getById(publishId);
            if (userId != entity.getCreator().intValue()) {
                throw new RRException("当前登录用户不是发布信息的创建人，不能修改");
            }
            //校验发布信息的状态
            if (entity.getStatus() > 1) {
                throw new RRException("发布信息已提交，不能修改");
            }
            /* -------------------   比对选择的工单是否有变化  --------------------*/
            String oldWorderChildIdstr = entity.getWorderIds();
            String oldStimulateIdStr = entity.getStimulateIds();
            if (StringUtils.isEmpty(oldWorderChildIdstr)) {
                //如果原来没有发布工单结算，直接把新选的工单结算工单发布
                if (CollectionUtils.isNotEmpty(worderIds)) {
                    worderInformationAccountDao.updateWorderChildToPublishByIds(worderChildIds, 5, "网点工单结算已发布");
                    worderInformationAccountDao.update(null,
                            new UpdateWrapper<WorderInformationAccountEntity>()
                                    .in("worder_id", worderIds)
                                    .set("worder_set_status", 5)
                                    .set("worder_set_status_value", "网点工单结算已发布"));
                }
            } else if (!oldWorderChildIdstr.equals(balancePublishEntity.getWorderIds())) {
                //先把原来发布的工单回退，再把新选的工单发布
                List<Integer> oldWorderChildIds = this.idStr2idList(oldWorderChildIdstr);
                List<Integer> oldWorderIds = worderChildInformationDao.selectWorderIdsByWorderChildIds(oldWorderChildIds);
                if (CollectionUtils.isNotEmpty(oldWorderIds)) {
                    worderInformationAccountDao.updateWorderChildToPublishByIds(oldWorderChildIds, 13, "网点工单三次审核通过");
                    worderInformationAccountDao.update(null,
                            new UpdateWrapper<WorderInformationAccountEntity>()
                                    .in("worder_id", oldWorderIds)
                                    .set("worder_set_status", 13)
                                    .set("worder_set_status_value", "网点工单三次审核通过"));
                }
                if (CollectionUtils.isNotEmpty(worderChildIds)) {
                    worderInformationAccountDao.updateWorderChildToPublishByIds(worderChildIds, 5, "网点结算已发布");
                    worderInformationAccountDao.update(null,
                            new UpdateWrapper<WorderInformationAccountEntity>()
                                    .in("worder_id", worderIds)
                                    .set("worder_set_status", 5)
                                    .set("worder_set_status_value", "网点结算已发布"));
                }
            }

            // 判断旧激励单
            if (!oldStimulateIdStr.equals(balancePublishEntity.getStimulateIds())) {
                //先把原来发布的激励单回退，再把新选的激励单发布
                List<Integer> oldStimulateWorderChildIds = this.idStr2idList(oldStimulateIdStr);
                List<Integer> oldStimulateIds = worderChildInformationDao.selectStimulateIdsByWorderChildIds(oldStimulateWorderChildIds);

                if (CollectionUtils.isNotEmpty(oldStimulateIds)) {
                    worderInformationAccountDao.updateWorderChildToPublishByIds(oldStimulateWorderChildIds, 22, "记账成功");
                    worderInformationAccountDao.updateStimulateStatusToAccountByIds(oldStimulateIds);
                }
            }
            // 激励单发布
            if (CollectionUtils.isNotEmpty(stimulateWorderChildIds)) {
                worderInformationAccountDao.updateWorderChildToPublishByIds(stimulateWorderChildIds, 32, "结算发布中");
                worderInformationAccountDao.updateStimulateStatusToPublishByIds(stimulateIds);
            }

            String oldIncreIdStr = entity.getIncreIds();
            if (StringUtils.isEmpty(oldIncreIdStr)) {
                //如果原来没有发布增项结算，直接把新选的增项结算工单发布
                if (CollectionUtils.isNotEmpty(increIds)) {
                    worderInformationAccountDao.update(null,
                            new UpdateWrapper<WorderInformationAccountEntity>()
                                    .in("worder_id", increIds)
                                    .set("worder_incre_status", 2)
                                    .set("worder_incre_status_value", "增项结算已发布"));
                }
            } else if (!oldIncreIdStr.equals(balancePublishEntity.getIncreIds())) {
                //先把原来发布的工单回退，再把新选的工单发布
                List<Integer> oldIncreIds = this.idStr2idList(oldIncreIdStr);

                if (CollectionUtils.isNotEmpty(oldIncreIds)) {
                    worderInformationAccountDao.update(null,
                            new UpdateWrapper<WorderInformationAccountEntity>()
                                    .in("worder_id", oldIncreIds)
                                    .set("worder_incre_status", 1)
                                    .set("worder_incre_status_value", "增项待结算"));
                }
                if (CollectionUtils.isNotEmpty(worderChildIds)) {
                    worderInformationAccountDao.updateWorderChildToPublishByIds(worderChildIds, 2, "增项结算已发布");
                    worderInformationAccountDao.update(null,
                            new UpdateWrapper<WorderInformationAccountEntity>()
                                    .in("worder_id", increIds)
                                    .set("worder_incre_status", 2)
                                    .set("worder_incre_status_value", "增项结算已发布"));
                }

            }

            //更新发布信息
//            BeanUtils.copyProperties(balancePublishEntity, entity);
            entity.setWorderIds(balancePublishEntity.getWorderIds());
            entity.setBrandId(balancePublishEntity.getBrandId());
            entity.setStatus(balancePublishEntity.getStatus());
            entity.setPublishFee(balancePublishEntity.getPublishFee());
            if (balancePublishEntity.getStatus() == 2) {
                entity.setSubmitTime(date);
            }
            this.updateById(entity);
            //处理页面上进行删除的已提交的关联文件
            balanceFileService.deleteFiles(balancePublishEntity.getDelFileIds());
            balancePublishFileService.remove(new QueryWrapper<BalancePublishFileEntity>()
                    .eq("publish_id", publishId)
                    .in("file_id", balancePublishEntity.getDelFileIds()));
            //保存页面上新增的关联文件
            if (files != null && files.length > 0) {
                addFiles(publishId, userId, files);
            }
        } else {
            //首次暂存或提交的话，生成发布单号
            String balancePublishNo = serialNoVersionService.businessNoMaker('f');
            //整理发布实体类对象
            BalancePublishEntity entity = new BalancePublishEntity();
            BeanUtils.copyProperties(balancePublishEntity, entity);
            entity.setCreator(userId);
            entity.setCreatorName(user.getEmployeeName());
            entity.setCreateTime(date);
            entity.setBalancePublishNo(balancePublishNo);
            if (balancePublishEntity.getStatus() == 2) {
                entity.setSubmitTime(date);
            }
            //保存发布信息
            this.save(entity);
            //工单修改发布状态
            if (CollectionUtils.isNotEmpty(increIds)) {
                worderInformationAccountDao.update(null,
                        new UpdateWrapper<WorderInformationAccountEntity>()
                                .in("worder_id", increIds)
                                .set("worder_incre_status", 2)
                                .set("worder_incre_status_value", "增项结算已发布"));
            }

            //工单修改发布状态
            if (CollectionUtils.isNotEmpty(worderChildIds)) {
                worderInformationAccountDao.updateWorderChildToPublishByIds(worderChildIds, 5, "车企结算已发布");

                // 校验工单结算子订单全部审核通过 更新工单结算状态
                for (Integer worderId : worderIds) {
                    if (valiAllWorderChildInfomationAudit(worderId, 5)) {
                        worderInformationAccountDao.update(null,
                                new UpdateWrapper<WorderInformationAccountEntity>()
                                        .in("worder_id", worderIds)
                                        .set("worder_set_status", 5)
                                        .set("worder_set_status_value", "车企结算已发布"));
                    }
                }
            }

            // 网点激励单发布
            if (CollectionUtils.isNotEmpty(stimulateWorderChildIds)) {
                worderInformationAccountDao.updateWorderChildToPublishByIds(stimulateWorderChildIds, 32, "结算发布中");
                worderInformationAccountDao.updateStimulateStatusToPublishByIds(stimulateIds);
            }
            //保存关联文件
            if (files != null && files.length > 0) {
                addFiles(entity.getId(), userId, files);
            }
        }
        updatePmStatus(balancePublishEntity.getBatchNo(),balancePublishEntity.getStatus());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R submitPublish(BalancePublishEntity balancePublishEntity) throws IOException {
        Date date = new Date();
        //获取登录信息
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        int userId = user.getUserId().intValue();
        /* ------------------第一步：数据校验--------------------*/
        //校验是否选择工单
        List<Integer> worderChildIds = this.idStr2idList(balancePublishEntity.getWorderIds());
        List<Integer> worderIds = new ArrayList<>();
        List<Integer> increIds = this.idStr2idList(balancePublishEntity.getIncreIds());
        List<Integer> stimulateWorderChildIds = this.idStr2idList(balancePublishEntity.getStimulateIds());
        List<Integer> stimulateIds = new ArrayList<>();

        // 网点工单费用-新资金结算需校验网点发布费用是否少于每笔新资金最小额度
        if (balancePublishEntity.getPublishType() == 36) {

            AdvanceMoneyInfoEntity advanceMoneyInfo = advanceMoneyInfoService.getAdvanceMoneyInfo();
            if (balancePublishEntity.getPublishFee().compareTo(advanceMoneyInfo.getSurplusLimit()) > 0) {
                return R.error(1, "发布超过新资金池剩余额度");
            }

            List<Map> checkDotList = this.balancePublishDao.checkAdvanceMoneyMinLimit(worderChildIds);
            if (worderChildIds == null || worderChildIds.size() == 0) {
                return R.error("请选择子订单");
            }
            if (!balancePublishEntity.getPayForm().equals("X") && !balancePublishEntity.getPayForm().equals("BSYP")){
                if (StringUtils.isBlank(balancePublishEntity.getPayBank())) {
                    return R.error("网点工单费用-新资金发布需选择收款银行");
                }
                List<Map> checkBankList = this.balancePublishDao.checkBankMoneyMinLimit(worderChildIds);
                if (checkBankList != null && checkBankList.size() > 0) {
                    Set<String> dotSet = new HashSet<>();
                    checkBankList.stream().forEach(item -> {
                        dotSet.add(item.get("dotName") + "");
                    });
                    return R.error(1, "网点发布金额少于银行承兑最小额度").put("dotName", dotSet);
                }
                if (balancePublishEntity.getOutBank().equals("CMB")){
                    //判断选择是招商银行
                    if (!balancePublishEntity.getPayBank().equals("CMB")){
                        return R.error("选择出账银行为招商银行时，收款银行也必须为招商银行");
                    }
                }
                if (balancePublishEntity.getPayBank().equals("MRYH")){
                    Map<String,String> checkDotBank = new HashMap<>();
                    List<Integer> allDotIds = balancePublishDao.getDotByChilds(worderChildIds);
                    //循环网点
                    List<Integer> dotIds = balancePublishDao.checkDotBankFirst(worderChildIds);
                    for (Integer dotId : dotIds) {
                        checkDotBank.put(dotId.toString(),dotId.toString());
                    }
                    if (allDotIds.size()!=checkDotBank.size()){
                        for (Integer dotId : allDotIds) {
                            if (!checkDotBank.containsKey(dotId.toString())){
                                //不存在的网点
                                DotInformationEntity dotInformationEntity = dotInformationDao.selectById(dotId);
                                return R.error(1,dotInformationEntity.getDotName()+"网点未绑定银行信息");
                            }
                        }
                    }
                }else{
                    List<SysDictionaryDetailEntity> sysDictionaryDetailEntity = sysDictionaryDetailDao.getPayBank(balancePublishEntity.getPayBank());
                    String accountName = sysDictionaryDetailEntity.get(0).getDetailName();
                    Map<String,String> checkDotBank = new HashMap<>();
                    List<Integer> allDotIds = balancePublishDao.getDotByChilds(worderChildIds);
                    //循环网点
                    List<Integer> dotIds = balancePublishDao.checkDotBank(worderChildIds,accountName);
                    for (Integer dotId : dotIds) {
                        checkDotBank.put(dotId.toString(),dotId.toString());
                    }
                    if (allDotIds.size()!=checkDotBank.size()){
                        for (Integer dotId : allDotIds) {
                            if (!checkDotBank.containsKey(dotId.toString())){
                                //不存在的网点
                                DotInformationEntity dotInformationEntity = dotInformationDao.selectById(dotId);
                                return R.error(1,dotInformationEntity.getDotName()+"网点不支持"+sysDictionaryDetailEntity.get(0).getDetailName()+"付款银行");
                            }
                        }
                    }
                }
                if (StringUtils.isBlank(balancePublishEntity.getOutBank())||(StringUtils.isNotBlank(balancePublishEntity.getOutBank())&&balancePublishEntity.getOutBank().equals("请选择"))){
                    return R.error("网点工单费用-新资金发布银行承兑需选择出账银行");
                }
            }else {
                if (checkDotList != null && checkDotList.size() > 0) {
                    Set<String> dotSet = new HashSet<>();
                    checkDotList.stream().forEach(item -> {
                        dotSet.add(item.get("dotName") + "");
                    });
                    return R.error(1, "网点发布金额少于每笔新资金最小额度").put("dotName", dotSet);
                }
                balancePublishEntity.setOutBank("");
            }
        } else if (!balancePublishEntity.getPayForm().equals("BSYP")) {
            balancePublishEntity.setOutBank("");
            balancePublishEntity.setPayForm("X");
        }




        Assert.isTrue(CollectionUtils.isEmpty(worderChildIds) || CollectionUtils.isEmpty(increIds), "未选择工单或者选择的工单的结算类型不一致！");
        //校验总结算金额
        BigDecimal publishFee = BigDecimal.ZERO;
        List<PublishWorder> publishWorderList = new ArrayList<>();
        List<Map> dotMap = worderInformationAccountDao.getAllDotList();
        Map<String,Object> dotTaxMap = new HashMap<>();
        for (Map map : dotMap) {
            dotTaxMap.put(getStringValue(map, "dot_id"),getStringValue(map, "dotTaxPoint"));
        }
        if (CollectionUtils.isNotEmpty(worderChildIds)) {
            List<PublishWorder> list = this.listPublishWorderByIds(1, worderChildIds);
            for (PublishWorder e : list) {
                if (e.getFee() != null) {
                    if (e.getStimulateId()==null){
                        //查询网点信息
                        BigDecimal dotTaxRate = new BigDecimal(dotTaxMap.get(e.getDotId().toString()).toString().replaceAll("%", "").trim());
                        BigDecimal dotTax = dotTaxRate.divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP);
                        BigDecimal includedMoney = e.getNoTaxfee().multiply(dotTax.add(new BigDecimal(1))).setScale(2,balanceProperties.ROUND_MODE);
                        BigDecimal Tax = includedMoney.subtract(e.getNoTaxfee());
                        e.setTax(Tax);
                        //重新计算含税价
                        e.setFee(includedMoney);
                    }
                    publishFee = publishFee.add(e.getFee());
                }
                worderIds.add(e.getWorderId());
            }
            publishWorderList.addAll(list);
        }
        if (CollectionUtils.isNotEmpty(increIds)) {
            List<PublishWorder> list = this.listPublishWorderByIds(2, increIds);
            for (PublishWorder e : list) {
                if (e.getFee() != null) {
                    if (e.getStimulateId()==null){
                        //查询网点信息
                        BigDecimal dotTaxRate = new BigDecimal(dotTaxMap.get(e.getDotId().toString()).toString().replaceAll("%", "").trim());

                        BigDecimal dotTax = dotTaxRate.divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP);
                        BigDecimal includedMoney = e.getNoTaxfee().multiply(dotTax.add(new BigDecimal(1))).setScale(2,balanceProperties.ROUND_MODE);
                        BigDecimal Tax = includedMoney.subtract(e.getNoTaxfee());
                        e.setTax(Tax);
                        //重新计算含税价
                        e.setFee(includedMoney);
                    }
                    publishFee = publishFee.add(e.getFee());
                }
            }
            publishWorderList.addAll(list);
        }

        if (CollectionUtils.isNotEmpty(stimulateWorderChildIds)) {
            List<PublishWorder> list = this.listPublishWorderByIds(3, stimulateWorderChildIds);
            for (PublishWorder e : list) {
                if (e.getFee() != null) {
                    if (e.getStimulateId()==null){
                        //查询网点信息
                        BigDecimal dotTaxRate = new BigDecimal(dotTaxMap.get(e.getDotId().toString()).toString().replaceAll("%", "").trim());

                        BigDecimal dotTax = dotTaxRate.divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP);
                        BigDecimal includedMoney = e.getNoTaxfee().multiply(dotTax.add(new BigDecimal(1))).setScale(2,balanceProperties.ROUND_MODE);
                        BigDecimal Tax = includedMoney.subtract(e.getNoTaxfee());
                        e.setTax(Tax);
                        //重新计算含税价
                        e.setFee(includedMoney);
                    }
                    publishFee = publishFee.add(e.getFee());
                }
                stimulateIds.add(e.getStimulateId());
            }
            publishWorderList.addAll(list);
        }
        if (publishFee.compareTo(balancePublishEntity.getPublishFee()) != 0) {
            throw new RRException("发布的结算总金额有误，应为" + publishFee + ",实为" + balancePublishEntity.getPublishFee());
        }
//        if (publishFee.compareTo(BigDecimal.ZERO) <= 0) {
//            throw new RRException("发布的结算总金额必须大于0");
//        }

        //根据品牌分组《k=品牌ID，value=集合对象》
        Map<Integer, List<PublishWorder>> brandGrouyPublishMap = publishWorderList.stream().collect(Collectors.groupingBy(PublishWorder::getBrandId, Collectors.toList()));

        //获取工单和费用Map
        Map<Integer, BigDecimal> worderIdFee = balancePublishEntity.getWorderIdFee();
        //获取网点激励单和费用Map
        Map<Integer, BigDecimal> stimulateIdFee = balancePublishEntity.getStimulateIdFee();


        //生成当前批次号
        String bathNo = serialNoVersionService.businessNoMaker('p');

        Integer publishId = null;

        //遍历分组的品牌
        brandGrouyPublishMap.forEach((brandId, brandList) -> {
            //在按照网点进行分组
            Map<Integer, List<PublishWorder>> dotGrouyPublishMap = brandList.stream().collect(Collectors.groupingBy(PublishWorder::getDotId, Collectors.toList()));
            //遍历网点分组集合
            dotGrouyPublishMap.forEach((dotId, dotList) -> {
                Map<String,List<Integer>> worderIdMap = new HashMap<>();
                Map<String,List<Integer>> increIdMap = new HashMap<>();
                Map<String,List<Integer>> stimulateIdMap = new HashMap<>();
                //获取结算工单的id
                List<Integer> worderIdList = dotList.stream().map(PublishWorder::getWorderChildId).filter(worderChildIds::contains).collect(Collectors.toList());
                //获取增项结算的工单id
                List<Integer> increIdList = dotList.stream().map(PublishWorder::getWorderId).filter(increIds::contains).collect(Collectors.toList());
                //获取网点激励结算单id
                List<Integer> stimulateIdList = dotList.stream().map(PublishWorder::getWorderChildId).filter(stimulateWorderChildIds::contains).collect(Collectors.toList());

                if (worderIdList.size() > 0) {
                    for (Integer worderId : worderIdList) {
                        CostInformationEntity costInformationEntity = costInformationService.getCostByWorderId(worderId);
                        if (worderIdMap.containsKey(costInformationEntity.getYwms())){
                            List<Integer> idList = worderIdMap.get(costInformationEntity.getYwms());
                            idList.add(costInformationEntity.getBalanceId());
                            worderIdMap.put(costInformationEntity.getYwms(),idList);
                        }else{
                            List<Integer> idList = new ArrayList<>();
                            idList.add(costInformationEntity.getBalanceId());
                            worderIdMap.put(costInformationEntity.getYwms(),idList);
                        }
                    }
                }
                if (increIdList.size() > 0) {
                    for (Integer increId : increIdList) {
                        CostInformationEntity costInformationEntity = costInformationService.getCostByIncreId(increId);
                        if (increIdMap.containsKey(costInformationEntity.getYwms())){
                            List<Integer> idList = increIdMap.get(costInformationEntity.getYwms());
                            idList.add(costInformationEntity.getIncreId());
                            increIdMap.put(costInformationEntity.getYwms(),idList);
                        }else{
                            List<Integer> idList = new ArrayList<>();
                            idList.add(costInformationEntity.getIncreId());
                            increIdMap.put(costInformationEntity.getYwms(),idList);
                        }
                    }
                }
                if (stimulateIdList.size() > 0) {
                    for (Integer stimulateId : stimulateIdList) {
                        CostInformationEntity costInformationEntity = costInformationService.getCostByStimulateId(stimulateId);
                        if (stimulateIdMap.containsKey(costInformationEntity.getYwms())){
                            List<Integer> idList = stimulateIdMap.get(costInformationEntity.getYwms());
                            idList.add(costInformationEntity.getBalanceId());
                            stimulateIdMap.put(costInformationEntity.getYwms(),idList);
                        }else{
                            List<Integer> idList = new ArrayList<>();
                            idList.add(costInformationEntity.getBalanceId());
                            stimulateIdMap.put(costInformationEntity.getYwms(),idList);
                        }
                    }
                }
                //工单
                for (Map.Entry<String, List<Integer>> entry : worderIdMap.entrySet()) {
                    //首次暂存或提交的话，生成发布单号
                    String balancePublishNo = serialNoVersionService.businessNoMaker('f');
                    BigDecimal sum = new BigDecimal(0);
                    //整理发布实体类对象
                    BalancePublishEntity entity = new BalancePublishEntity();
                    BeanUtils.copyProperties(balancePublishEntity, entity);

                    entity.setWorderIds(entry.getValue().stream().map(Object::toString).collect(Collectors.joining(",")));
                    entity.setIncreIds("");
                    entity.setStimulateIds("");

                    String dotTaxRate = dotTaxMap.get(dotId.toString()).toString();
                    //获取发布金额
                    for (Integer id : entry.getValue()) {
                        if (null != id){
                            sum = sum.add(worderIdFee.get(id));
                        }
                    }
//                    for (PublishWorder publishWorder : dotList) {
//                        if (null != publishWorder.getStimulateId()){
//                            sum = sum.add(stimulateIdFee.get(publishWorder.getWorderChildId()));
//                        } else if (null != publishWorder.getWorderChildId()) {
//                            sum = sum.add(worderIdFee.get(publishWorder.getWorderChildId()));
//                        } else {
//                            sum = sum.add(worderIdFee.get(publishWorder.getWorderId()));
//                        }
//                    }
                    entity.setCreator(userId);
                    entity.setBatchNo(bathNo);
                    entity.setBrandId(brandId);
                    entity.setDotId(dotId);
                    entity.setPublishFee(sum);
                    entity.setDotTaxRate(dotTaxRate);
                    entity.setCreatorName(user.getEmployeeName());
                    entity.setCreateTime(date);
                    entity.setBalancePublishNo(balancePublishNo);
                    if (balancePublishEntity.getStatus() == 2) {
                        entity.setSubmitTime(date);
                    }
                    //保存发布信息
                    this.save(entity);

                    // 子订单表保存发布单id
                    if (entry.getValue() != null && entry.getValue().size() > 0) {
                        worderChildInformationService.update(new UpdateWrapper<WorderChildInformationEntity>()
                                .in("id", entry.getValue())
                                .set("publish_id", entity.getId()));
                    }
                    //修改激励单状态
                    if(CollectionUtils.isNotEmpty(stimulateIdList)){
                        stimulateIdList.forEach(stiId->{
                            WorderPmStimulateEntity worderPmStimulateEntity = new WorderPmStimulateEntity();
                            worderPmStimulateEntity.setPublishStatus(balancePublishEntity.getStatus());
                            worderPmStimulateEntity.setId(stiId);
                            worderPmStimulateDao.updateById(worderPmStimulateEntity);
                        });
                    }
                    //保存关联文件
                    List<BalancePublishFileEntity> balancePublishFiles = new ArrayList<>();
                    List<Integer> fileIds = balancePublishEntity.getFileIds();
                    fileIds.forEach(fileId -> {
                        BalancePublishFileEntity balancePublishFileEntity = new BalancePublishFileEntity();
                        balancePublishFileEntity.setFileId(fileId);
                        balancePublishFileEntity.setPublishId(entity.getId());
                        balancePublishFiles.add(balancePublishFileEntity);
                    });
                    balancePublishFileService.saveBatch(balancePublishFiles);
                }
                //增项
                for (Map.Entry<String, List<Integer>> entry : increIdMap.entrySet()) {
                    //首次暂存或提交的话，生成发布单号
                    String balancePublishNo = serialNoVersionService.businessNoMaker('f');
                    BigDecimal sum = new BigDecimal(0);
                    //整理发布实体类对象
                    BalancePublishEntity entity = new BalancePublishEntity();
                    BeanUtils.copyProperties(balancePublishEntity, entity);

                    entity.setWorderIds("");
                    entity.setIncreIds(entry.getValue().stream().map(Object::toString).collect(Collectors.joining(",")));
                    entity.setStimulateIds("");

                    String dotTaxRate = dotTaxMap.get(dotId.toString()).toString();
                    //获取发布金额
                    for (Integer id : entry.getValue()) {
                        if (null != id){
                            sum = sum.add(worderIdFee.get(id));
                        }
                    }
//                    for (PublishWorder publishWorder : dotList) {
//                        if (null != publishWorder.getStimulateId()){
//                            sum = sum.add(stimulateIdFee.get(publishWorder.getWorderChildId()));
//                        } else if (null != publishWorder.getWorderChildId()) {
//                            sum = sum.add(worderIdFee.get(publishWorder.getWorderChildId()));
//                        } else {
//                            sum = sum.add(worderIdFee.get(publishWorder.getWorderId()));
//                        }
//                    }
                    entity.setCreator(userId);
                    entity.setBatchNo(bathNo);
                    entity.setBrandId(brandId);
                    entity.setDotId(dotId);
                    entity.setPublishFee(sum);
                    entity.setDotTaxRate(dotTaxRate);
                    entity.setCreatorName(user.getEmployeeName());
                    entity.setCreateTime(date);
                    entity.setBalancePublishNo(balancePublishNo);
                    if (balancePublishEntity.getStatus() == 2) {
                        entity.setSubmitTime(date);
                    }
                    //保存发布信息
                    this.save(entity);
                    //保存关联文件
                    List<BalancePublishFileEntity> balancePublishFiles = new ArrayList<>();
                    List<Integer> fileIds = balancePublishEntity.getFileIds();
                    fileIds.forEach(fileId -> {
                        BalancePublishFileEntity balancePublishFileEntity = new BalancePublishFileEntity();
                        balancePublishFileEntity.setFileId(fileId);
                        balancePublishFileEntity.setPublishId(entity.getId());
                        balancePublishFiles.add(balancePublishFileEntity);
                    });
                    balancePublishFileService.saveBatch(balancePublishFiles);
                }
                //激励
                for (Map.Entry<String, List<Integer>> entry : stimulateIdMap.entrySet()) {
                    //首次暂存或提交的话，生成发布单号
                    String balancePublishNo = serialNoVersionService.businessNoMaker('f');
                    BigDecimal sum = new BigDecimal(0);
                    //整理发布实体类对象
                    BalancePublishEntity entity = new BalancePublishEntity();
                    BeanUtils.copyProperties(balancePublishEntity, entity);

                    entity.setWorderIds("");
                    entity.setIncreIds("");
                    entity.setStimulateIds(entry.getValue().stream().map(Object::toString).collect(Collectors.joining(",")));

                    String dotTaxRate = dotTaxMap.get(dotId.toString()).toString();
                    //获取发布金额
                    for (Integer id : entry.getValue()) {
                        if (null != id){
                            sum = sum.add(stimulateIdFee.get(id));
                        }
                    }
//                    for (PublishWorder publishWorder : dotList) {
//                        if (null != publishWorder.getStimulateId()){
//                            sum = sum.add(stimulateIdFee.get(publishWorder.getWorderChildId()));
//                        } else if (null != publishWorder.getWorderChildId()) {
//                            sum = sum.add(worderIdFee.get(publishWorder.getWorderChildId()));
//                        } else {
//                            sum = sum.add(worderIdFee.get(publishWorder.getWorderId()));
//                        }
//                    }
                    entity.setCreator(userId);
                    entity.setBatchNo(bathNo);
                    entity.setBrandId(brandId);
                    entity.setDotId(dotId);
                    entity.setPublishFee(sum);
                    entity.setDotTaxRate(dotTaxRate);
                    entity.setCreatorName(user.getEmployeeName());
                    entity.setCreateTime(date);
                    entity.setBalancePublishNo(balancePublishNo);
                    if (balancePublishEntity.getStatus() == 2) {
                        entity.setSubmitTime(date);
                    }
                    //保存发布信息
                    this.save(entity);

                    // 子订单表保存发布单id
                    if (entry.getValue() != null && entry.getValue().size() > 0) {
                        worderChildInformationService.update(new UpdateWrapper<WorderChildInformationEntity>()
                                .in("id", entry.getValue())
                                .set("publish_id", entity.getId()));
                    }
                    //修改激励单状态
                    if(CollectionUtils.isNotEmpty(stimulateIdList)){
                        stimulateIdList.forEach(stiId->{
                            WorderPmStimulateEntity worderPmStimulateEntity = new WorderPmStimulateEntity();
                            worderPmStimulateEntity.setPublishStatus(balancePublishEntity.getStatus());
                            worderPmStimulateEntity.setId(stiId);
                            worderPmStimulateDao.updateById(worderPmStimulateEntity);
                        });
                    }
                    //保存关联文件
                    List<BalancePublishFileEntity> balancePublishFiles = new ArrayList<>();
                    List<Integer> fileIds = balancePublishEntity.getFileIds();
                    fileIds.forEach(fileId -> {
                        BalancePublishFileEntity balancePublishFileEntity = new BalancePublishFileEntity();
                        balancePublishFileEntity.setFileId(fileId);
                        balancePublishFileEntity.setPublishId(entity.getId());
                        balancePublishFiles.add(balancePublishFileEntity);
                    });
                    balancePublishFileService.saveBatch(balancePublishFiles);
                }
            });
        });
        //如果是导入发布，保存批次号和导入发布的文件
        if (StringUtils.isNotBlank(balancePublishEntity.getImportFileUrl())) {
            this.baseMapper.saveBatchFile(bathNo,balancePublishEntity.getImportFileUrl());
        }

        if (CollectionUtils.isNotEmpty(increIds)) {
            worderInformationAccountDao.update(null,
                    new UpdateWrapper<WorderInformationAccountEntity>()
                            .in("worder_id", increIds)
                            .set("worder_incre_status", 32)
                            .set("worder_incre_status_value", "增项结算发布中"));
        }

        //工单修改发布状态
        if (CollectionUtils.isNotEmpty(worderChildIds)) {
            worderInformationAccountDao.updateWorderChildToPublishByIds(worderChildIds, 32, "车企结算发布中");

            // 校验工单结算子订单全部审核通过 更新工单结算状态
            for (Integer worderId : worderIds) {
                if (valiAllWorderChildInfomationAudit(worderId, 32)) {
                    worderInformationAccountDao.update(null,
                            new UpdateWrapper<WorderInformationAccountEntity>()
                                    .in("worder_id", worderId)
                                    .set("worder_set_status", 32)
                                    .set("worder_set_status_value", "车企结算发布中"));
                }
            }
        }

        // 网点激励单发布
        if (CollectionUtils.isNotEmpty(stimulateWorderChildIds)) {
            worderInformationAccountDao.updateWorderChildToPublishByIds(stimulateWorderChildIds, 32, "结算发布中");
            worderInformationAccountDao.updateStimulateStatusToPublishByIds(stimulateIds);
        }
        updatePmStatus(bathNo,balancePublishEntity.getStatus());

        // 网点工单费用-新资金发布更新新资金池
        if (balancePublishEntity.getPublishType() == 36) {
            // 新资金池处理 - 预占
            advanceMoneyInfoService.preemptMoney(balancePublishEntity.getPublishFee());
            // 新资金开票单状态改为10(发布中)
            companyInvoiceService.update(null,
                    new UpdateWrapper<CompanyInvoiceEntity>()
                            .eq("company_invoice_no", balancePublishEntity.getCompanyInvoiceNo())
                            .set("status", 10));
        }

        return R.ok();
    }

    /**
     * 校验所有工单结算子订单都已审核
     * @param worderId
     * @param balanceSetStatus
     * @return
     */
    private Boolean valiAllWorderChildInfomationAudit (Integer worderId, Integer balanceSetStatus) {
        List<WorderChildInformationEntity> worderChildInformationEntityList = worderChildInformationDao.selectList(new QueryWrapper<WorderChildInformationEntity>()
                .eq("worder_id", worderId)
                .eq("is_delete", 0));
        // 获取状态未审核结算子订单
        List<Integer> worderChildIds = worderChildInformationEntityList.stream().filter(item -> !balanceSetStatus.equals(item.getBalanceSetStatus()))
                .map(WorderChildInformationEntity::getId)
                .collect(Collectors.toList());
        // 没有未审核结算子订单 验证通过 返回true
        return worderChildIds.size() == 0;
    }

    /**
     * 查询所有新资金发布单号
     * @return
     */
    @Override
    public List<String> queryAdvanceCompanyNo () {
        return balancePublishDao.queryAdvanceCompanyNo();
    }

    /**
     * 查询所有付款方式
     * @return
     */
    @Override
    public List<Map<String, String>> queryPayFormList () {
        return balancePublishDao.queryPayFormList();
    }

    /**
     * 查询所有付款银行
     * @return
     */
    @Override
    public List<Map<String, String>> queryPayBankList () {
        return balancePublishDao.queryPayBankList();
    }

    /**
     * 查询所有出账银行
     * @return
     */
    @Override
    public List<Map<String, String>> queryOutBankList () {
        return balancePublishDao.queryOutBankList();
    }

    @Override
    @Transactional
    public void commitPublish(BalancePublishEntity balancePublishEntity) {
        Optional.ofNullable(balancePublishEntity.getBatchNo())
                .orElseThrow(() -> new RRException("批次号为空"));
        //暂存时提交，更新状态
        this.update(new UpdateWrapper<BalancePublishEntity>()
                .set("status", balancePublishEntity.getStatus())
                .set("batch_status", balancePublishEntity.getBatchStatus())
                .set("submit_time", new Date())
                .eq("batch_no", balancePublishEntity.getBatchNo()));
        updatePmStatus(balancePublishEntity.getBatchNo(),balancePublishEntity.getStatus());
    }
    public void  updatePmStatus(String BatchNo,Integer status){
        //激励表更新发布状态
        QueryWrapper<BalancePublishEntity> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.in("batch_no",BatchNo);
        List<BalancePublishEntity> balancePublishEntities = balancePublishDao.selectList(objectQueryWrapper);
        //通过激励Id修改激励发布状态
        if(balancePublishEntities.size()>0){
            for (BalancePublishEntity balancePublish:balancePublishEntities){
                if(StringUtils.isNotBlank(balancePublish.getStimulateIds())){
                    String[] as = balancePublish.getStimulateIds().split(",");
                    for (String a:as){
                        WorderChildInformationEntity worderChildInformationEntity = worderChildInformationDao.selectById(Integer.valueOf(a));

                        if (worderChildInformationEntity == null) {
                            continue;
                        }
                        if(status!=null){
                            worderChildInformationDao.updateChildInfoStatus(worderChildInformationEntity.getId(), status, null);
                            worderPmStimulateDao.updatePmStatus(worderChildInformationEntity.getStimulateId(),status);
                        }else{
                            worderChildInformationDao.updateChildInfoStatus(worderChildInformationEntity.getId(), 22, "激励记账已完成");
                            worderPmStimulateDao.updatePublishStatus(worderChildInformationEntity.getStimulateId());
                        }
                    }
                }
            }
        }
    }
    @Override
    public List<PublishCompanyExcelProperty> selectCompanyWorders(BalancePublishEntity balancePublishEntity) {
        return this.baseMapper.selectCompanyWorders(balancePublishEntity.getBrandId(),balancePublishEntity.getDotId(),balancePublishEntity.getReceivableStartTime(),balancePublishEntity.getReceivableEndTime());
    }

    @Override
    public List<PublishCompanyExcelProperty> queryStimulateInfoAduitList(BalancePublishEntity balancePublishEntity) {
        return this.baseMapper.queryStimulateInfoAduitList(balancePublishEntity.getBrandId(),balancePublishEntity.getDotId(),balancePublishEntity.getReceivableStartTime(),balancePublishEntity.getReceivableEndTime());
    }

    @Override
    public List<PublishCompanyExcelProperty> queryBalanceAdwanceMoneyWorderInfoAduitList(BalancePublishEntity balancePublishEntity) {
        return this.baseMapper.queryBalanceAdwanceMoneyWorderInfoAduitList(balancePublishEntity.getBrandId(),balancePublishEntity.getDotId(), balancePublishEntity.getCompanyInvoiceNo());
    }

    @Override
    public List<PublishCompanyExcelProperty> queryBalanceAdwanceMoneyWorderInfoExport(BalancePublishEntity balancePublishEntity) {
        return this.baseMapper.queryBalanceAdwanceMoneyWorderInfoExport(balancePublishEntity.getBrandId(),balancePublishEntity.getDotId(), balancePublishEntity.getCompanyInvoiceNo(),balancePublishEntity.getReceivableStartTime(),balancePublishEntity.getReceivableEndTime());
    }

    @Override
    public List<PublishIncreExcelProperty> selectPublishIncreWorders(BalancePublishEntity balancePublishEntity) {
        return this.baseMapper.selectPublishIncreWorders(balancePublishEntity.getBrandId(), balancePublishEntity.getDotId(),balancePublishEntity.getReceivableStartTime(),balancePublishEntity.getReceivableEndTime());
    }

    @Override
    public List<PublishMaterialExcelProperty> selectCompanyMaterial(List<Integer> worderIds) {
        return this.baseMapper.selectCompanyMaterial(worderIds);
    }

    @Override
    public List<BalancePublishExcelProperty> selectPublishByBatchNo(String batchNo) {
        return this.baseMapper.selectPublishByBatchNo(batchNo);
    }


    /**
     * 查询发布详情信息
     */
    @Override
    public BalancePublishVO getPublishDetail(Integer publishId) {
        //查询发布信息
        BalancePublishEntity balancePublishEntity = this.baseMapper.selectById(publishId);
        //查询发布关联的工单信息
        List<PublishWorder> worderList = new ArrayList<>();
        List<Integer> worderIds = this.idStr2idList(balancePublishEntity.getWorderIds());
        if (CollectionUtils.isNotEmpty(worderIds)) {
            worderList.addAll(this.listPublishWorderByIds(1, worderIds));
        }
        List<Integer> increIds = this.idStr2idList(balancePublishEntity.getIncreIds());
        if (CollectionUtils.isNotEmpty(increIds)) {
            worderList.addAll(this.listPublishWorderByIds(2, increIds));
        }
        //发布信息和工单信息填充入视图对象
        BalancePublishVO balancePublishVO = new BalancePublishVO();
        BeanUtils.copyProperties(balancePublishEntity, balancePublishVO);
        balancePublishVO.setWorderList(worderList);
        //关联的文件信息填充入视图对象
        List<BalancePublishFileEntity> relationList = balancePublishFileService.list(
                new QueryWrapper<BalancePublishFileEntity>().eq("publish_id", publishId));    //查询发布与文件的关联关系
        if (CollectionUtils.isNotEmpty(relationList)) {
            List<Integer> fileIds = relationList.stream().map(BalancePublishFileEntity::getFileId).collect(Collectors.toList()); //获取文件ID
            List<BalanceFileEntity> fileList = (List<BalanceFileEntity>) balanceFileService.listByIds(fileIds);//查询文件
            balancePublishVO.setFileList(fileList);
        } else {
            balancePublishVO.setFileList(new ArrayList<>());
        }
        return balancePublishVO;
    }

    /**
     * 撤销发布
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePublish(Integer publishId) {
        //校验，只有暂存或审批未通过的发布才能撤销
        BalancePublishEntity balancePublishEntity = this.baseMapper.selectById(publishId);
        if (balancePublishEntity.getStatus() > 1) {
            throw new RRException("发布信息已提交，不能撤销");
        }
        /* --------------删除发布关联的文件------------*/
        //查询发布与文件的关联关系
        List<BalancePublishFileEntity> relationList = balancePublishFileService.list(
                new QueryWrapper<BalancePublishFileEntity>().eq("publish_id", publishId));
        if (CollectionUtils.isNotEmpty(relationList)) {
            //获取文件ID，并删除文件
            List<Integer> fileIds = relationList.stream().map(BalancePublishFileEntity::getFileId).collect(Collectors.toList());
            balanceFileService.deleteFiles(fileIds);
            //删除关联关系
            List<Integer> relationIds = relationList.stream().map(BalancePublishFileEntity::getId).collect(Collectors.toList());
            balancePublishFileService.removeByIds(relationIds);
        }
        /* --------------发布工单状态回退------------*/
        List<Integer> worderIds = this.idStr2idList(balancePublishEntity.getWorderIds());
        if (CollectionUtils.isNotEmpty(worderIds)) {
            worderInformationAccountDao.update(null,
                    new UpdateWrapper<WorderInformationAccountEntity>()
                            .in("worder_id", worderIds)
                            .set("worder_set_status", 13)
                            .set("worder_set_status_value", "工单发布审核通过"));
        }
        List<Integer> increIds = this.idStr2idList(balancePublishEntity.getIncreIds());
        if (CollectionUtils.isNotEmpty(increIds)) {
            worderInformationAccountDao.update(null,
                    new UpdateWrapper<WorderInformationAccountEntity>()
                            .in("worder_id", increIds)
                            .set("worder_incre_status", 1)
                            .set("worder_incre_status_value", "增项待结算"));
        }
        /* --------------删除发布信息------------*/
        this.baseMapper.deleteById(publishId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePublishByBathNo(String batchNo) {
        //校验，只有暂存或审批未通过的发布才能撤销
        List<BalancePublishEntity> balancePublishs = this.baseMapper.selectList(new QueryWrapper<BalancePublishEntity>().eq("batch_no", batchNo));
        long count = balancePublishs.stream().filter(balancePublishEntity -> balancePublishEntity.getStatus() > 1).count();
        if (count > 0) {
            throw new RRException("发布信息已提交，不能撤销");
        }
        //清空发布表激励状态  激励单状态记账成功
        updatePmStatus(batchNo,null);
        /* --------------删除发布关联的文件------------*/

        BigDecimal backPreemptMoney = BigDecimal.ZERO;
        for (BalancePublishEntity balancePublishEntity : balancePublishs) {
            //查询发布与文件的关联关系
            List<BalancePublishFileEntity> relationList = balancePublishFileService.list(
                    new QueryWrapper<BalancePublishFileEntity>().eq("publish_id", balancePublishEntity.getId()));

            if (CollectionUtils.isNotEmpty(relationList)) {
                //获取文件ID，并删除文件
                List<Integer> fileIds = relationList.stream().map(BalancePublishFileEntity::getFileId).collect(Collectors.toList());
                balanceFileService.deleteFiles(fileIds);
                //删除关联关系
                List<Integer> relationIds = relationList.stream().map(BalancePublishFileEntity::getId).collect(Collectors.toList());
                balancePublishFileService.removeByIds(relationIds);
            }
            /* --------------发布工单状态回退------------*/
            List<Integer> worderIds = this.idStr2idList(balancePublishEntity.getWorderIds());
//            if (CollectionUtils.isNotEmpty(worderIds)) {
//                worderInformationAccountDao.update(null,
//                        new UpdateWrapper<WorderInformationAccountEntity>()
//                                .in("worder_id", worderIds)
//                                .set("worder_set_status", 13)
//                                .set("worder_set_status_value", "网点工单三次审核通过"));
//            }
            List<Integer> increIds = this.idStr2idList(balancePublishEntity.getIncreIds());
            if (CollectionUtils.isNotEmpty(increIds)) {
                worderInformationAccountDao.update(null,
                        new UpdateWrapper<WorderInformationAccountEntity>()
                                .in("worder_id", increIds)
                                .set("worder_incre_status", 1)
                                .set("worder_incre_status_value", "增项待结算"));
            }
            if (StringUtils.isNotEmpty(balancePublishEntity.getWorderIds())) {
                balancePublishDao.deletePublishUpdateWorderChildInformation(worderIds);
//                worderChildInformationService.update(null,
//                        new UpdateWrapper<WorderChildInformationEntity>()
//                                .in("id", worderIds)
//                                .eq("is_delete", 0)
//                                .set("balance_set_status", 4)
//                                .set("balance_set_status_value", "车企已回款"));
            }
            if (balancePublishEntity.getPublishType() == 36) {
                // 新资金发布单里的子订单只能在同一个开票单
                // 新资金发布撤销修改开票单状态为7(完成)
                Integer invoiceId = balancePublishDao.queryInvoiceIdByWorderChildId(worderIds.get(0));
                if (invoiceId != null) {
                    companyInvoiceService.update(null,
                            new UpdateWrapper<CompanyInvoiceEntity>()
                                    .eq("id", invoiceId)
                                    .set("status", 7));
                }

                backPreemptMoney = backPreemptMoney.add(balancePublishEntity.getPublishFee());
            }
            /* --------------删除发布信息------------*/
            this.baseMapper.deleteById(balancePublishEntity.getId());
        }

        // 新资金发布撤销预占金额
        if (backPreemptMoney.compareTo(BigDecimal.ZERO) > 0) {
            advanceMoneyInfoService.backPreemptMoney(backPreemptMoney);
        }
    }

    @Override
    public RespSettlementCancel settlementCancel(String billNo, String reason){
        ReqSettlementCancel reqSettlementCancel = new ReqSettlementCancel();
        reqSettlementCancel.setBillNo(billNo);
        reqSettlementCancel.setReason(reason);
        JSONObject respJson = financeBusiness.settlementCancel(reqSettlementCancel);
        RespSettlementCancel resp = new RespSettlementCancel();
        if (respJson != null) {
            resp = respJson.toJavaObject(RespSettlementCancel.class);
        } else {
            resp.setCode("500");
            resp.setMsg("调用结算单作废接口失败");
        }
        return resp;
    }

    /**
     * 分页查询发布信息列表
     */
    @Override
    public PageUtils pageBalancePublish(BalancePublishQuery query) {
        //获取登录信息
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        int userId = user.getUserId().intValue();
        query.setUserId(userId);
        Page<BalancePublishEntity> paget = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<BalancePublishEntity> page = balancePublishDao.queryPublishGroupBatch(paget, query);
//        for (BalancePublishEntity record : page.getRecords()) {
//            List<BalancePublishEntity> pubishList = balancePublishDao.selectList(new QueryWrapper<BalancePublishEntity>().eq("batch_no",record.getBatchNo()));
//            BigDecimal includedMoney = BigDecimal.valueOf(0);
//            for (BalancePublishEntity balancePublishEntity : pubishList) {
//                String worderIds = balancePublishEntity.getWorderIds();
//                String stimulateIds = balancePublishEntity.getStimulateIds();
//
//                if (StringUtils.isNotEmpty(worderIds)){
//                    String [] ids = worderIds.split(",");
//                    List<String> idList = Arrays.asList(ids);
//                    for (String id : idList) {
//                        BigDecimal noFeeMoney = worderChildInformationDao.selectFeeByWorderId(id);
//                        Map map = this.worderInformationAccountDao.getOneDotAllInfoByDotId(balancePublishEntity.getDotId());
//                        BigDecimal dotTaxRate = new BigDecimal(getStringValue(map, "dotTaxPoint").replaceAll("%", "").trim());
//                        BigDecimal dotTax = dotTaxRate.divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP);
//                        BigDecimal feeMoney = noFeeMoney.multiply(dotTax.add(new BigDecimal(1))).setScale(2,balanceProperties.ROUND_MODE);
//                        //重新计算含税价
//                        includedMoney = includedMoney.add(feeMoney);
//                    }
//                }
//                if (StringUtils.isNotEmpty(stimulateIds)){
//                    String [] ids = stimulateIds.split(",");
//                    List<String> idList = Arrays.asList(ids);
//                    BigDecimal feeMoney = worderChildInformationDao.selectFeeByStimulateIds(idList);
//                    includedMoney = includedMoney.add(feeMoney);
//                }
//            }
//            record.setPublishFee(includedMoney);
//        }
        // 状态 1:暂存 2:提交 3:第一次审核通过 4:第二次审核通过 5:已发布 -1:第一次审核不通过 -2:第二次审核不通过
        for (BalancePublishEntity entity : page.getRecords()) {
            if (null != entity.getBatchStatus() && entity.getBatchStatus() == 1) {
                entity.setBalanceStatus("暂存");
            } else if (null != entity.getBatchStatus() && entity.getBatchStatus() == 2) {
                entity.setBalanceStatus("提交");
            } else if (null != entity.getBatchStatus() && entity.getBatchStatus() == 3) {
                entity.setBalanceStatus("第一次审核通过");
            } else if (null != entity.getBatchStatus() && entity.getBatchStatus() == 4) {
                entity.setBalanceStatus("第二次审核通过");
            } else if (null != entity.getBatchStatus() && entity.getBatchStatus() == 5) {
                entity.setBalanceStatus("已发布");
            } else if (null != entity.getBatchStatus() && entity.getBatchStatus() == 13) {
                entity.setBalanceStatus("审核中");
            } else if (null != entity.getBatchStatus() && entity.getBatchStatus() == 14) {
                entity.setBalanceStatus("财务审核中");
            } else if (null != entity.getBatchStatus() && entity.getBatchStatus() == -1) {
                entity.setBalanceStatus("第一次审核不通过");
            } else if (null != entity.getBatchStatus() && entity.getBatchStatus() == -2) {
                entity.setBalanceStatus("第二次审核不通过");
            }
        }
        PageUtils pageUtils = new PageUtils(page.getRecords(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
        return pageUtils;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void pushCvpBill(BalancePublishEntity balancePublishEntity) {

        BrandEntity brandEntity = brandService.findByBrand(balancePublishEntity.getBrandId().longValue());
        String currentTime = DateUtils.getCurrentTime();
        //查询数据
        List<CostInformationEntity> costList = new ArrayList<>();
        Map<Integer, List<Map>> worderMap = new HashMap<>();
        Map<Integer, List<Map>> increMap = new HashMap<>();
        Map<Integer, Map> stimulateMap = new HashMap<>();
        PublishDetail pd = new PublishDetail();
        List<Integer> worderChildIdList = new ArrayList();
        if (StringUtils.isNotEmpty(balancePublishEntity.getWorderIds())) {
            worderChildIdList = idStr2idList(balancePublishEntity.getWorderIds());
            pd.setWorderIds(worderChildIdList);
        }
        if (StringUtils.isNotEmpty(balancePublishEntity.getIncreIds())) {
            pd.setIncreIds(idStr2idList(balancePublishEntity.getIncreIds()));
        }
        if (StringUtils.isNotEmpty(balancePublishEntity.getStimulateIds())) {
            pd.setStimulateIds(idStr2idList(balancePublishEntity.getStimulateIds()));
        }
        if (CollectionUtils.isNotEmpty(pd.getWorderIds())) {
            costList.addAll(costInformationService.list(new QueryWrapper<CostInformationEntity>().in("balance_id", pd.getWorderIds())));
            List<Map> worderList = this.worderInformationAccountDao.queryChildWorderByBalanceIds(pd.getWorderIds());
            for (Map map : worderList) {
                Integer worderId = getIntegerValue(map, "balance_id");
                if (!worderMap.containsKey(worderId)) {
                    worderMap.put(worderId, new ArrayList<>());
                }
                worderMap.get(worderId).add(map);
            }
            // 记录网点工单结算发布时间
            updateChildWorderStatus(pd.getWorderIds(),0,5,"网点结算已发布");
            //worderInformationAccountDao.update(null, new UpdateWrapper<WorderInformationAccountEntity>()
            //        .in("worder_id", pd.getWorderIds())
            //        .set("worder_publish_time", currentTime)
            //        .set("worder_set_status",5));
        }
        if (CollectionUtils.isNotEmpty(pd.getIncreIds())) {
            costList.addAll(costInformationService.list(new QueryWrapper<CostInformationEntity>().in("incre_id", pd.getIncreIds())));
            List<Map> worderList = worderInformationAccountDao.queryIncreWorderByIds(pd.getIncreIds());
            for (Map map : worderList) {
                Integer worderId = getIntegerValue(map, "worder_id");
                if (!increMap.containsKey(worderId)) {
                    increMap.put(worderId, new ArrayList<>());
                }
                increMap.get(worderId).add(map);
            }
            // 记录网点增项结算发布时间
            worderInformationAccountDao.update(null, new UpdateWrapper<WorderInformationAccountEntity>()
                    .in("worder_id", pd.getIncreIds())
                    .set("worder_incre_publish_time", currentTime)
                    .set("worder_incre_status", 2)
                    .set("worder_incre_status_value", "增项结算已发布"));
        }
        if (CollectionUtils.isNotEmpty(pd.getStimulateIds())) {
            costList.addAll(costInformationService.list(new QueryWrapper<CostInformationEntity>().in("balance_id", pd.getStimulateIds())));
            List<Map> stimulateList = worderInformationAccountDao.queryChildStimulateWorderByBalanceIds(pd.getStimulateIds());
            for (Map map : stimulateList) {
                stimulateMap.put(getIntegerValue(map, "balanceId"), map);
            }
            // 记录网点激励结算发布时间
            updateChildWorderStatus(pd.getStimulateIds(),1,null,null);
            //worderPmStimulateService.update(new UpdateWrapper<WorderPmStimulateEntity>()
            //        .in("id", pd.getStimulateIds())
            //        .set("publish_time", currentTime));
        }
        List<String> rowIdList = new ArrayList<>();
        for (CostInformationEntity costInformationEntity : costList) {
            rowIdList.add(costInformationEntity.getRowId());
        }
        Map<String, String> accountingMap = new HashMap<>();

        Collection<BalanceAcsAccountingStatusEntity> accountingStatusEntities = balanceAcsAccountingStatusService.listByIds(rowIdList);
        for (BalanceAcsAccountingStatusEntity entity : accountingStatusEntities) {
//            if ("S".equals(entity.getFlag()) && ("1".equals(entity.getAccountFlag()) || "3".equals(entity.getAccountFlag()))) {
            if ("20".equals(entity.getAccountFlag())) {
                accountingMap.put(entity.getRowId(), entity.getAccountCode());
            } else {
                throw new RRException("有未记账成功的成本信息，rowId:" + entity.getRowId());
            }
        }

        //查询网点信息
        Map map = this.worderInformationAccountDao.getOneDotAllInfoByDotId(balancePublishEntity.getDotId());
        String dotNo = getStringValue(map, "dot_code");
        String customerCode = getStringValue(map, "customer_code");
        String dotVCode = getStringValue(map, "v_code");
        String dotName = getStringValue(map, "dot_name");
        String dotCity = getDotCity(map);
        String branchCode = getStringValue(map, "branch_code");
        String branchName = getStringValue(map, "branch_name");
        String dotBank = getStringValue(map, "dot_bank");
        BigDecimal dotTaxRate = new BigDecimal(getStringValue(map, "dotTaxPoint").replaceAll("%", "").trim());
        String bankAccount = getStringValue(map, "bank_account");
        String contractUrl = getStringValue(map, "contract_url");

        //整理时间参数
        Date date = new Date();
        GregorianCalendar c = new GregorianCalendar();
        c.setTime(date);
        String gc1 = DateUtils.format(date, DateUtils.DATE_TIME_PATTERN);
//        XMLGregorianCalendar gc1 = null;
//        try {
//            gc1 = DatatypeFactory.newInstance().newXMLGregorianCalendar(c);
//        } catch (DatatypeConfigurationException e) {
//            e.printStackTrace();
//            throw new RRException("调用商户通对账接口失败", e);
//        }
//        c.add(GregorianCalendar.DATE, 7);

        Date date2 = DateUtils.addDateDays(date, 7);
        String gc2 = DateUtils.format(date2, DateUtils.DATE_TIME_PATTERN);
        String cvpPayTime = DateUtils.format(date2, DateUtils.DATE_PATTERN);
//        XMLGregorianCalendar gc2 = null;
//        try {
//            gc2 = DatatypeFactory.newInstance().newXMLGregorianCalendar(c);
//        } catch (DatatypeConfigurationException e) {
//            e.printStackTrace();
//            throw new RRException("调用商户通对账接口失败", e);
//        }
        //生成对账单单号
        //3位厂商序号
        String dotIdStr = balancePublishEntity.getDotId().toString();
        if (dotIdStr.length() > 3) {
            dotIdStr = dotIdStr.substring(dotIdStr.length() - 3);
        } else {
            while (dotIdStr.length() < 3) {
                dotIdStr = "0" + dotIdStr;
            }
        }
        //对账单单号为ZLW+6位日期+6位时分秒+6位序列
        List<WorderChildInformationEntity> list = worderChildInformationDao.selectList(new QueryWrapper<WorderChildInformationEntity>().eq("publish_id",balancePublishEntity.getId()));
        String billNo = null;
        if(list!=null&&list.size()>0&&list.get(0).getType()==1){
            billNo = "ZLWDZ" + new SimpleDateFormat("yyMMddHHmmss").format(date) + getSerialNumber();
        }else{
            billNo = "ZLW" + new SimpleDateFormat("yyMMddHHmmss").format(date) + getSerialNumber();
        }

        //明细整理
        BigDecimal apAmt = BigDecimal.ZERO; //对账单应结总金额（不包含索赔激励的）
        BigDecimal rewardAmt = BigDecimal.ZERO; //激励总金额
        BigDecimal reduceAmt = BigDecimal.ZERO; //索赔总金额
        BigDecimal acAmt = BigDecimal.ZERO; //对账单应结总金额（包含索赔激励的）

        /* --------------按照LJ的推送接口整理参数并推送 start ----------------*/
//        List<IfLgSheetDetailCkDTO> ljList = new ArrayList<>();
        List<SettleOrderInfoForHscpsInfo> ljList = new ArrayList<>();


        List<BalanceCvpDetailEntity> detailList = new ArrayList<>();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateStr = df.format(date);
        XMLGregorianCalendar nowDate = null;
        try {
            GregorianCalendar c1 = new GregorianCalendar();
            c1.setTime(date);
            nowDate = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RRException("调用商户通对账接口失败", e);
        }
        String businessType = "";
        for (CostInformationEntity cost : costList) {
            if (!accountingMap.containsKey(cost.getRowId())) {
                continue;
            }
            businessType = cost.getSourceType();
            String accountCode = accountingMap.get(cost.getRowId());
            String rowId = cost.getRowId();
            if (cost.getStimulateId() != null) {
                BigDecimal dmbtr = cost.getDmbtr();
//                BigDecimal dotTax = dotTaxRate.divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP);
//                BigDecimal includedMoney = dmbtr.multiply(dotTax.add(new BigDecimal(1))).setScale(2,balanceProperties.ROUND_MODE);

                Map worder = stimulateMap.get(cost.getBalanceId());
//                IfLgSheetDetailCkDTO dto = new IfLgSheetDetailCkDTO();
                SettleOrderInfoForHscpsInfo dto = new SettleOrderInfoForHscpsInfo();

//                dto.setOrderId(getStringValue(worder, "id"));
//                dto.setItemCode(getStringValue(worder, "id"));
                dto.setItemCode(getStringValue(worder, "item_code"));
                dto.setOrderCode(getStringValue(worder, "balanceNo"));
                dto.setProductName("激励费用-" + brandEntity.getBrandName() + "-" + getStringValue(worder, "typeName"));
                dto.setSupplierVcode(dotVCode);
                dto.setSupplierName(dotName);
//                XMLGregorianCalendar createDate = null;
                String createDate = "";
                try {
                    createDate = DateUtils.format((Date) worder.get("create_time"), DateUtils.DATE_TIME_PATTERN);
//                    GregorianCalendar c1 = new GregorianCalendar();
//                    c1.setTime((Date) worder.get("create_time"));
//                    createDate = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RRException("调用商户通对账接口失败", e);
                }
                dto.setOrderCreateTime(createDate);
//                dto.setOrderAmount(dmbtr);
                dto.setOrderAmount(balancePublishEntity.getPublishFee().toString());
                dto.setSettleAmount(dmbtr == null ? null : dmbtr.toString());
                dto.setProductQuantity("1");
                dto.setInvoiceTaxRate(dotTaxRate == null ? null : dotTaxRate.toString());
                dto.setBranchCode(branchCode);
                dto.setAccountCreateTime(dateStr);
                dto.setOrderCreateTime(createDate);
                dto.setPayNo(cost.getPayNo());
                dto.setBuyerName(getStringValue(worder, "user_name"));
                dto.setBuyerCity(cost.getCity());
                dto.setBuyerMobile(getStringValue(worder, "user_phone"));

                /* -------------暂估成本信息 start----------------*/
//                dto.setYwdh(cost.getOrderNo());
                dto.setYwms(cost.getYwms());
//                dto.setBubrs(balanceProperties.RRS_COMPANY_CODE);
//                XMLGregorianCalendar budatGc = null;
                Date budatDate = null;
                String budatGc = cost.getBudat();
//                try {
//                    GregorianCalendar c1 = new GregorianCalendar();
//                    budatDate = new SimpleDateFormat("yyyy-MM-dd").parse(cost.getBudat());
//                    c1.setTime(budatDate);
//                    budatGc = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    throw new RRException("调用商户通对账接口失败", e);
//                }
                dto.setOrderPayTime(dateStr);
                dto.setBudat(budatGc);
                dto.setBktxt(cost.getBktxt());
                dto.setXblnr(cost.getXblnr());
                dto.setWaers(cost.getWaers());
                dto.setKunnr(cost.getKunnr());
                dto.setLifnr(cost.getLifnr());
//                dto.setName(cost.getName());
//                dto.setCity(cost.getCity());
                dto.setDmbtr(cost.getDmbtr() != null ? cost.getDmbtr().abs().toString() : null);
                dto.setDmbtr1(cost.getDmbtr1() != null ? cost.getDmbtr1().abs().toString() : null);
                dto.setDmbtr2(cost.getDmbtr2() != null ? cost.getDmbtr2().abs().toString() : null);
                dto.setXref3(cost.getXref3());
                dto.setSgtxt(cost.getSgtxt());
                if(list!=null&&list.size()>0&&list.get(0).getType()==1){
                    dto.setSourceType("ST_XZJCD");
                }else{
                    dto.setSourceType(cost.getSourceType());
                }

//                dto.setRowIdzg(cost.getRowId());
//                dto.setYwmszg(cost.getYwms());
//                dto.setBukrszg(balanceProperties.RRS_COMPANY_CODE);
//                dto.setGjahrzg(cost.getBudat().substring(0, 4));
//                dto.setBelnrzg(accountCode);
                dto.setAccountCode(accountCode);
                dto.setBukrs(balanceProperties.RRS_COMPANY_CODE);
                // 固定填写 RS-大服务
                dto.setSysCode("RS");
                // 固定填写 0-后支付
                dto.setSettlePayType("0");

                dto.setOrderRevenue("0");
                /* -------------暂估成本信息 end----------------*/
                ljList.add(dto);
//                apAmt = apAmt.add(cost.getDmbtr());

                BalanceCvpDetailEntity e = new BalanceCvpDetailEntity();
                BeanUtils.copyProperties(dto, e);
                e.setStimulateId(cost.getBalanceId());
                detailList.add(e);
            } else {
                String balanceType = null;
                String balanceTypeCode = null;
                List<Map> worderList;
                if (cost.getWorderId() != null) {
                    balanceType = "-工单结算";
                    balanceTypeCode = "-1";
                    worderList = worderMap.get(cost.getBalanceId());
                } else {
                    balanceType = "-增项结算";
                    balanceTypeCode = "-2";
                    worderList = increMap.get(cost.getIncreId());
                }
                for (Map worder : worderList) {
                    BigDecimal dmbtr = cost.getDmbtr();
                    BigDecimal dotTax = dotTaxRate.divide(BigDecimal.valueOf(100),2, balanceProperties.ROUND_MODE);
                    BigDecimal includedMoney = dmbtr.multiply(dotTax.add(new BigDecimal(1))).setScale(2,balanceProperties.ROUND_MODE);
//                    IfLgSheetDetailCkDTO dto = new IfLgSheetDetailCkDTO();
                    SettleOrderInfoForHscpsInfo dto = new SettleOrderInfoForHscpsInfo();
                    BigDecimal orderAmount = null;
//                    dto.setItemCode(rowId);
                    dto.setInvoiceOrderNo(getStringValue(worder, "invoiceNo"));
                    dto.setSettleAuditNo(balancePublishEntity.getBatchNo());
                    if (cost.getWorderId() != null) {
//                        dto.setOrderId(getStringValue(worder, "balance_id"));
                        dto.setItemCode(getStringValue(worder, "item_code"));
                        dto.setOrderCode(getStringValue(worder, "balance_no"));
                        orderAmount = new BigDecimal(getStringValue(worder, "company_balance_fee_sum"));
                        String balanceTypeId = getStringValue(worder, "balance_type");
                        if ("1".equals(balanceTypeId)) {
                            dto.setProductName("质保金费用-" + brandEntity.getBrandName() + "-" + getStringValue(worder, "typeName"));
                        } else {
                            dto.setProductName("工单费用-" + brandEntity.getBrandName() + "-" + getStringValue(worder, "typeName"));
                        }

                        for (WorderChildInformationEntity worderChildInformationEntity : list) {
                            if (worderChildInformationEntity.getId().equals(cost.getBalanceId())){
                                dto.setOrderRevenue(String.valueOf(worderChildInformationEntity.getCompanyBalanceFeeSum()));
                                break;
                            }
                        }
                    } else {
//                        dto.setOrderId(getStringValue(worder, "worder_id"));
//                        dto.setItemCode(getStringValue(worder, "worder_id"));
                        dto.setItemCode(getStringValue(worder, "worder_no"));
                        dto.setOrderCode(getStringValue(worder, "worder_no"));
                        orderAmount = new BigDecimal(getStringValue(worder, "user_actual_cost"));
                        dto.setProductName("增项费用-" + brandEntity.getBrandName() + "-" + getStringValue(worder, "typeName"));
                        WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no",getStringValue(worder, "worder_no")));
                        dto.setOrderRevenue(String.valueOf(worderInformationEntity.getUserActualCost()));
                    }

                    dto.setSupplierVcode(dotVCode);
                    dto.setSupplierName(dotName);
//                    XMLGregorianCalendar createDate = null;
                    String createDate = "";
                    try {
//                        GregorianCalendar c1 = new GregorianCalendar();
//                        c1.setTime((Date) worder.get("create_time"));
//                        createDate = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
                        createDate = DateUtils.format((Date) worder.get("create_time"), DateUtils.DATE_TIME_PATTERN);
                    } catch (Exception e) {
                        log.error("调用商户通对账接口失败", e);
                        throw new RRException("调用商户通对账接口失败", e);
                    }
//                    BigDecimal amt = new BigDecimal(getStringValue(worder,"balance_fee_sum"));
                    dto.setOrderCreateTime(createDate);
//                    dto.setOrderAmount(orderAmount);
                    dto.setOrderAmount(balancePublishEntity.getPublishFee().toString());
//                    dto.setSettleAmount(amt); //必须与暂估成本的dmbtr一致
                    dto.setSettleAmount(includedMoney.toString());
                    dto.setProductQuantity("1");
                    dto.setInvoiceTaxRate(dotTaxRate == null ? null : dotTaxRate.toString());
                    dto.setBranchCode(branchCode);
                    dto.setAccountCreateTime(dateStr);
                    dto.setOrderCreateTime(createDate);
                    dto.setPayNo(cost.getPayNo());
                    dto.setBuyerName(getStringValue(worder, "user_name"));
                    dto.setBuyerCity(cost.getCity());
                    dto.setBuyerMobile(getStringValue(worder, "user_phone"));

                    /* -------------暂估成本信息 start----------------*/
//                    dto.setYwdh(cost.getOrderNo());
                    dto.setYwms(cost.getYwms());
//                    dto.setBubrs(balanceProperties.RRS_COMPANY_CODE);
//                    XMLGregorianCalendar budatGc = null;
//                    Date budatDate = null;
//                    try {
//                        GregorianCalendar c1 = new GregorianCalendar();
//                        budatDate = new SimpleDateFormat("yyyy-MM-dd").parse(cost.getBudat());
//                        c1.setTime(budatDate);
//                        budatGc = DatatypeFactory.newInstance().newXMLGregorianCalendar(c1);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                        throw new RRException("调用商户通对账接口失败", e);
//                    }
                    dto.setOrderPayTime(dateStr);
                    dto.setBudat(cost.getBudat());
                    dto.setBktxt(cost.getBktxt());
                    dto.setXblnr(cost.getXblnr());
                    dto.setWaers(cost.getWaers() == null ? "CNY" : cost.getWaers());
                    dto.setKunnr(cost.getKunnr());
                    dto.setLifnr(cost.getLifnr());
//                    dto.setName(cost.getName());
//                    dto.setCity(cost.getCity());
//                    dto.setDmbtr(amt);
//                    dto.setDmbtr1(BigDecimal.ZERO);
//                    dto.setDmbtr2(BigDecimal.ZERO);
                    dto.setDmbtr(cost.getDmbtr() == null ? null : cost.getDmbtr().toString());
                    dto.setDmbtr1(cost.getDmbtr1() == null ? null : cost.getDmbtr1().toString());
                    dto.setDmbtr2(cost.getDmbtr2() == null ? null : cost.getDmbtr2().toString());
                    dto.setXref3(cost.getXref3());
                    dto.setSgtxt(cost.getSgtxt());
                    if(list!=null&&list.size()>0&&list.get(0).getType()==1){
                        dto.setSourceType("ST_XZJCD");
                    }else{
                        dto.setSourceType(cost.getSourceType());
                    }
//                    dto.setRowIdzg(cost.getRowId());
//                    dto.setYwmszg(cost.getYwms());
//                    dto.setBukrszg(balanceProperties.RRS_COMPANY_CODE);
//                    dto.setGjahrzg(cost.getBudat().substring(0, 4));
//                    dto.setBelnrzg(accountCode);
                    dto.setAccountCode(accountCode);
                    dto.setBukrs(balanceProperties.RRS_COMPANY_CODE);
                    // 固定填写 RS-大服务
                    dto.setSysCode("RS");
                    // 固定填写 0-后支付
                    dto.setSettlePayType("0");
                    /* -------------暂估成本信息 end----------------*/
                    ljList.add(dto);
//                    apAmt = apAmt.add(cost.getDmbtr());

                    BalanceCvpDetailEntity e = new BalanceCvpDetailEntity();
                    BeanUtils.copyProperties(dto, e);
                    e.setWorderId(cost.getBalanceId());
                    e.setIncreId(cost.getIncreId());
                    detailList.add(e);
                }
            }
        }
        apAmt = balancePublishEntity.getPublishFee();
        acAmt = apAmt.add(rewardAmt);
//        if (acAmt.compareTo(BigDecimal.ZERO) <= 0) {
//            throw new RRException("对账单结算总金额必须大于0！");
//        }

        /*----------------------  商户通对账接口参数对象 start ------------------*/
//        IfLjSheetIn ifLjSheetIn = new IfLjSheetIn();
        ReqNewsDataAccess ifLjSheetIn = new ReqNewsDataAccess();
        ifLjSheetIn.setBillNo(billNo);
        ifLjSheetIn.setDetailCount(detailList.size() + "");
        ifLjSheetIn.setApAmt(apAmt == null ? null : apAmt.toString());
        ifLjSheetIn.setAcAmt(ifLjSheetIn.getApAmt());
        ifLjSheetIn.setRewardAmt(rewardAmt == null ? null : rewardAmt.toString());
        ifLjSheetIn.setReduceAmt(reduceAmt == null ? null : reduceAmt.toString());
        ifLjSheetIn.setVendorCode(dotVCode);
        ifLjSheetIn.setVendorName(dotName);
        ifLjSheetIn.setStEntityCode(branchCode);
        ifLjSheetIn.setStEntityName(branchName);
        ifLjSheetIn.setBdEntityCode(balanceProperties.BUDGET_CODE);
        ifLjSheetIn.setBdEntityName(balanceProperties.BUDGET_NAME);
        ifLjSheetIn.setStUserCode(balanceProperties.BALANCE_STAFF_CODE);
        ifLjSheetIn.setStUserName(balanceProperties.BALANCE_STAFF_NAME);
//        ifLjSheetIn.setFeeTypeCode(balanceProperties.FEE_TYPE_CODE);
//        ifLjSheetIn.setFeeTypeName(balanceProperties.FEE_TYPE_NAME);
        ifLjSheetIn.setPeriodChar(new SimpleDateFormat("yyyyMM").format(date));
        ifLjSheetIn.setCreatedTime(gc1);
        ifLjSheetIn.setBillBeginDate(gc1);
        ifLjSheetIn.setBillEndDate(gc2);
//        ifHcspSheetIn.setBusinesstype("ST_SH");
        ifLjSheetIn.setOriginApp("RS");
        ifLjSheetIn.setCAccountCode(balanceProperties.RRS_COMPANY_CODE);
        ifLjSheetIn.setCAccountName(balanceProperties.RRS_COMPANY_NAME);
        ifLjSheetIn.setCSaleType("内销");


        // 新资金发布 修改开票单状态为11（已发布） 更新新资金池
        if (balancePublishEntity.getPublishType() == 36 || balancePublishEntity.getPayForm().equals("BSYP")) {
            ifLjSheetIn.setCPayTypeCode(balancePublishEntity.getPayForm());
            if (balancePublishEntity.getPayForm().equals("BSYP")) {
                ifLjSheetIn.setCPayType("背书转让银票");
                log.info("ifLjSheetIn set to 背书转让银票, {}", ifLjSheetIn);
            }
        } else {
            ifLjSheetIn.setCPayTypeCode("X");
        }

        ifLjSheetIn.setIApplied(acAmt == null ? null : acAmt.toString());
        if(list!=null&&list.size()>0&&list.get(0).getType()==1){
            ifLjSheetIn.setBusinessType("ST_XZJCD");
            ifLjSheetIn.setCPayType("正常付款");
            log.info("ifLjSheetIn set to 正常付款, {}", ifLjSheetIn);
            if (balancePublishEntity.getPayForm().equals("X") || balancePublishEntity.getPayForm().equals("BSYP")) {
                // 出账公司账户
                ifLjSheetIn.setCAccountNoOut("801005267002039001");
                // 出账银行名称
                ifLjSheetIn.setCAccountBankOut("海尔集团财务有限责任公司");

                ifLjSheetIn.setCAccountNo(bankAccount);
                ifLjSheetIn.setCAccountBank(dotBank);
            }else{
                //招商银行编码 CMB
                if (balancePublishEntity.getOutBank().equals("CMB")){
                    // 出账公司账户
                    ifLjSheetIn.setCAccountNoOut("***************");
                    // 出账银行名称
                    ifLjSheetIn.setCAccountBankOut("招商银行股份有限公司上海闵行支行");

                    List<SysDictionaryDetailEntity> sysDictionaryDetailEntity = sysDictionaryDetailDao.getPayBank(balancePublishEntity.getPayBank());
                    String accountName = sysDictionaryDetailEntity.get(0).getDetailName();
                    DotBank bank = balancePublishDao.getBank(balancePublishEntity.getDotId(),accountName);
                    if (bank!=null){
                        ifLjSheetIn.setCAccountNo(bank.getBankAccount());
                        ifLjSheetIn.setCAccountBank(bank.getDotBank());
                    }
                }else{
                    ifLjSheetIn.setAcceptingBank(balancePublishEntity.getPayBank());
                    if (StringUtils.isNotBlank(balancePublishEntity.getOutBank())){
                        String outBank = balancePublishEntity.getOutBank();
                        List<SysDictionaryDetailEntity> sysDictionaryDetailEntity = sysDictionaryDetailDao.getOutBank(outBank);
                        // 出账公司账户
                        ifLjSheetIn.setCAccountNoOut(sysDictionaryDetailEntity.get(0).getRemark());
                        // 出账银行名称
                        ifLjSheetIn.setCAccountBankOut(sysDictionaryDetailEntity.get(0).getDetailName());
                    }
                    if (balancePublishEntity.getPayBank().equals("MRYH")){
                        //默认银行
                        DotBank bank = balancePublishDao.getBankFirst(balancePublishEntity.getDotId());
                        if (bank!=null){
                            ifLjSheetIn.setCAccountNo(bank.getBankAccount());
                            ifLjSheetIn.setCAccountBank(bank.getDotBank());
                        }
                    }else{
                        List<SysDictionaryDetailEntity> sysDictionaryDetailEntity = sysDictionaryDetailDao.getPayBank(balancePublishEntity.getPayBank());
                        String accountName = sysDictionaryDetailEntity.get(0).getDetailName();
                        DotBank bank = balancePublishDao.getBank(balancePublishEntity.getDotId(),accountName);
                        if (bank!=null){
                            ifLjSheetIn.setCAccountNo(bank.getBankAccount());
                            ifLjSheetIn.setCAccountBank(bank.getDotBank());
                        }
                    }

                }
            }
        }else{
            ifLjSheetIn.setCPayType("正常付款");
            // 出账公司账户
            ifLjSheetIn.setCAccountNoOut("801005267002039001");
            // 出账银行名称
            ifLjSheetIn.setCAccountBankOut("海尔集团财务有限责任公司");
            ifLjSheetIn.setBusinessType(businessType);

            ifLjSheetIn.setCAccountNo(bankAccount);
            ifLjSheetIn.setCAccountBank(dotBank);
        }
        ifLjSheetIn.setCInvoiceType("正常发票");
        ifLjSheetIn.setCInvoiceOption("发票先到");
        ifLjSheetIn.setCFeeItemCode("7010101");
        ifLjSheetIn.setCFeeItemName("材料款");
        ifLjSheetIn.setCSsupType("供应商");
        ifLjSheetIn.setCUserCode(balanceProperties.BALANCE_STAFF_CODE);
        ifLjSheetIn.setCUserName(balanceProperties.BALANCE_STAFF_NAME);
        ifLjSheetIn.setCDescriPtionON("桩联网工单结算");
        ifLjSheetIn.setCReason("框架合同");
        ifLjSheetIn.setDPayable(gc1);
        ifLjSheetIn.setCvpPayTime(cvpPayTime);

        ifLjSheetIn.setInvoiceTaxRate(dotTaxRate == null ? null : dotTaxRate.toString());


        ifLjSheetIn.setDeliveryTime(DateUtils.format(balancePublishEntity.getSubmitTime(), DateUtils.DATE_TIME_PATTERN));

        ifLjSheetIn.setOprUserId(balancePublishEntity.getCreatorName());
        if (balancePublishEntity.getCreator() != null) {
            Integer publishUserId = balancePublishEntity.getCreator();

            SysUserEntity publishUser = sysUserService.getBaseMapper().selectById(publishUserId);
            ifLjSheetIn.setHandledBy(publishUser.getUsername());
        }

//        List<IfLgSheetDetailCkDTO> ljList1 = ifLjSheetIn.getLjList();
//        ljList1.addAll(ljList);

        ifLjSheetIn.setSettleOrderInfoForHscpsList(ljList);
        /*----------------------  商户通对账接口参数对象 end ------------------*/
        //保存推送数据的流水记录
        BalanceCvpRecordEntity cvpRecordEntity = new BalanceCvpRecordEntity();
        BeanUtils.copyProperties(ifLjSheetIn, cvpRecordEntity);
        cvpRecordEntity.setPushTime(date);
        if (ifLjSheetIn.getBillBeginDate() != null) {
            cvpRecordEntity.setBillBeginDate(date);
        }
        if (ifLjSheetIn.getBillEndDate() != null) {
            cvpRecordEntity.setBillEndDate(date2);
        }
        if (ifLjSheetIn.getCreatedTime() != null) {
            cvpRecordEntity.setCreatedTime(date);
        }
        if (ifLjSheetIn.getDPayable() != null) {
            cvpRecordEntity.setDPayable(date2);
        }
        balanceCvpRecordService.save(cvpRecordEntity);
        balanceCvpDetailService.saveBatch(detailList);
        //保存对账单信息
        CvpBillEntity bill = new CvpBillEntity();
        bill.setBillNo(billNo);
        bill.setStatusFlag("0");
        cvpBillDao.insert(bill);
        //更新发布单
        balancePublishEntity.setStatus(5);
        balancePublishEntity.setCvpBillId(bill.getId());
        this.updateById(balancePublishEntity);
        //保存对账单明细
        List<CvpBillDetailEntity> billDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pd.getWorderIds())) {
            for (Integer id : pd.getWorderIds()) {
                billDetailList.add(new CvpBillDetailEntity(null, bill.getId(), id, null, null));
            }
        }
        if (CollectionUtils.isNotEmpty(pd.getIncreIds())) {
            for (Integer id : pd.getIncreIds()) {
                billDetailList.add(new CvpBillDetailEntity(null, bill.getId(), null, id, null));
            }
        }
        if (CollectionUtils.isNotEmpty(pd.getStimulateIds())) {
            for (Integer id : pd.getStimulateIds()) {
                billDetailList.add(new CvpBillDetailEntity(null, bill.getId(), null, null, id));
            }
        }
        cvpBillDetailService.saveBatch(billDetailList);
        log.info("ifLjSheetIn: {}", ifLjSheetIn);
        JSONObject respJson = financeBusiness.newsDataAccess(ifLjSheetIn);
        RespNewsDataAccess resp = new RespNewsDataAccess();
        if (respJson != null) {
            resp = respJson.toJavaObject(RespNewsDataAccess.class);
        } else {
            resp.setCode("500");
            resp.setMsg("调用结算单传输接口失败");
        }
        //推送商户通
//        HcspResultInfo hcspResultInfo = dotHcspService.sendLjSheet(ifLjSheetIn);
//        if (!"1".equals(hcspResultInfo.getFlag())) {
//            throw new RRException("调用商户通对账接口失败:" + hcspResultInfo.getMsg());
//        }
        if (!FinanceBusiness.getSUCCESS().equals(resp.getCode())) {
            throw new RRException("调用结算单传输接口失败:" + resp.getMsg());
        }

        // 新资金发布 修改开票单状态为11（已发布） 更新垫资池
        if (balancePublishEntity.getPublishType() == 36) {
//            if (worderChildIdList != null && !worderChildIdList.isEmpty()) {
//                Integer invoiceId = balancePublishDao.queryInvoiceIdByWorderChildId(worderChildIdList.get(0));
//                if (invoiceId != null) {
//                    companyInvoiceService.update(null,
//                            new UpdateWrapper<CompanyInvoiceEntity>()
//                                    .eq("id", invoiceId)
//                                    .set("status", 11));
//                }
//            }
            // 垫资池处理 - 垫资完成
            advanceMoneyInfoService.publishMoney(balancePublishEntity.getPublishFee());
        }
        /* --------------按照LJ的推送接口整理参数并推送 end ----------------*/
    }

    /**
     * 更新结算子工单状态并且同步工单状态
     * @param ids
     * @param balanceSource
     * @param balanceSetStatus
     * @param message
     */
    private void updateChildWorderStatus(List<Integer> ids, int balanceSource, Integer balanceSetStatus, String message) {
        if(ids.isEmpty()){
            return;
        }
        //查询结算子工单
        List<WorderChildInformationEntity> childInformationEntityList = worderChildInformationDao.selectList(new QueryWrapper<WorderChildInformationEntity>().in("id", ids));
        //判断是否需要同步工单或者激励单状态，所有子结算工单更新完成，才会更新
        if(balanceSource == 0){
            //工单
            //更新结算子工单状态
            worderChildInformationDao.update(null, new UpdateWrapper<WorderChildInformationEntity>().in("id", ids).set("balance_set_status", balanceSetStatus).set("balance_set_status_value", message).set("publish_time", LocalDateTime.now()));
            //获取当前所有工单ID
            Set<Integer> worderIds = childInformationEntityList.stream().map(WorderChildInformationEntity::getWorderId).collect(Collectors.toSet());
            //查询工单下所有工单结算子工单
            List<WorderChildInformationEntity> childInformationList = worderChildInformationDao.selectList(new QueryWrapper<WorderChildInformationEntity>().in("worder_id", worderIds).eq("balance_source",0).eq("is_delete", 0));
            //分组
            Map<Integer, List<WorderChildInformationEntity>> worderGroupBys = childInformationList.stream().collect(Collectors.groupingBy(WorderChildInformationEntity::getWorderId, Collectors.toList()));
            worderGroupBys.forEach((worderId, list) -> {
                long count = list.stream().filter(worderChildInformation -> !worderChildInformation.getBalanceSetStatus().equals(balanceSetStatus)).count();
                if (count <= 0) {
                    //更新工单结算状态
                    worderInformationDao.update(null, new UpdateWrapper<WorderInformationEntity>().set("worder_set_status", balanceSetStatus).set("worder_set_status_value", message).set("worder_publish_time", DateUtils.getCurrentTime()).eq("worder_id", worderId));
                }
            });
        }else if(balanceSource == 1){
            //激励单
            worderChildInformationDao.update(null, new UpdateWrapper<WorderChildInformationEntity>().in("id", ids).set("publish_time", LocalDateTime.now()));
            //获取当前所有激励单ID
            Set<Integer> stimulateIds = childInformationEntityList.stream().map(WorderChildInformationEntity::getStimulateId).collect(Collectors.toSet());
            //查询激励单下所有激励结算子工单
            List<WorderChildInformationEntity> childInformationList = worderChildInformationDao.selectList(new QueryWrapper<WorderChildInformationEntity>().in("stimulate_id", stimulateIds).eq("balance_source",1).eq("is_delete", 0));
            //分组
            Map<Integer, List<WorderChildInformationEntity>> stimulateGroupBys = childInformationList.stream().collect(Collectors.groupingBy(WorderChildInformationEntity::getStimulateId, Collectors.toList()));
            stimulateGroupBys.forEach((stimulateId, list) -> {
                long count = list.stream().filter(worderChildInformation -> worderChildInformation.getPublishTime() == null).count();
                if (count <= 0) {
                    //更新激励单发布时间
                    worderPmStimulateDao.update(null, new UpdateWrapper<WorderPmStimulateEntity>().eq("id", stimulateId).set("publish_time", DateUtils.getCurrentTime()));
                }
            });
        }
    }

    @Override
    public BalancePublishVO getPublishBatchDetail(String batchNo) {
        //根据批次号查询发布信息
        List<BalancePublishEntity> balancePublishs = this.baseMapper.selectList(new QueryWrapper<BalancePublishEntity>().eq("batch_no", batchNo));
        //查询发布关联的工单信息
        List<PublishWorder> worderList = new ArrayList<>();
        balancePublishs.forEach(balancePublishEntity -> {
            List<Integer> worderIds = this.idStr2idList(balancePublishEntity.getWorderIds());
            if (CollectionUtils.isNotEmpty(worderIds)) {
                worderList.addAll(this.listPublishWorderByIds(1, worderIds));
            }
            List<Integer> increIds = this.idStr2idList(balancePublishEntity.getIncreIds());
            if (CollectionUtils.isNotEmpty(increIds)) {
                worderList.addAll(this.listPublishWorderByIds(2, increIds));
            }
            List<Integer> str2Ids = this.idStr2idList(balancePublishEntity.getStimulateIds());
            if (CollectionUtils.isNotEmpty(str2Ids)) {
                worderList.addAll(this.listPublishWorderByIds(3, str2Ids));
            }
        });
        for (PublishWorder publishWorder : worderList) {
            //激励单不进行重新计算
            if (publishWorder.getStimulateId()==null){
                //查询网点信息
                Map map = this.worderInformationAccountDao.getOneDotAllInfoByDotId(publishWorder.getDotId());
                BigDecimal dotTaxRate = new BigDecimal(getStringValue(map, "dotTaxPoint").replaceAll("%", "").trim());

                BigDecimal dotTax = dotTaxRate.divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP);
                BigDecimal includedMoney = publishWorder.getNoTaxfee().multiply(dotTax.add(new BigDecimal(1))).setScale(2,balanceProperties.ROUND_MODE);
                BigDecimal Tax = includedMoney.subtract(publishWorder.getNoTaxfee());
                publishWorder.setTax(Tax);
                //重新计算含税价
                publishWorder.setFee(includedMoney);
            }
        }
        //发布信息和工单信息填充入视图对象
        BalancePublishVO balancePublishVO = new BalancePublishVO();
        balancePublishVO.setBatchNo(batchNo);
        balancePublishVO.setBrandId(balancePublishs.get(0).getBrandId());
        balancePublishVO.setDotId(balancePublishs.get(0).getDotId());
        balancePublishVO.setStatus(balancePublishs.get(0).getStatus());
        balancePublishVO.setCreateTime(balancePublishs.get(0).getCreateTime());
        balancePublishVO.setCreator(balancePublishs.get(0).getCreator());
        balancePublishVO.setCreatorName(balancePublishs.get(0).getCreatorName());
        balancePublishVO.setWorderList(worderList);
        //关联的文件信息填充入视图对象
        List<BalancePublishFileEntity> relationList = balancePublishFileService.list(
                new QueryWrapper<BalancePublishFileEntity>().eq("publish_id", balancePublishs.get(0).getId()));    //查询发布与文件的关联关系
        if (CollectionUtils.isNotEmpty(relationList)) {
            List<Integer> fileIds = relationList.stream().map(BalancePublishFileEntity::getFileId).collect(Collectors.toList()); //获取文件ID
            List<BalanceFileEntity> fileList = (List<BalanceFileEntity>) balanceFileService.listByIds(fileIds);//查询文件
            balancePublishVO.setFileList(fileList);
        } else {
            balancePublishVO.setFileList(new ArrayList<>());
        }
        return balancePublishVO;
    }

    /**
     * @param type 类型 1: 网点工单结算 2:网点增项结算 3:网点激励结算
     * @param ids
     * @return
     */
    private List<PublishWorder> listPublishWorderByIds(int type, List<Integer> ids,List<Integer> stiIds) {
        switch (type) {
            case 1:
                List<PublishWorder> list = this.baseMapper.listWorders(ids,stiIds);
                //查询并整理回款流水号
                List<Map> maps = this.baseMapper.listWorderPayNo(ids);
                Map<Integer, StringBuilder> payNoMap = new HashMap<>();
                for (Map map : maps) {
                    Integer worderId = ((Number) map.get("worder_id")).intValue();
                    String payNo = (String) map.get("voucher_no");
                    StringBuilder sb = payNoMap.get(worderId);
                    if (sb == null) {
                        payNoMap.put(worderId, new StringBuilder(payNo));
                    } else {
                        sb.append(',').append(payNo);
                    }
                }
                //工单中填充回款流水号
                for (PublishWorder publishWorder : list) {
                    StringBuilder sb = payNoMap.get(publishWorder.getWorderId());
                    if (sb != null) {
                        publishWorder.setPayNo(sb.toString());
                    }
                }
                return list;
            case 2:
                list = this.baseMapper.listIncreWorders(ids,stiIds);
                return list;
            default:
                list = new ArrayList<>();
                return list;
        }
    }
    private List<PublishWorder> listPublishWorderByIds(int type, List<Integer> ids) {
        switch (type) {
            case 1:
                List<PublishWorder> list = this.baseMapper.listWorder(ids);
                //查询并整理回款流水号
                List<Map> maps = this.baseMapper.listWorderPayNo(ids);
                Map<Integer, StringBuilder> payNoMap = new HashMap<>();
                for (Map map : maps) {
                    Integer worderId = ((Number) map.get("worder_id")).intValue();
                    String payNo = (String) map.get("voucher_no");
                    StringBuilder sb = payNoMap.get(worderId);
                    if (sb == null) {
                        payNoMap.put(worderId, new StringBuilder(payNo));
                    } else {
                        sb.append(',').append(payNo);
                    }
                }
                //工单中填充回款流水号
                for (PublishWorder publishWorder : list) {
                    StringBuilder sb = payNoMap.get(publishWorder.getWorderId());
                    if (sb != null) {
                        publishWorder.setPayNo(sb.toString());
                    }
                }
                return list;
            case 2:
                list = this.baseMapper.listIncreWorder(ids);
                return list;
            case 3:
                list = this.baseMapper.listStimulateWorder(ids);
                return list;
            default:
                list = new ArrayList<>();
                return list;
        }
    }

    /**
     * 逗号隔开的id字符串转为List
     *
     * @param idStr 逗号隔开的ID字符串
     * @return
     */
    private List<Integer> idStr2idList(String idStr) {
        List<Integer> list = new ArrayList<>();
        if (StringUtils.isEmpty(idStr)) {
            return list;
        }
        String[] ids = idStr.split(",");
        for (String id : ids) {
            list.add(Integer.valueOf(id));
        }
        return list;
    }

    /**
     * 上传文件信息入库
     */
    private void addFiles(Integer publishId, Integer userId, MultipartFile... files) throws IOException {
        List<BalanceFileEntity> newFiles = balanceFileService.addFiles(userId, 22, files);
        List<BalancePublishFileEntity> newRelationList =
                newFiles.stream()
                        .map(e -> new BalancePublishFileEntity().setFileId(e.getFileId()).setPublishId(publishId))
                        .collect(Collectors.toList());
        balancePublishFileService.saveBatch(newRelationList);
    }

    private String getDotCity(Map map) {
        String dotAreaName = getStringValue(map, "dotAreaName");
        String dotCityName = getStringValue(map, "dotCityName");
        String dotCity;
        if ("市辖区".equals(dotCityName)) {
            dotCity = dotAreaName.substring(0, dotAreaName.length() - 1);
        } else {
            dotCity = dotCityName.substring(0, dotCityName.length() - 1);
        }
        return dotCity;
    }

    private String getStringValue(Map worderMap, Object key) {
        Object value = worderMap.get(key);
        return (value == null) ? "" : value.toString();
    }

    private Integer getIntegerValue(Map worderMap, Object key) {
        Object value = worderMap.get(key);
        if (null == value) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof Boolean) {
            boolean b = ((Boolean) value).booleanValue();
            if (b) {
                return 1;
            } else {
                return 0;
            }
        }
        return Integer.valueOf(value.toString());
    }


    private static Integer serialNo = 0;

    private synchronized static String getSerialNumber() {
        String serialStr = serialNo.toString();
        while (serialStr.length() < 6) {
            serialStr = "0" + serialStr;
        }
        serialNo = (serialNo + 1) % 1000000;
        return serialStr;
    }

}
