package com.bonc.rrs.worderapp.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.bonc.rrs.balanceprocess.entity.BalanceFileEntity;
import com.bonc.rrs.branchbalance.dao.WorderUsedMaterielDao;
import com.bonc.rrs.branchbalance.entity.WorderUsedMaterielEntity;
import com.bonc.rrs.ca.domain.CaApiResponse;
import com.bonc.rrs.ca.domain.OverProof;
import com.bonc.rrs.ca.service.ICaApiService;
import com.bonc.rrs.gace.enums.WorkOrderStatusEnum;
import com.bonc.rrs.gace.service.GacePushService;
import com.bonc.rrs.invoice.enterprises.util.StorageBusiness;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.serviceprovider.service.ProviderBusinessService;
import com.bonc.rrs.warning.annotation.Warning;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.constant.FlowConstant;
import com.bonc.rrs.worder.dao.*;
import com.bonc.rrs.worder.dto.dto.FixedMaterialBrandDto;
import com.bonc.rrs.worder.dto.dto.FixedMaterialNameDto;
import com.bonc.rrs.worder.dto.dto.FixedMaterielSpecDto;
import com.bonc.rrs.worder.dto.dto.IncreaseFeeDto;
import com.bonc.rrs.worder.dto.vo.WorderMaterielVo;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.entity.po.BrandPo;
import com.bonc.rrs.worder.entity.po.ExecuteFlowResultPo;
import com.bonc.rrs.worder.service.*;
import com.bonc.rrs.worderAudit.service.WorderAuditResultService;
import com.bonc.rrs.worderapp.Vo.PageVo;
import com.bonc.rrs.worderapp.Vo.SnStoreVo;
import com.bonc.rrs.worderapp.Vo.SnVo;
import com.bonc.rrs.worderapp.constant.Constant;
import com.bonc.rrs.worderapp.constant.FieldConstant;
import com.bonc.rrs.worderapp.entity.dto.WorderInformationDto;
import com.bonc.rrs.worderapp.entity.vo.*;
import com.bonc.rrs.worderapp.service.WorderOrderService;
import com.bonc.rrs.workManager.dao.AutidOrderMapper;
import com.bonc.rrs.workManager.dao.BrandMapper;
import com.bonc.rrs.workManager.dao.SysFilesMapper;
import com.bonc.rrs.workManager.dao.WorkMsgDao;
import com.bonc.rrs.workManager.entity.OperationRecord;
import com.bonc.rrs.workManager.entity.vo.DotPositionAddedMaterielStockVo;
import com.bonc.rrs.workManager.entity.vo.ReceiverInfoVo;
import com.bonc.rrs.workManager.entity.vo.RegionCodeVo;
import com.bonc.rrs.workManager.entity.vo.UsedMaterielVo;
import com.bonc.rrs.workManager.service.SysDoloadService;
import com.common.pay.common.utils.DateUtil;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/app/worder")
@Api(tags = {"app勘测/安装接口"})
@Log4j2
public class WorderOrderController {

    @Autowired
    WorderOrderService worderOrderService;
    @Autowired
    private BizAttendantService bizAttendantService;
    @Autowired
    WorderExtFieldService worderExtFieldService;
    @Autowired(required = false)
    WorkMsgDao workMsgDao;
    @Autowired(required = false)
    WorderAuditResultService worderAuditResultService;
    @Autowired(required = false)
    BrandMapper brandMapper;
    @Autowired(required = false)
    SysDoloadService sysDoloadService;
    @Autowired
    private WorderTemplateDao worderTemplateDao;
    @Autowired
    WorderInformationService worderInformationService;
    @Autowired
    WorderInformationDao worderInformationDao;

    @Autowired
    WorderInformationAttributeDao worderInformationAttributeDao;
    @Autowired
    private MaterialInforDao materialInforDao;

    @Autowired
    private StorageBusiness storageBusiness;

    @Autowired
    private SysDictionaryDetailService sysDictionaryDetailService;

    @Autowired(required = false)
    private ICaApiService caApiService;

    @Autowired
    private FlowCommon flowCommon;

    @Autowired
    private WorderRemarkLogService worderRemarkLogService;

    @Autowired(required = false)
    private SysFilesMapper sysFilesMapper;

    @Autowired(required = false)
    private AutidOrderMapper autidOrderMapper;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private ExtFieldDao extFieldDao;

    @Autowired
    private ProviderBusinessService providerBusinessService;

    @Autowired
    private WorderUsedMaterielDao worderUsedMaterielDao;

    @Autowired
    private WorderBuffService worderBuffService;

    @Autowired
    private GacePushService gacePushService;


    @PostMapping("/user/get")
    public String getCurrentUser() {
        Subject subject = SecurityUtils.getSubject();
        SysUserEntity principal = (SysUserEntity) subject.getPrincipal();
        System.out.println(principal.toString());
        return principal.toString();
    }

    // finished 工单数量 0:未完成 1：已完成
    @PostMapping("/count")
    @ApiOperation("工单数量")
    public R getWorderInformationCount(PageVo pageVo) {
        Map map = JSONObject.parseObject(JSONObject.toJSONString(pageVo));
        SysUserEntity sysUserEntity = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        map.put("userId", sysUserEntity.getUserId());
        map.put("colorLabel", "0");  // 色标
        return R.resultCount(worderOrderService.getWorderInformationCount(map));
    }

    @PostMapping("/detail/get")
    @ApiOperation("工单详情")
    public R getWorderDetails(String worderNo) {
        return R.ok().put("detail", worderOrderService.getWorderDetails(worderNo));
    }


    // 工单状态修改
    @RequestMapping("/status/update")
    public R updateWorderStatus(WorderStatusVo worderStatusVo) {

        if (worderStatusVo.getWorderStatus() != null && worderStatusVo.getWorderStatus() == 2
                && worderStatusVo.getWorderExecStatus() != null && worderStatusVo.getWorderExecStatus() == 13) {
            //校验图片是否存在未上传
            R result = worderExtFieldService.checkFieldRequired(worderStatusVo.getWorderNo(), 3);
            if ((int) result.get("code") != 0) {
                return result;
            }
        } else if (worderStatusVo.getWorderExecStatus() == 5) {
            //校验图片是否存在未上传
            R result = worderExtFieldService.checkFieldRequired(worderStatusVo.getWorderNo(), 2);
            if ((int) result.get("code") != 0) {
                return result;
            }
        }

        if (worderStatusVo.getWorderStatus() != null && worderStatusVo.getWorderStatus() == 2
                && worderStatusVo.getWorderExecStatus() != null && worderStatusVo.getWorderExecStatus() == 13) {
            recodeWorderOperate(worderStatusVo.getWorderNo(), Constant.INSTALL, Constant.INSTALL_NOT_AUDIT, "提交安装信息审核");
        }

        Integer updateNum = worderOrderService.updateWorderStatus(worderStatusVo);
        return R.ok().put("updateNum", updateNum);
    }

    // 工单状态查询
    @PostMapping("/status/get")
    public R getWorderStatus(@RequestParam("worderNo") String worderNo) {
        return R.ok().put(Constant.WORDER_STATUS, worderOrderService.getWorderStatus(worderNo));
    }

    @PostMapping("/info/get")
    @ApiOperation("根据工单编号获得工单详情")
    public List<WorderInformationEntity> getWorderInformation(@ApiParam("工单编号") String worderNo) {
        return worderOrderService.getWorderInformation(worderNo);
    }

    // 修改勘测预约时间也是勘测预约
    @Warning("勘测预约")
    @PostMapping("/convey/appoint")
    @ApiOperation("修改勘测预约时间也是勘测预约")
    @Lock4j(keys = {"#worderInformationVo.worderNo"})
    public R updateConveyApoint(@RequestBody WorderInformationVo worderInformationVo) {
        Subject subject = SecurityUtils.getSubject();
        SysUserEntity principal = (SysUserEntity) subject.getPrincipal();
        WorderInformationDto worderInformationDto = new WorderInformationDto();
        worderInformationDto.setWorderNo(worderInformationVo.getWorderNo());
        worderInformationDto.setWorderStatus(1);
        worderInformationDto.setWorderExecStatus(FieldConstant.NOT_CONVEY);
        if (worderInformationVo.getConveyAppointTime() == null || worderInformationVo.getConveyAppointTime().trim().equals("") || worderInformationVo.getConveyAppointTime().equals("YYYY-MM-DD")) {
            return R.error("请填写预约时间");
        }
        if (StringUtils.isNotBlank(worderInformationVo.getNoInstallReason()) && worderInformationVo.getNoInstallReason().equals("-1")) {
            return R.error("请选择原因");
        }
        if (StringUtils.isBlank(worderInformationVo.getWorderLevel())) {
            SysDictionaryDetailEntity sysDictionaryDetailEntity = sysDictionaryDetailService.getById(worderInformationVo.getNoInstallReason());
            worderInformationDto.setWorderLevel(sysDictionaryDetailEntity.getRemark());
        }
        String time = worderInformationVo.getConveyAppointTime();
        time = time.replace(":", " ");
        if (StringUtils.isNotBlank(time)) {
            time = time.substring(0, 13) + ":00:00";
            worderInformationVo.setConveyAppointTime(time);
        }
        Result providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderInformationVo.getWorderNo()).conveyAppointTime(worderInformationVo.getConveyAppointTime()).build(), "pushContactInfo");
        if (providerBusinessResult.getCode() != 0) {
            return R.error(providerBusinessResult.getCode(), providerBusinessResult.getMsg());
        }
        //调用长安接口
        QueryWrapper<WorderInformationEntity> queryWrapper = new QueryWrapper<>();
        String worderNo = worderInformationVo.getWorderNo();
        queryWrapper.eq("worder_no",worderNo );
        WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(queryWrapper);
        List<WorderRemarkLogEntity> worderRemarkLogEntityList = worderRemarkLogService.queryConnectTime(worderInformationEntity.getWorderNo());
        Date firstContactTime = null;
        if (worderRemarkLogEntityList != null && worderRemarkLogEntityList.size() > 0) {
            firstContactTime = worderRemarkLogEntityList.get(0).getCreateTime();
        } else {
            firstContactTime = new Date();
        }
        try {
            WorderInformationAttributeEntity worderInformationAttributeEntity =
                    worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationEntity.getWorderNo(), "push_updateFirstcontactTime", "ca");
            if (worderInformationAttributeEntity != null && worderInformationAttributeEntity.getAttributeValue().equals("0")) {
                CaApiResponse response = caApiService.pushFirstcontactTime(worderNo, worderInformationEntity.getCompanyOrderNumber(), firstContactTime);
                if (!response.getSuccess()) {
                    return R.error(Integer.parseInt(response.getCode()), response.getMessage());
                }
                worderInformationAttributeEntity.setAttributeValue("1");
                worderInformationAttributeDao.updateById(worderInformationAttributeEntity);
            }
            CaApiResponse response2 = caApiService.pushMeasureTime(worderNo,worderInformationEntity.getCompanyOrderNumber(),time);
            if(!response2.getSuccess()){
                return  R.error(Integer.parseInt(response2.getCode()), response2.getMessage());
            }
        } catch (IOException e) {
            return R.error(500, "调用长安接口异常");
        }

        // 广汽接口
        gacePushService.pushOrder(worderInformationEntity.getWorderNo(), WorkOrderStatusEnum.APPROVED, "已预约勘测");

        worderInformationDto.setConveyAppointTime(worderInformationVo.getConveyAppointTime());
        WorderRemarkLogEntity worderRemarkLog = new WorderRemarkLogEntity();
        worderRemarkLog.setUserId(principal.getUserId());
        WorderStatusVo worderStatus = worderOrderService.getWorderStatus(worderInformationVo.getWorderNo());
        worderRemarkLog.setMasterStatus(worderStatus.getWorderStatus());
        worderRemarkLog.setSubStatus(worderStatus.getWorderExecStatus());
        worderRemarkLog.setWorderNo(worderInformationVo.getWorderNo());
        if (StringUtils.isNotBlank(worderInformationVo.getNoInstallReason())) {
            SysDictionaryDetailEntity sysDictionaryDetailEntity = sysDictionaryDetailService.getById(worderInformationVo.getNoInstallReason());
            worderRemarkLog.setNoInstallReasonId(sysDictionaryDetailEntity.getId());
            worderRemarkLog.setContent(sysDictionaryDetailEntity.getDetailName() + "_" + worderInformationVo.getRemake() + " " + worderInformationVo.getConveyAppointTime());  //备注内容
        } else {
            worderRemarkLog.setContent(worderInformationVo.getRemake() + " " + worderInformationVo.getConveyAppointTime());
        }
        worderRemarkLog.setCreateTime(new Date());
        SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
        String title = formatDate.format(new Date()) + " " + principal.getUsername() + "预约勘测";
        worderRemarkLog.setTitle(title);
        Integer integer = 0;
        if (flowCommon.hasFlowByWorderNo(worderInformationVo.getWorderNo())) {
            //调用预约勘测流程
            ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderInformationVo.getWorderNo(), FlowConstant.ProcessCode.BookingSurv, FlowConstant.ProcessStatus.Y);
            // 流程调用失败直接返回
            if (!"0".equals(executeFlowResultPo.getCode())) {
                return R.error(executeFlowResultPo.getMsg());
            }
            integer = worderOrderService.updateAppointConvey(worderInformationDto);
        } else {
            integer = worderOrderService.updateConveyAppoint(worderInformationDto);
        }
        if (StringUtils.isNotBlank(worderRemarkLog.getContent())) {
            worderRemarkLogService.save(worderRemarkLog);
        }
        recodeWorderOperate(worderInformationVo.getWorderNo(), Constant.CONVEY, Constant.NOT_CONVEY, "勘测预约");

        worderBuffService.saveConveyFieldsInAheadWithDefaultValue(worderInformationVo.getWorderNo());

        if (integer.intValue() > 0) {
            return R.ok().putWorderNo(worderInformationVo.getWorderNo()).putWorderExecStatus(Constant.NOT_CONVEY)
                    .putWorderTriggerEvent(WarningConstant.CONVEY_APPOINT);
        } else {
            return R.error();
        }

    }

    // 获取勘测预约时间（得到倒计时）
    @PostMapping("/convey/time/get")
    @ApiOperation("获取勘测预约时间")
    public R getConveyAppointTime(String worderNo) {
        return R.ok().put("conveyAppointTime", worderOrderService.getConveyAppointTime(worderNo));
    }

    // 勘测签到（勘测图片）
    @Warning("勘测签到")
    @PostMapping("/convey/sign")
    @ApiOperation("勘测签到")
    public R conveySignIn(@RequestBody WorderInformationVo worderInformationVo) {
        if (StringUtils.isBlank(worderInformationVo.getConveySignArea())) {
            return R.error("手机GPS信号弱，请更换位置，重新定位");
        }
        recodeWorderOperate(worderInformationVo.getWorderNo(), Constant.CONVEY, Constant.CONVEY_NOT_COMMIT, "勘测签到");
        return worderOrderService.updateWorderConveySign(worderInformationVo);

    }

    // 获得勘测字段
    @PostMapping("/convey/field")
    @ApiOperation("获得勘测字段")
    public R listConveyField(String worderNo) {
        return R.ok().putList(worderOrderService.listConveyField(worderNo));
    }

    // 获得勘测资料字段
    @PostMapping("/convey/data")
    @ApiOperation("获得勘测资料字段")
    public R listConveyDataField(String worderNo) {
        return R.ok().putList(worderOrderService.listConveyDataField(worderNo));
    }

    /**
     * 字段保存
     * 服务兵 APP提交资料
     * @param fieldSaveVo
     * @return
     */
    @PostMapping("/field/info/save")
    @Transactional(rollbackFor = {Exception.class, Error.class})
    public R saveFieldInfo(@RequestBody FieldSaveVo fieldSaveVo) {
        List<FieldVo> fields = fieldSaveVo.getFields();
        String worderNo = null;
        if (fields != null && fields.get(0) != null && StringUtils.isNotBlank(fields.get(0).getWorderNo())) {
            worderNo = fields.get(0).getWorderNo();
        }
        for (FieldVo field : fields) {
            if (field.getFieldId().equals("1197") || field.getFieldId().equals("1711") || field.getFieldId().equals("1718")) {
                String time = field.getFieldValue();
                String str = time.substring(10, 11);
                if (str.equals("时")) {
                    time = time.substring(0, 10) + " 00:00:00";
                    field.setFieldValue(time);
                } else {
                    time = time.replace(":", " ");
                    time = time.substring(0, 13) + ":00:00";
                    field.setFieldValue(time);
                }
            }
        }
        Integer saveNum = 0;
        // 使用map去重
        Map<String, FieldVo> map = new HashMap<>();
        // 查找需要修改的fieldId
        List<String> fieldIdList = fields.stream().filter(item -> item != null && StringUtils.isNotBlank(item.getFieldId()))
                .map(item -> {
                            map.put(item.getFieldId(), item);
                            return item.getFieldId();
                        }
                ).collect(Collectors.toList());
        // map去重转List
        fields = new ArrayList<>(map.values());
        if (fieldIdList.size() > 0) {
            // 删除
            worderExtFieldService.remove(new QueryWrapper<WorderExtFieldEntity>().eq("worder_no", worderNo).in("field_id", fieldIdList));
            // 添加
            for (int i = 0; i < fields.size(); i++) {
                saveNum += worderOrderService.saveWorderFieldInfo(fields.get(i));
            }
            WorderInformationEntity entity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderNo).eq("is_delete", "0"));
            List<String> outSn = new ArrayList<>();
            List<SnStoreVo> snStoreVoList = new ArrayList<>();
            Map<String, String> storeMap = new HashMap<>();
            //判断工单是否是比亚迪标识的
            if (providerBusinessService.checkBydOrderByWorderId(entity.getWorderId())) {
                // 查询工单扩展字段
                List<WorderExtFieldEntity> worderExtFieldEntities = worderExtFieldService.list(new QueryWrapper<>(WorderExtFieldEntity.builder().worderNo(entity.getWorderNo()).build()));
                // 转Map
                Map<Integer, WorderExtFieldEntity> filedMap = worderExtFieldEntities.stream().collect(Collectors.toMap(WorderExtFieldEntity::getFieldId, Function.identity()));
                if (filedMap.containsKey(1723)) {
                    //存在客户自提桩
                    WorderExtFieldEntity fieldVo = filedMap.get(1723);
                    if (fieldVo.getFieldValue().equals("是")) {
                        if (map.get("1736") != null && StringUtils.isBlank(map.get("1736").getFieldValue())) {
                            return R.error("充电桩编码-勘测不能为空");
                        }
                        if (map.get("950") != null && StringUtils.isBlank(map.get("950").getFieldValue())) {
                            return R.error("充电桩编码不能为空");
                        }
                    } else {
                        if (map.get("1709") != null && StringUtils.isBlank(map.get("1709").getFieldValue())) {
                            return R.error("住宅类型不能为空");
                        }
                        if (map.get("1712") != null && StringUtils.isBlank(map.get("1712").getFieldValue())) {
                            return R.error("是否需要电力报装不能为空");
                        }
                    }
                }
                if (entity.getWorderTypeId() == 6) {
                    // 1697 售后类型 1698 标签
                    if (filedMap.containsKey(1697) && filedMap.containsKey(1698)) {
                        if (filedMap.get(1698).getFieldValue() != null && filedMap.get(1698).getFieldValue().equals("保内订单") && filedMap.get(1697).getFieldValue().equals("报修")) {
                            if (map.get("1714") != null && StringUtils.isBlank(map.get("1714").getFieldValue())) {
                                return R.error("是否换桩不能为空");
                            }
                        }
                    }
                    // 1714	是否换桩
                    if (filedMap.containsKey(1714)) {
                        if (filedMap.get(1714).getFieldValue() != null && StringUtils.isNotBlank(filedMap.get(1714).getFieldValue()) && filedMap.get(1714).getFieldValue().equals("是")) {
                            if (map.get("950") != null && StringUtils.isBlank(map.get("950").getFieldValue())) {
                                return R.error("充电桩编码不能为空");
                            }
                            if (map.get("1395") != null && StringUtils.isBlank(map.get("1395").getFieldValue())) {
                                return R.error("充电桩编码不能为空");
                            }
                        }
                    }
                    if (filedMap.containsKey(1717) && filedMap.containsKey(1698)) {
                        if (((filedMap.get(1698).getFieldValue() != null && filedMap.get(1698).getFieldValue().equals("保内订单")) || (filedMap.get(1698).getFieldValue() != null && filedMap.get(1698).getFieldValue().equals("保外订单")) && filedMap.get(1697).getFieldValue().equals("拆桩"))) {
                            if (map.get("1717") != null && StringUtils.isBlank(map.get("1717").getFieldValue())) {
                                return R.error("客户是否留桩不能为空");
                            }
                        }
                    }
                    if (filedMap.containsKey(1714)) {
                        if (filedMap.get(1714).getFieldValue() != null && filedMap.get(1714).getFieldValue().equals("是")) {
                            if (map.containsKey("1065")) {
                                if (map.get("1065") != null && StringUtils.isBlank(map.get("1065").getFieldValue())) {
                                    return R.error("返厂快递单号不能为空");
                                }
                            }
                        }
                    }
                }
            }
            List<WorderInformationAttributeEntity> list = worderInformationAttributeDao.selectList(
                    new QueryWrapper<WorderInformationAttributeEntity>().eq("attribute_code", "LeaveOrderNumber")
                            .eq("attribute", "NoOutbound").eq("is_delete", "0").eq("worder_id", entity.getWorderId())
            );
            List<String> snList = worderInformationDao.selectSnByWorderId(entity.getWorderId());
            // 是否为立柱
            boolean cloumn = false;
            // 无感出库
            boolean noOutbound = true;
            for (FieldVo field : fields) {
                // 1063,是否需要换桩
                if (field.getFieldId().equals("1063") && field.getFieldValue().equals("否")) {
                    // 不启用无感出库
                    noOutbound = false;
                    break;
                }
            }

            if (noOutbound) {
                for (FieldVo field : fields) {
                    if (field.getFieldId().equals("921") && field.getFieldValue().equals("自带立柱")) {
                        //安装方式为立柱
                        cloumn = true;
                    }
                    if (StringUtils.isNotBlank(field.getFieldType()) && field.getFieldType().equals("ButtonText")) {
                        String id = field.getFieldId();
                        ExtFieldEntity extFieldEntity = extFieldDao.selectOne(new QueryWrapper<ExtFieldEntity>().eq("field_id", id));
                        if (extFieldEntity.getIsNotnull() == 0) {
                            if (StringUtils.isBlank(field.getFieldValue())) {
                                //如果类型为无感出库，字段不是必填，判断是否有值，没值则跳过
                                continue;
                            }
                        }
                        if (StringUtils.isNotBlank(field.getFieldValue())) {
                            SnStoreVo snStoreVo = worderInformationDao.checkSnInfo(field.getFieldValue(), entity.getDotId());
                            if (snStoreVo == null) {
                                //不存在该sn
                                return R.error("该sn号在该网点内未查询到");
                            } else {
                                if (snStoreVo.getGoodsType() != null && !snStoreVo.getGoodsType().equals("4")) {
                                    return R.error("该sn号不为全新类型");
                                }
                                if (list.size() > 0) {
                                    String leaveNumCode = list.get(0).getAttributeValue();
                                    String leaveStatus = worderInformationDao.getLeaveStatus(leaveNumCode);
                                    if (leaveStatus.equals("0")) {
                                        boolean rule = false;
                                        for (String s : snList) {
                                            if (s.equals(snStoreVo.getSn())) {
                                                rule = true;
                                                break;
                                            }
                                        }
                                        if (!rule) {
                                            if (snStoreVo.getStatus() != null && !snStoreVo.getStatus().equals("1")) {
                                                //该SN状态不在在库状态
                                                return R.error("该sn号不为在库状态");
                                            }
                                        } else {
                                            if (snStoreVo.getStatus() != null && !snStoreVo.getStatus().equals("7")) {
                                                //该SN状态不在在库状态
                                                return R.error("该sn号不为预占状态");
                                            }
                                        }
                                    }
                                } else {
                                    boolean rule = false;
                                    for (String s : snList) {
                                        if (s.equals(snStoreVo.getSn())) {
                                            rule = true;
                                            break;
                                        }
                                    }
                                    if (!rule) {
                                        if (snStoreVo.getStatus() != null && !snStoreVo.getStatus().equals("1")) {
                                            //该SN状态不在在库状态
                                            return R.error("该sn号不为在库状态");
                                        }
                                    }
                                }
                            }
                            outSn.add(field.getFieldValue());
                            snStoreVoList.add(snStoreVo);
                            storeMap.put(snStoreVo.getStoreId(), snStoreVo.getSn());
                        }
                    }
                }
                if (outSn.size() > 0) {
                    Map<String, String> stringMap = new HashMap<>();
                    for (String s : outSn) {
                        stringMap.put(s, s);
                    }
                    if (stringMap.size() != outSn.size()) {
                        return R.error("输入的SN不能相同");
                    }
                    //校验sn+物料是否存在
                    if (entity != null) {
                        //包含无感出库字段
                        if (storeMap.size() > 1) {
                            return R.error("输入的多条sn号不在同一仓库内，无法出库");
                        }
                        List<Integer> storeId = worderInformationService.getStoreIdByWorderNo(worderNo, outSn);
                        if (list.size() > 0) {
                            //校验出库单是否还在预录状态
                            String leaveNumCode = list.get(0).getAttributeValue();
                            String leaveStatus = worderInformationDao.getLeaveStatus(leaveNumCode);
                            if (leaveStatus.equals("0")) {
                                //则存在属性表，校验
                                List<String> snNum = new ArrayList<>();
                                if (snList.size() > 0) {
                                    snNum = worderInformationDao.checkSnNum(outSn, entity.getWorderId());
                                }
                                if (snNum.size() != snList.size() || snNum.size() != outSn.size() || cloumn) {
                                    //进行修改sn
                                    //作废属性表信息
                                    worderInformationAttributeDao.updateDelete(entity.getWorderId(), "NoOutbound", "LeaveOrderNumber");
                                    //修改之前的SN状态为在库，出库单改为作废
                                    if (snList != null) {
                                        for (String s : snList) {
                                            //根据sn改为在库
                                            worderInformationDao.updateSnStatus(s);
                                        }
                                    }
                                    Integer id = worderInformationDao.getLeaveIdByNum(list.get(0).getAttributeValue());
                                    JSONObject invalidIntf = new JSONObject();
                                    invalidIntf.put("id", id);
                                    JSONObject InvalidStoreResp = storageBusiness.handleInvalidIntf(invalidIntf);
                                    if (InvalidStoreResp.getInteger("code") != 0) {
                                        OperationRecord operationRecord = new OperationRecord((long) entity.getServiceId(), entity.getServiceId().toString(), "无感出库-作废出库单失败-" + InvalidStoreResp.get("msg"), worderNo);
                                        operationRecord.setWorderStatus(Constant.INSTALL);
                                        operationRecord.setWorderExecStatus(Constant.INSTALL_NOT_UPLOAD_COMPANY);
                                        workMsgDao.insertOperation(operationRecord);
                                        return R.error("无感出库-作废出库单失败-" + InvalidStoreResp.get("msg"));
                                    }
                                    //根据现在的sn生成新的出库单
                                    String leaveNum = getLeaveCodeAndTime();
                                    JSONObject leaveStoreBody = getLeaveStoreBodyNo(entity.getWorderId(), entity.getWorderNo(), leaveNum, storeId.get(0));
                                    JSONArray goodsList = new JSONArray();
                                    if (cloumn) {
                                        //根据工单模板查询车企品牌id
                                        BrandPo brandPo = worderInformationDao.getBrandByWorderNo(entity.getWorderNo());
                                        //查询该网点库存是否存在该品牌物料
                                        List<SnStoreVo> storeVoList = worderInformationDao.getStoreInfoByBrandId(brandPo.getBrandId(), entity.getDotId(), storeId.get(0));
                                        if (!storeVoList.isEmpty() && storeVoList.size() > 0) {
                                            SnStoreVo snStoreVo = storeVoList.get(0);
                                            Integer exNum = worderInformationDao.getExNum(snStoreVo.getStoreId(), snStoreVo.getPositionId(), snStoreVo.getMaterielId());
                                            goodsList.add(getCloumnGoodsInfo(exNum, snStoreVo));
                                        } else {
                                            String storeName = worderInformationService.getStoreNameById(storeId.get(0));
                                            return R.error(storeName + "-该仓库内未查询到立柱信息");
                                        }
                                    }
                                    for (SnStoreVo snStoreVo : snStoreVoList) {
                                        worderInformationDao.updateSnStatusUse(snStoreVo.getSn());
                                        //根据物料查询库存等信息
                                        Integer exNum = worderInformationDao.getExNum(snStoreVo.getStoreId(), snStoreVo.getPositionId(), snStoreVo.getMaterielId());
                                        goodsList.add(getGoodsInfo(exNum, snStoreVo));
                                    }
                                    //保存详情表
                                    leaveStoreBody.put("goodsList", goodsList);
                                    leaveStoreBody.put("username", getUser().getUsername());
                                    JSONObject leaveStoreResp = storageBusiness.leaveStore(leaveStoreBody);
                                    // 生成出库单失败 记录操作日志
                                    if (leaveStoreResp.getInteger("code") != 0) {
                                        OperationRecord operationRecord = new OperationRecord((long) entity.getServiceId(), entity.getServiceId().toString(), "无感出库-生成出库单失败-" + leaveStoreResp.get("msg"), worderNo);
                                        operationRecord.setWorderStatus(Constant.INSTALL);
                                        operationRecord.setWorderExecStatus(Constant.INSTALL_NOT_UPLOAD_COMPANY);
                                        workMsgDao.insertOperation(operationRecord);
                                        return R.error("无感出库-生成出库单失败-" + leaveStoreResp.get("msg"));
                                    }
                                    //生成新的属性表信息
                                    worderInformationAttributeDao.insertOutBound(entity.getWorderId(), "无感出库出库单单号", leaveNum);
                                }
                            }
                        } else {
                            //不存在属性表，新建
                            //根据现在的sn生成新的出库单
                            String leaveNum = getLeaveCodeAndTime();
                            JSONObject leaveStoreBody = getLeaveStoreBodyNo(entity.getWorderId(), entity.getWorderNo(), leaveNum, storeId.get(0));
                            JSONArray goodsList = new JSONArray();
                            if (cloumn) {
                                //根据工单模板查询车企品牌id
                                BrandPo brandPo = worderInformationDao.getBrandByWorderNo(entity.getWorderNo());
                                //查询该网点库存是否存在该品牌物料
                                List<SnStoreVo> storeVoList = worderInformationDao.getStoreInfoByBrandId(brandPo.getBrandId(), entity.getDotId(), storeId.get(0));
                                if (!storeVoList.isEmpty() && storeVoList.size() > 0) {
                                    SnStoreVo snStoreVo = storeVoList.get(0);
                                    Integer exNum = worderInformationDao.getExNum(snStoreVo.getStoreId(), snStoreVo.getPositionId(), snStoreVo.getMaterielId());
                                    goodsList.add(getCloumnGoodsInfo(exNum, snStoreVo));
                                } else {
                                    String storeName = worderInformationService.getStoreNameById(storeId.get(0));
                                    return R.error(storeName + "-该仓库内未查询到立柱信息");
                                }
                            }
                            for (SnStoreVo snStoreVo : snStoreVoList) {
                                //根据物料查询库存等信息
                                Integer exNum = worderInformationDao.getExNum(snStoreVo.getStoreId(), snStoreVo.getPositionId(), snStoreVo.getMaterielId());
                                goodsList.add(getGoodsInfo(exNum, snStoreVo));
                            }
                            //保存详情表
                            leaveStoreBody.put("goodsList", goodsList);
                            leaveStoreBody.put("username", getUser().getUsername());
                            JSONObject leaveStoreResp = storageBusiness.leaveStore(leaveStoreBody);
                            // 生成出库单失败 记录操作日志
                            if (leaveStoreResp.getInteger("code") != 0) {
                                OperationRecord operationRecord = new OperationRecord((long) entity.getServiceId(), entity.getServiceId().toString(), "无感出库-生成出库单失败-" + leaveStoreResp.get("msg"), worderNo);
                                operationRecord.setWorderStatus(Constant.INSTALL);
                                operationRecord.setWorderExecStatus(Constant.INSTALL_NOT_UPLOAD_COMPANY);
                                workMsgDao.insertOperation(operationRecord);
                                return R.error("无感出库-生成出库单失败-" + leaveStoreResp.get("msg"));
                            }
                            for (SnStoreVo snStoreVo : snStoreVoList) {
                                worderInformationDao.updateSnStatusUse(snStoreVo.getSn());
                            }
                            //生成属性表信息
                            worderInformationAttributeDao.insertOutBound(entity.getWorderId(), "无感出库出库单单号", leaveNum);
                        }
                    }
                }
            } else {
                if (list.size() > 0) {
                    //校验出库单是否还在预录状态
                    String leaveNumCode = list.get(0).getAttributeValue();
                    String leaveStatus = worderInformationDao.getLeaveStatus(leaveNumCode);
                    if (leaveStatus.equals("0")) {
                        //进行修改sn
                        //作废属性表信息
                        worderInformationAttributeDao.updateDelete(entity.getWorderId(), "NoOutbound", "LeaveOrderNumber");
                        //修改之前的SN状态为在库，出库单改为作废
                        if (snList != null) {
                            for (String s : snList) {
                                //根据sn改为在库
                                worderInformationDao.updateSnStatus(s);
                            }
                        }
                        Integer id = worderInformationDao.getLeaveIdByNum(list.get(0).getAttributeValue());
                        JSONObject invalidIntf = new JSONObject();
                        invalidIntf.put("id", id);
                        JSONObject InvalidStoreResp = storageBusiness.handleInvalidIntf(invalidIntf);
                        if (InvalidStoreResp.getInteger("code") != 0) {
                            OperationRecord operationRecord = new OperationRecord((long) entity.getServiceId(), entity.getServiceId().toString(), "无感出库-作废出库单失败-" + InvalidStoreResp.get("msg"), worderNo);
                            operationRecord.setWorderStatus(Constant.INSTALL);
                            operationRecord.setWorderExecStatus(Constant.INSTALL_NOT_UPLOAD_COMPANY);
                            workMsgDao.insertOperation(operationRecord);
                            return R.error("无感出库-作废出库单失败-" + InvalidStoreResp.get("msg"));
                        }
                    }
                }
            }
        }
       /* WorderInfoDTO worderInfo = worderInformationDao.findWorderInfo(worderNo);
        worderTemplateDao.updateByWorderTempId(worderInfo.getTemplateId().toString(),fieldSaveVo.getIsOneself());*/
        try {

            // Result pushInstallationInfoResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).build(),
            // "pushInstallationInfo");
            // if (!pushInstallationInfoResult.getCode().equals(0)) {
            //     errs = pushInstallationInfoResult.getMsg();
            // }
            //调用长安接口
            WorderInformationAttributeEntity worderInformationAttributeEntity = worderInformationAttributeDao.selectAttributeByWorderNo(worderNo,
                    "push_canInstallOrders", "ca");
            if (worderInformationAttributeEntity != null && worderInformationAttributeEntity.getAttributeValue().equals("0")) {
                QueryWrapper<WorderInformationEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("worder_no", worderNo);
                WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(queryWrapper);
                CaApiResponse response = caApiService.pushCanInstallOrders(worderNo, worderInformationEntity.getCompanyOrderNumber(), 1, "1", null);
                if (!response.getSuccess() && !response.getMessage().contains("不能多次")) {
                    throw new RRException("请求长安接口失败" + response.getMessage());
                }
                worderInformationAttributeEntity.setAttributeValue("1");
                worderInformationAttributeDao.updateById(worderInformationAttributeEntity);
            }
        } catch (RRException e) {
            throw e;
        } catch (Exception e) {
            throw new RRException("请求长安接口失败");
        }
        log.info("worderNo:{}", worderNo);
        return R.ok().put("saveNum", saveNum);
    }

    private String getLeaveCodeAndTime() {
        Date date = new Date();
        String currDate = DateUtil.dateToString(date, DateUtil.LONG_DATE_FORMAT);
        String moveCodeAndTime = getNum(autidOrderMapper.getOutMoveCodeAndTime(currDate));
        return "OUT-" + DateUtil.dateToString(date, DateUtil.FORMAT_FOUR) + "-" + moveCodeAndTime;
    }

    private String getNum(Integer integer) {
        if (integer > 999999) {
            throw new RuntimeException();
        }
        return String.format("%06d", integer); //25为int型
    }

    private JSONObject getGoodsInfo(Integer exNum, SnStoreVo snStoreVo) {
        JSONObject goods = new JSONObject();
        goods.put("exNum", exNum);
        goods.put("expectOutNum", 1);
        goods.put("goodsId", Integer.parseInt(snStoreVo.getMaterielId()));
        goods.put("goodsType", 4);
        goods.put("leaveNum", 1);
        goods.put("storePositionId", Integer.parseInt(snStoreVo.getPositionId()));
        goods.put("materielSn", snStoreVo.getSn());
        return goods;
    }

    private JSONObject getCloumnGoodsInfo(Integer exNum, SnStoreVo snStoreVo) {
        JSONObject goods = new JSONObject();
        goods.put("exNum", exNum);
        goods.put("expectOutNum", 1);
        goods.put("goodsId", Integer.parseInt(snStoreVo.getMaterielId()));
        goods.put("goodsType", 4);
        goods.put("leaveNum", 1);
        goods.put("storePositionId", Integer.parseInt(snStoreVo.getPositionId()));
        return goods;
    }

    /**
     * 获取用户
     *
     * @return
     */
    public SysUserEntity getUser() {
        return (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
    }

    private JSONObject getLeaveStoreBodyNo(Integer worderId, String worderNo, String leaveNum, Integer storeId) {
        ReceiverInfoVo receiverInfo = getReceiverInfo(worderId);

        JSONObject body = new JSONObject();

        body.put("address", receiverInfo.getAddressDup());
        body.put("fromName", receiverInfo.getUserName());
        body.put("isManufacturerInStorage", "N");
        body.put("leaveNum", leaveNum);
        body.put("outScenario", "3");//出库类型为3 无感出库
        body.put("phone", receiverInfo.getUserPhone());
        body.put("storeId", storeId);
        body.put("worderId", worderNo);
        return body;
    }

    private JSONObject getLeaveStoreBody(Integer worderId, String worderNo, String leaveNum, Integer storeId, String outScenario) {
        ReceiverInfoVo receiverInfo = getReceiverInfo(worderId);

        JSONObject body = new JSONObject();

        body.put("address", receiverInfo.getAddressDup());
        body.put("fromName", receiverInfo.getUserName());
        body.put("isManufacturerInStorage", "N");
        body.put("leaveNum", leaveNum);
        body.put("outScenario", outScenario);
        body.put("phone", receiverInfo.getUserPhone());
        body.put("storeId", storeId);
        body.put("worderId", worderNo);
        return body;
    }

    private ReceiverInfoVo getReceiverInfo(Integer worderId) {
        ReceiverInfoVo receiverInfo = autidOrderMapper.getReceiverInfo(worderId);
        List<RegionCodeVo> maps = autidOrderMapper.selectReginCode();
        Integer regcode = null;
        String v = null;
        if (StringUtils.isNotBlank(receiverInfo.getAddressDup())) {
            String value = receiverInfo.getAddressDup();
            String[] region = value.split("_");
            String[] regions = Arrays.copyOfRange(region, 0, 2);
            for (String i : regions) {
                for (RegionCodeVo regionCodeVo : maps) {
                    if (i.equals(regionCodeVo.getName())) {
                        Integer reg = regionCodeVo.getId();
                        regcode = reg;
                        if (v != null) {
                            v = v + regcode;
                        } else {
                            v = regcode + ",";
                        }
                        break;
                    }
                }

            }
            receiverInfo.setAddressDup(v + "," + region[2] + region[3]);
        }
        return receiverInfo;
    }

    //资料保存
    @PostMapping("/data/info/save")
    @Transactional(rollbackFor = {Exception.class, Error.class})
    public R saveDataInfo(@RequestBody DataSaveVo dataSaveVo) {
        List<DataVo> fields = dataSaveVo.getFields();
        String worderNo = null;
        if (fields.size() == 0) {
            //需要先查询该阶段是否有资料需保存表
            WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(
                    new QueryWrapper<WorderInformationEntity>().eq("worder_no", dataSaveVo.getWorderNo()));
            fields = worderExtFieldService.getFieldIdByWorderNo(dataSaveVo.getWorderNo(), worderInformationEntity.getWorderStatus().toString());
            for (int i = 0; i < fields.size(); i++) {
                List<Integer> fieldValue = fields.get(i).getFieldValue();
                worderOrderService.saveWorderDataInfo(fields.get(i));
                //将之前的sys_file改为is_delete = 1  将现在的改为 is_delete = 0
                WorderExtFieldEntity worderExtFieldEntity = worderExtFieldService.getOne(new QueryWrapper<WorderExtFieldEntity>().eq("worder_no", fields.get(i).getWorderNo()).eq("field_Id", fields.get(i).getFieldId()));
                //根据主键id更新删除标识为1
                sysFilesMapper.update(null, new UpdateWrapper<BalanceFileEntity>().set("is_delete", "1").eq("obj_value", worderExtFieldEntity.getId()));
                //根据文件id更新删除标识为0
                if (fieldValue.size() > 0) {
                    sysFilesMapper.update(null, new UpdateWrapper<BalanceFileEntity>().set("is_delete", "0").set("obj_value", worderExtFieldEntity.getId()).in("file_id", fieldValue));
                }
            }
            return R.ok().put("saveNum", 0);
        }

        //是否移除电力报装完成时间
        Boolean isRemove = false;
        if (fields != null && fields.get(0) != null && StringUtils.isNotBlank(fields.get(0).getWorderNo())) {
            worderNo = fields.get(0).getWorderNo();
        }
        Integer saveNum = 0;
        Map<String, DataVo> map = new HashMap();
        List<String> fieldIdList = fields.stream().filter(item -> item != null && StringUtils.isNotBlank(item.getFieldId()))
                .map(item -> {
                            List<Integer> imgIds = item.getFieldValue();
                            if (imgIds != null) {
                                List<Integer> tempList = new ArrayList<>();
                                for (Integer imgId : imgIds) {
                                    if (imgId != null) {
                                        tempList.add(imgId);
                                    }
                                }
                                item.setFieldValue(tempList);
                            }
                            map.put(item.getFieldId(), item);
                            return item.getFieldId();
                        }
                ).collect(Collectors.toList());
        fields = new ArrayList<>(map.values());

        WorderInformationEntity entity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderNo).eq("is_delete", "0"));
        //判断工单是否是比亚迪标识的
        if (providerBusinessService.checkBydOrderByWorderId(entity.getWorderId())) {
            // 查询工单扩展字段
            List<WorderExtFieldEntity> worderExtFieldEntities = worderExtFieldService.list(new QueryWrapper<>(WorderExtFieldEntity.builder().worderNo(entity.getWorderNo()).build()));
            // 转Map
            Map<Integer, WorderExtFieldEntity> filedMap = worderExtFieldEntities.stream().collect(Collectors.toMap(WorderExtFieldEntity::getFieldId, Function.identity()));
            if (filedMap.containsKey(1723)) {
                //存在客户自提桩
                if (filedMap.get(1723).getFieldValue().equals("是")) {
                    if (map.containsKey("1724")) {
                        if (map.get("1724") != null && map.get("1724").getFieldValue() != null && map.get("1724").getFieldValue().size() <= 0) {
                            return R.error("自提桩申请单不能为空");
                        } else {
                            if (map.get("1724").getFieldValue().get(0) == null) {
                                return R.error("自提桩申请单不能为空");
                            }
                        }
                    }
                    if (map.containsKey("1725")) {
                        if (map.get("1725") != null && map.get("1725").getFieldValue() != null && map.get("1725").getFieldValue().size() <= 0) {
                            return R.error("充电桩序列码照片不能为空");
                        } else {
                            if (map.get("1725").getFieldValue().get(0) == null) {
                                return R.error("充电桩序列码照片不能为空");
                            }
                        }
                    }
                    if (map.containsKey("1726")) {
                        if (map.get("1726").getFieldValue() != null && map.get("1726").getFieldValue().size() <= 0) {
                            return R.error("行驶证/购车发票不能为空");
                        } else {
                            if (map.get("1726").getFieldValue().get(0) == null) {
                                return R.error("行驶证/购车发票不能为空");
                            }
                        }
                    }
                } else {
                    if (map.containsKey("1719")) {
                        if (map.get("1719").getFieldValue() != null && map.get("1719").getFieldValue().size() <= 0) {
                            return R.error("勘测结论不能为空");
                        } else {
                            if (map.get("1719").getFieldValue().get(0) == null) {
                                return R.error("勘测结论不能为空");
                            }
                        }
                    }
                    if (map.containsKey("1662")) {
                        if (map.get("1662").getFieldValue() != null && map.get("1662").getFieldValue().size() <= 0) {
                            return R.error("勘测确认书不能为空");
                        } else {
                            if (map.get("1662").getFieldValue().get(0) == null) {
                                return R.error("勘测确认书不能为空");
                            }
                        }
                    }
                }
            }
            if (entity.getWorderTypeId() == 6) {
                if (filedMap.containsKey(1697) && filedMap.containsKey(1698)) {
                    if (filedMap.get(1698).getFieldValue() != null && filedMap.get(1697).getFieldValue() != null && filedMap.get(1698).getFieldValue().equals("保内订单") && filedMap.get(1697).getFieldValue().equals("报修")) {
                        if (map.containsKey("1722")) {
                            if (map.get("1722") != null && (map.get("1722").getFieldValue() == null || map.get("1722").getFieldValue().size() <= 0)) {
                                return R.error("客户维修确认单不能为空");
                            } else {
                                if (map.get("1722").getFieldValue().get(0) == null) {
                                    return R.error("客户维修确认单不能为空");
                                }
                            }
                        }
                    }
                    if ((filedMap.get(1698).getFieldValue() != null && filedMap.get(1698).getFieldValue().equals("保外订单")) || (filedMap.get(1698).getFieldValue() != null && filedMap.get(1698).getFieldValue().equals("保内订单") && !filedMap.get(1697).getFieldValue().equals("报修"))) {
                        if (map.containsKey("1730")) {
                            if (map.get("1730") != null && (map.get("1730").getFieldValue() == null || map.get("1730").getFieldValue().size() <= 0)) {
                                return R.error("维修确认单不能为空");
                            } else {
                                if (map.get("1730").getFieldValue().get(0) == null) {
                                    return R.error("客户维修确认单不能为空");
                                }
                            }
                        }
                    }
                }
            }
        }
        if (fieldIdList.size() > 0) {
            // 删除
            worderExtFieldService.remove(new QueryWrapper<WorderExtFieldEntity>().eq("worder_no", worderNo).in("field_id", fieldIdList));
            // 添加
            for (int i = 0; i < fields.size(); i++) {
                List<Integer> fieldValue = fields.get(i).getFieldValue();

                saveNum += worderOrderService.saveWorderDataInfo(fields.get(i));
                //将之前的sys_file改为is_delete = 1  将现在的改为 is_delete = 0
                WorderExtFieldEntity worderExtFieldEntity = worderExtFieldService.getOne(new QueryWrapper<WorderExtFieldEntity>().eq("worder_no", fields.get(i).getWorderNo()).eq("field_Id", fields.get(i).getFieldId()));
                //根据主键id更新删除标识为1
                sysFilesMapper.update(null, new UpdateWrapper<BalanceFileEntity>().set("is_delete", "1").eq("obj_value", worderExtFieldEntity.getId()));
                //根据文件id更新删除标识为0
                if (fieldValue.size() > 0) {
                    sysFilesMapper.update(null, new UpdateWrapper<BalanceFileEntity>().set("is_delete", "0").set("obj_value", worderExtFieldEntity.getId()).in("file_id", fieldValue));
                }
            }
        }
        return R.ok().put("saveNum", saveNum);
    }

    // 修改工单字段和资料信息
    @PostMapping("/field/info/update")
    public R updateFieldInfo(@RequestBody FieldSaveVo fieldSaveVo) {
        List<FieldVo> fields = fieldSaveVo.getFields();
        Integer updateNum = 0;
        String worderNo = null;
        if (fields != null && fields.size() > Constant.INTEGER_ZERO) {
            worderNo = fields.get(0).getWorderNo();
        }
//        // 查询工单状态，工单状态为已保存，待提交资料，则为二次保存
//        Integer worderStatus = Integer.parseInt(worderOrderService.getWorderStatus(worderNo));
        for (int i = 0; i < fields.size(); i++) {
            updateNum += worderOrderService.updateWorderFieldInfo(fields.get(i));
        }
        return R.ok().put("updateNum", updateNum);
    }

    /**
     * 提交勘测信息审核（保存信息，修改工单状态）
     * PC 端 APP端都在使用
     * @param worderNo
     * @return
     */
    @Warning("勘测资料提交")
    @PostMapping("/convey/audit/submit")
    @ApiOperation("提交勘测信息审核")
    public R submitConveyAudit(String worderNo) {

        //校验图片是否存在未上传
        R result = worderExtFieldService.checkFieldRequired(worderNo, 2);
        if ((int) result.get("code") != 0) {
            return result;
        }


        List<WorderExtFieldEntity> fields = worderExtFieldService.getFieldsByWorderNo(worderNo);
        //是否需要电力报装
        WorderExtFieldEntity w1 = fields.stream().filter(w -> w.getFieldId().equals(1712)).findFirst().orElse(null);
        //是否是自提装
        WorderExtFieldEntity w3 = fields.stream().filter(w -> w.getFieldId().equals(1723)).findFirst().orElse(null);

        if(w1 !=null){
            WorderExtFieldEntity w2 = fields.stream().filter(w -> w.getFieldId().equals(1715)).findFirst().orElse(null);
            if(w2 != null){
                if("是".equals(w1.getFieldValue())){
                    if(StringUtils.isBlank(w2.getFieldValue())){
                        return R.error("电力报装完成时间不能为空");
                    }
                }
                if("否".equals(w1.getFieldValue())){
                    worderExtFieldService.setFieldValueNull(w2.getId());
                }
            }
        }

        // 修改工单表的勘测预约时间
        WorderExtFieldEntity worderExtFieldEntity = fields.stream().filter(w -> w.getFieldId().equals(1718)).findFirst().orElse(null);
        if (worderExtFieldEntity != null) {
            String fieldValue = worderExtFieldEntity.getFieldValue();
            if (StringUtils.isNotBlank(fieldValue)) {
                Map<String, Object> map = new HashMap<>();
                map.put("worderNo", worderNo);

                WorderExtFieldEntity w4 = fields.stream().filter(w -> w.getFieldId().equals(1718)).findFirst().orElse(null);

                //如果是自提装
                if(w3 != null && w4 != null && w3.getFieldValue().equals("是")){

                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime localDateTime = LocalDateTime.parse(w4.getFieldValue(), formatter).plusMinutes(-5);
                    String  installAppointTime = localDateTime.format(formatter);
                    map.put("installAppointTime", installAppointTime);   //修改安装预约时间
                    LocalDateTime time = LocalDateTime.parse(w4.getFieldValue(), formatter).plusMinutes(-10);
                    map.put("conveyAppointTime", time.format(formatter));   //修改勘测预约时间
                }else {
                    map.put("conveyAppointTime", fieldValue);   //修改勘测预约时间
                }
                worderInformationDao.updateAppointTime(map);
            }
        }
        //2服务商信息回传
        Result r1 = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).build(), "pushContactInfo");
        if (r1.getCode() != 0) {
            return R.error(r1.getCode(), r1.getMsg());
        }
        // 3勘测资料回传比亚迪
        Result providerBusinessResult;
        WorderInformationEntity worder = worderInformationService.getByWorderNo(worderNo);
        if (worder.getTemplateId()==836 || worder.getTemplateId()==837 || worder.getTemplateId()==838 || worder.getTemplateId()==840 || worder.getTemplateId()==841 || worder.getTemplateId()==842) {
            providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).build(), "pushSurveyInfo");
        } else {
            providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).build(), "pushSurveyInfoOld");
        }
        if (providerBusinessResult.getCode() != 0) {
            return R.error(providerBusinessResult.getCode(), providerBusinessResult.getMsg());
        }

        gacePushService.pushOrder(worderNo, WorkOrderStatusEnum.INPRG, "勘测信息提交审核");

        R r = R.ok().put("num", worderOrderService.submitConveyAduit(worderNo));
        recodeWorderOperate(worderNo, Constant.CONVEY, Constant.CONVEY_NOT_AUDIT, "提交勘测信息审核");
//        Integer id = brandMapper.selectBrandCarID(worderNo);
//        if(id==14){
//            sysDoloadService.queue(worderNo,2,0,0,2);
//        }
        return r.putWorderNo(worderNo).putWorderExecStatus(Constant.CONVEY_NOT_AUDIT)
                .putWorderTriggerEvent(WarningConstant.CONVEY_DATA_SUBMIT);
    }

    // 勘测签退离场
    @PostMapping("/convey/signout")
    @ApiOperation("勘测签退离场")
    public R conveySignOut(ConveySignOutVo conveySignOutVo) {
        worderOrderService.conveySignOut(conveySignOutVo);
        recodeWorderOperate(conveySignOutVo.getWorderNo(), Constant.CONVEY, null, "勘测签退离场");
        return R.ok();
    }


    // 修改安装预约时间也是安装预约
    @Warning("安装预约")
    @PostMapping("/install/appoint")
    @ApiOperation("修改安装预约时间也是安装预约")
    @Lock4j(keys = {"#worderInformationVo.worderNo"})
    public R updateInstallApoint(WorderInformationVo worderInformationVo) {
        Subject subject = SecurityUtils.getSubject();
        SysUserEntity principal = (SysUserEntity) subject.getPrincipal();
        WorderInformationDto worderInformationDto = new WorderInformationDto();
        worderInformationDto.setWorderNo(worderInformationVo.getWorderNo());
        worderInformationDto.setWorderStatus(2);
        worderInformationDto.setWorderExecStatus(FieldConstant.NOT_INSTALL);
        if (worderInformationVo.getInstallAppointTime() == null || worderInformationVo.getInstallAppointTime().trim().equals("") || worderInformationVo.getInstallAppointTime().equals("YYYY-MM-DD")) {
            return R.error("请填写预约时间");
        }
        String time = worderInformationVo.getInstallAppointTime();
        time = time.replace(":", " ");
        if (StringUtils.isNotBlank(time)) {
            time = time.substring(0, 13) + ":00:00";
            worderInformationVo.setInstallAppointTime(time);
        }
        if (StringUtils.isNotBlank(worderInformationVo.getNoInstallReason()) && worderInformationVo.getNoInstallReason().equals("-1")) {
            return R.error("请选择原因");
        }


        String processCode = "pushContactInfo";
        WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderInformationDto.getWorderNo()));
        if (worderInformationEntity.getWorderTypeId() == 6) {
            processCode = "aoPushContactInfo";
        }
        // 通知比亚迪系统
        Result providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderInformationDto.getWorderNo()).installAppointTime(worderInformationVo.getInstallAppointTime()).build(), processCode);
        if (providerBusinessResult.getCode() != 0) {
            return R.error(providerBusinessResult.getCode(), providerBusinessResult.getMsg());
        }
        //调用长安接口
        try {
            String visitTime = LocalDateTime.now().plusMinutes(1).format(DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN));
            CaApiResponse response2 = caApiService.pushVisitTime(worderInformationEntity.getWorderNo(), worderInformationEntity.getCompanyOrderNumber(), visitTime);
            if(!response2.getSuccess()){
                return  R.error(Integer.parseInt(response2.getCode()), response2.getMessage());
            }
        } catch (IOException e) {
            return R.error(500, "调用长安接口异常");
        }

        //调用广汽工单修改接口，修改工单状态
        gacePushService.pushOrder(worderInformationEntity.getWorderNo(), WorkOrderStatusEnum.APPROVED, "勘测安装中");

        if (StringUtils.isBlank(worderInformationVo.getWorderLevel())) {
            SysDictionaryDetailEntity sysDictionaryDetailEntity = sysDictionaryDetailService.getById(worderInformationVo.getNoInstallReason());
            worderInformationDto.setWorderLevel(sysDictionaryDetailEntity.getRemark());
        }
        worderInformationDto.setInstallAppointTime(worderInformationVo.getInstallAppointTime());
        WorderRemarkLogEntity worderRemarkLog = new WorderRemarkLogEntity();
        WorderStatusVo worderStatus = worderOrderService.getWorderStatus(worderInformationVo.getWorderNo());
        worderRemarkLog.setMasterStatus(worderStatus.getWorderStatus());
        worderRemarkLog.setSubStatus(worderStatus.getWorderExecStatus());
        worderRemarkLog.setWorderNo(worderInformationVo.getWorderNo());
        worderRemarkLog.setUserId(principal.getUserId());
        if (StringUtils.isNotBlank(worderInformationVo.getNoInstallReason())) {
            SysDictionaryDetailEntity sysDictionaryDetailEntity = sysDictionaryDetailService.getById(worderInformationVo.getNoInstallReason());
            worderRemarkLog.setNoInstallReasonId(sysDictionaryDetailEntity.getId());
            worderRemarkLog.setContent(sysDictionaryDetailEntity.getDetailName() + "_" + worderInformationVo.getRemake() + " " + worderInformationVo.getInstallAppointTime());  //备注内容
        } else {
            worderRemarkLog.setContent(worderInformationVo.getRemake() + " " + worderInformationVo.getInstallAppointTime());
        }
        worderRemarkLog.setCreateTime(new Date());
        SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
        String title = formatDate.format(new Date()) + " " + principal.getUsername() + "预约安装";
        worderRemarkLog.setTitle(title);
        int num;
        if (flowCommon.hasFlowByWorderNo(worderInformationVo.getWorderNo())) {
            //调用app安装预约流程
            ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderInformationVo.getWorderNo(), FlowConstant.ProcessCode.BookingFix, FlowConstant.ProcessStatus.Y);
            // 流程调用失败直接返回
            if (!"0".equals(executeFlowResultPo.getCode())) {
                return R.error(executeFlowResultPo.getMsg());
            }
            num = worderOrderService.updateAppointInstall(worderInformationDto);
        } else {
            num = worderOrderService.updateInstallAppoint(worderInformationDto);

        }
        if (StringUtils.isNotBlank(worderRemarkLog.getContent())) {
            worderRemarkLogService.save(worderRemarkLog);
        }
        R r = R.ok().put("num", num);
        recodeWorderOperate(worderInformationVo.getWorderNo(), Constant.INSTALL, Constant.NOT_INSTALL, "安装预约");
        r.putWorderNo(worderInformationVo.getWorderNo()).putWorderExecStatus(FieldConstant.NOT_INSTALL)
                .putWorderTriggerEvent(WarningConstant.INSTALL_APPOINT);

        worderBuffService.saveInstallFieldsInAheadWithDefaultValue(worderInformationVo.getWorderNo());
        return r;
    }

    // 安装签到
    @Warning("安装签到")
    @PostMapping("/install/sign")
    public R installSignIn(WorderInformationVo worderInformationVo) {
        if (StringUtils.isBlank(worderInformationVo.getInstallSignArea())) {
            return R.error("手机GPS信号弱，请更换位置，重新定位");
        }
        return worderOrderService.updateWorderInstallSign(worderInformationVo);
    }

    // 获得安装字段
    @PostMapping("/install/field")
    @ApiOperation("获得安装字段")
    public R listInstallField(String worderNo) {
        return R.ok().putList(worderOrderService.listInstallField(worderNo));
    }

    // 获得安装资料字段
    @PostMapping("/install/data")
    @ApiOperation("获得安装资料字段")
    public R listInstallDataField(String worderNo) {
        return R.ok().putList(worderOrderService.listInstallDataField(worderNo));
    }

    /**
     * 增值物料自动出库
     *
     * @param entity
     * @param userName
     * @return 失败信息
     */
    private String addMaterialAutoOutbound(WorderInformationEntity entity, String userName) {

        // 查询是否有配置增值物料
        List<Map<String, Object>> worderInformationAttributeMap = autidOrderMapper.queryAddMaterialConfig(entity.getWorderId());
        //未配置增值物料跳过增值物料出库
        if (worderInformationAttributeMap == null || worderInformationAttributeMap.size() <= 0) {
            return null;
        }

        // 查询是否自布线敷设方式
        Boolean isSelfWiring = false;
        Integer selfWiringCount = autidOrderMapper.isSelfWiringByWorderNo(entity.getWorderNo());
        if (selfWiringCount > 0) {
            isSelfWiring = true;
        }

        Integer storeId = null;
        // 查询网点使用的增值物料
        List<UsedMaterielVo> usedMaterielVoList = autidOrderMapper.queryUsedAddedMaterielByWorderId(entity.getWorderId());

        // 配置过增值物料必须添加增值物料
        if (usedMaterielVoList == null || usedMaterielVoList.size() <= 0) {
            // 自布线敷设方式 未添加物料直接跳过自动出库 非自布线敷设方式未添加增值物料进行拦截
            if (isSelfWiring) {
                return null;
            } else {
                return "未添加增值物料，请核查！";
            }
        }

        for (UsedMaterielVo usedMaterielVo : usedMaterielVoList) {
            for (Map<String, Object> worderInformationAttribute : worderInformationAttributeMap) {
                Integer materielId = Integer.valueOf((String) worderInformationAttribute.get("materiel_id"));
                if (usedMaterielVo.getMaterielId().equals(materielId)) {
                    Integer useNum = Integer.valueOf((String) worderInformationAttribute.get("use_num"));

                    // 判断自布线敷设方式 使用增值物料小于配置配置物料 跳过自动出库
                    if (isSelfWiring && usedMaterielVo.getUsedNum() < useNum) {
                        return null;
                        // 非自布线敷设方式 使用增值物料数量不能为0
                    } else if (!isSelfWiring && usedMaterielVo.getUsedNum() == 0) {
                        return "未添加增值物料，请核查！";
                    }
                }
            }
        }

        List<Integer> materielIdList = usedMaterielVoList.stream().map(UsedMaterielVo::getMaterielId).collect(Collectors.toList());
        List<DotPositionAddedMaterielStockVo> dotPositionAddedMaterielStockVoList = autidOrderMapper.queryDotPositionAddedMaterielStock(entity.getDotId(), materielIdList);

        Map<Integer, List<DotPositionAddedMaterielStockVo>> dotPositionAddedMaterielStockVoMap = dotPositionAddedMaterielStockVoList.stream().collect(Collectors.groupingBy(DotPositionAddedMaterielStockVo::getGoodsId, Collectors.toList()));

        JSONArray goodsList = new JSONArray();

        for (UsedMaterielVo usedMaterielVo : usedMaterielVoList) {

            if (!dotPositionAddedMaterielStockVoMap.containsKey(usedMaterielVo.getMaterielId())) {
                return "增值物料库存不足，请核查！";
            }
            DotPositionAddedMaterielStockVo dotPositionAddedMaterielStockVo = dotPositionAddedMaterielStockVoMap.get(usedMaterielVo.getMaterielId()).get(0);
            if (usedMaterielVo.getUsedNum() > dotPositionAddedMaterielStockVo.getRealGoodsTotal()) {
                return "增值物料库存不足，请核查！";
            }
            storeId = dotPositionAddedMaterielStockVo.getStoreId();
            goodsList.add(getGoodsInfo(usedMaterielVo, dotPositionAddedMaterielStockVo));

        }
        // 调用生产出库单接口
        String leaveNum = getLeaveCodeAndTime();
        JSONObject leaveStoreBody = getLeaveStoreBody(entity.getWorderId(), entity.getWorderNo(), leaveNum, storeId, "4");
        leaveStoreBody.put("goodsList", goodsList);
        leaveStoreBody.put("username", getUser().getUsername());
        JSONObject leaveStoreResp = storageBusiness.leaveStore(leaveStoreBody);
        // 生成出库单失败 记录操作日志
        if (leaveStoreResp.getInteger("code") != 0) {
            OperationRecord operationRecord = new OperationRecord((long) entity.getServiceId(), userName, "客服审核-自动出库-生成出库单失败-" + leaveStoreResp.get("msg"), entity.getWorderNo());
            operationRecord.setWorderStatus(Constant.INSTALL);
            operationRecord.setWorderExecStatus(Constant.INSTALL_NOT_UPLOAD_COMPANY);
            workMsgDao.insertOperation(operationRecord);
            return "自动出库-生成出库单失败-" + leaveStoreResp.get("msg");
        }
        Integer goodsLeaveInfoId = leaveStoreResp.getInteger("id");
        // 调用出库完成接口
        JSONObject handleOutCompleteBody = new JSONObject();
        handleOutCompleteBody.put("id", goodsLeaveInfoId);
        handleOutCompleteBody.put("leaveNum", leaveNum);
        handleOutCompleteBody.put("storeId", storeId);
        handleOutCompleteBody.put("username", getUser().getUsername());
        JSONObject handleOutCompleteResp = storageBusiness.handleOutComplete(handleOutCompleteBody);
        // 出库单出库完成失败 记录操作日志
        if (handleOutCompleteResp.getInteger("code") != 0) {
            OperationRecord operationRecord = new OperationRecord((long) entity.getServiceId(), userName, "客服审核-自动出库-出库单出库完成失败-" + leaveStoreResp.get("msg"), entity.getWorderNo());
            operationRecord.setWorderStatus(Constant.INSTALL);
            operationRecord.setWorderExecStatus(Constant.INSTALL_NOT_UPLOAD_COMPANY);
            workMsgDao.insertOperation(operationRecord);

            // 作废出库单
            autidOrderMapper.cancelLeaveInfo(leaveNum);
            autidOrderMapper.cancelLeaveOrder(leaveNum);
            return "自动出库-出库单出库完成失败-" + handleOutCompleteResp.get("msg");
        }
        // 自动生成出库单 -- end
        return null;
    }

    /**
     * 提交安装信息审核
     * PC APP
      * @param worderNo
     * @return
     */
    @Warning("提交安装信息审核")
    @PostMapping("/install/audit/submit")
    @ApiOperation("提交安装信息审核")
    public R submitInstallAudit(String worderNo) {

        //校验图片是否存在未上传
        R result = worderExtFieldService.checkFieldRequired(worderNo, 3);
        if ((int) result.get("code") != 0) {
            return result;
        }
        String time = "";
        String time2 = "";
        String time3 = "";
        // 只需修改状态就可以了
        WorderStatusVo worderStatusVo = new WorderStatusVo();
        worderStatusVo.setWorderNo(worderNo);
        //worderStatusVo.setWorderStatus(SysDictConstant.WORDER_STATUS_16);
        worderStatusVo.setWorderExecStatus(FieldConstant.INSTALL_NOT_AUDIT);
        WorderInformationEntity entity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderNo).eq("is_delete", "0"));
        List<WorderInformationAttributeEntity> list = worderInformationAttributeDao.selectList(
                new QueryWrapper<WorderInformationAttributeEntity>().eq("attribute_code", "InstallFixSubmit")
                        .eq("attribute", "FixDocSubmit").eq("is_delete", "0").eq("worder_id", entity.getWorderId())
        );
        boolean submitFlag = false;
        // 非比亚迪订单不调用后续逻辑
        WorderInformationAttributeEntity worderInformationAttributeEntity = worderInformationAttributeDao.selectAttributeByWorderNo(worderNo, "worder_source", "pushOrder");
        if (worderInformationAttributeEntity != null) {
            //比亚迪接口订单
            // 查询工单扩展字段
            List<WorderExtFieldEntity> worderExtFieldEntities = worderExtFieldService.list(new QueryWrapper<>(WorderExtFieldEntity.builder().worderNo(entity.getWorderNo()).build()));

            if (entity.getTemplateId()==836 || entity.getTemplateId()==837 || entity.getTemplateId()==838 || entity.getTemplateId()==840 || entity.getTemplateId()==841 || entity.getTemplateId()==842) {

                // 1262,敷设方式--用户自布线，线缆品牌传3自布线
                WorderExtFieldEntity cableBrand = worderExtFieldEntities.stream().filter(w -> w.getFieldId().equals(1262)).findFirst().orElse(null);
                //1712,是否需要电力报装
                WorderExtFieldEntity emeterRequestProgress = worderExtFieldEntities.stream().filter(w -> w.getFieldId().equals(1712)).findFirst().orElse(null);
                //1646,比亚迪-放弃电力报装免责声明
                WorderExtFieldEntity disclaimersImage = worderExtFieldEntities.stream().filter(w -> w.getFieldId().equals(1646)).findFirst().orElse(null);
                //1647,比亚迪-同级负载确认书
                WorderExtFieldEntity loadConfirmationImage = worderExtFieldEntities.stream().filter(w -> w.getFieldId().equals(1647)).findFirst().orElse(null);

                //放弃电力报装免责声明改为线缆品牌为自布线或是否电力报装为否 是必传；同级负载确认书改为当是否电力报装为否时必传
                if (cableBrand != null && "用户自布线".equals(cableBrand.getFieldValue())) {
                    if (disclaimersImage == null || StringUtils.isBlank(disclaimersImage.getFieldValue())) {
                        return R.error("放弃电力报装免责声明不能为空");
                    }
                }

                //是否电力报装为否时 放弃电力报装免责声明、同级负载确认书必传
                if (emeterRequestProgress != null && "否".equals(emeterRequestProgress.getFieldValue())) {
                    if (disclaimersImage == null || StringUtils.isBlank(disclaimersImage.getFieldValue())) {
                        return R.error("放弃电力报装免责声明不能为空");
                    }
                    if (loadConfirmationImage == null || StringUtils.isBlank(loadConfirmationImage.getFieldValue())) {
                        return R.error("同级负载确认书不能为空");
                    }
                }

            }

            WorderExtFieldEntity fieldEntity = worderExtFieldEntities.stream().filter(w -> w.getFieldId().equals(1197)).findFirst().orElse(null);
            if (fieldEntity != null && StringUtils.isNotBlank(fieldEntity.getFieldValue())) {
                worderStatusVo.setInstallSignOutTime(fieldEntity.getFieldValue());
            }
            // 转Map
            Map<Integer, WorderExtFieldEntity> filedMap = worderExtFieldEntities.stream().collect(Collectors.toMap(WorderExtFieldEntity::getFieldId, Function.identity()));
            if (filedMap.get(1723) != null && filedMap.get(1723).getFieldValue().equals("否")) {
                //非自提桩
                // 查询电缆的物料
                List<WorderUsedMaterielEntity> worderUsedMaterielEntities = worderUsedMaterielDao.selectListByWorderIdAndType(entity.getWorderId(), 53);
                if (worderUsedMaterielEntities == null || worderUsedMaterielEntities.isEmpty() || worderExtFieldEntities.size() <= 0) {
                    //未查询到
                    return R.error("非自提桩需添加电缆!");
                }
            }

            WorderExtFieldEntity field = worderExtFieldEntities.stream().filter(w->w.getFieldId().equals(1197)).findFirst().orElse(null);
            if (field != null) {
                //完成时间
//                time = filedMap.get(1197).toString();
                //安装完成时间
                time2 = field.getFieldValue();
                //勘测完成时间
                LocalDateTime dateTime = DateUtils.parseLocalDateTime(time2, "yyyy-MM-dd HH:mm:ss");
                if (dateTime != null) {
                    time = DateUtils.parseLocalDateTimeSting(dateTime.plusMinutes(-10), "yyyy-MM-dd HH:mm:ss");
                }
                WorderExtFieldEntity field2 = worderExtFieldEntities.stream().filter(w->w.getFieldId().equals(1712)).findFirst().orElse(null);
                    if (field2 != null && field2.getFieldValue().equals("是")) {
                        //电力报装完成时间 - 5
                        time3 = DateUtils.parseLocalDateTimeSting(dateTime.plusMinutes(-5), "yyyy-MM-dd HH:mm:ss");
                    }
            }



        }

        //回传长安信息
        try {
            WorderInformationEntity worder = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderNo));
            //增项物料
            IncreaseFeeDto increaseFee = worderInformationService.getIncreaseFee(worder.getWorderId());
            List<Map<String, Object>> details = increaseFee.getDetails();
            List<WorderMaterielVo> materiels = worderInformationService.getMateriel(worder.getWorderId());
            List<OverProof> overProofs = null;
            if (!materiels.isEmpty()) {
                overProofs = materiels.stream().map(m -> {
                    OverProof overProof = new OverProof();
                    //费用编码 费用名称
                    overProof.setPriceCode(m.getSku());
                    overProof.setPriceName(m.getMaterielName());
                    overProof.setUnit(m.getMaterielUnitValue());
                    overProof.setAmount(m.getNum());
                    Map<String, Object> map =
                            details.stream().filter(mm -> mm.get("materielName").toString().equals(m.getMaterielName())).findFirst().orElse(null);
                    if (map != null) {
                        overProof.setUnitPrice(new BigDecimal(map.get("balanceFee").toString()));
                    }
                    return overProof;
                }).collect(Collectors.toList());
            }

            WorderInformationAttributeEntity entity1 = worderInformationAttributeDao.selectAttributeByWorderNo(worderNo, "push_order_complete", "ca");
            if (entity1 != null && entity1.getAttributeValue().equals("0")) {
                CaApiResponse response2 = caApiService.pushOrdersComplete(worder.getWorderNo(), worder.getCompanyOrderNumber());
                if (!response2.getSuccess() && !response2.getMessage().contains("已安装完成,不可重复调用")) {
                    return R.error(Integer.valueOf(response2.getCode()), response2.getMessage());
                }
                entity1.setAttributeValue("1");
                worderInformationAttributeDao.updateById(entity1);
            }
            WorderInformationAttributeEntity entity2 =
                    worderInformationAttributeDao.selectAttributeByWorderNo(worderNo, "push_overProof", "ca");
            if (entity2 != null && entity2.getAttributeValue().equals("0")) {
                CaApiResponse response = caApiService.pushConfirmCanInstallOrders(worder.getWorderNo(), worder.getCompanyOrderNumber(), overProofs);
                if (!response.getSuccess()) {
                    return R.error(Integer.valueOf(response.getCode()), response.getMessage());
                }
                entity2.setAttributeValue("1");
                worderInformationAttributeDao.updateById(entity2);
            }
        } catch (IOException e) {
            return R.error("调用长安接口异常");
        }

        gacePushService.pushOrder(worderNo, WorkOrderStatusEnum.INPRG, "勘测安装中");

        if (list.isEmpty()) {
            try {
                //无感出库开始
                //查询属性表是否有出库单单号
                List<WorderInformationAttributeEntity> attributeEntityList = worderInformationAttributeDao.selectList(
                        new QueryWrapper<WorderInformationAttributeEntity>().eq("attribute_code", "LeaveOrderNumber")
                                .eq("attribute", "NoOutbound").eq("is_delete", "0").eq("worder_id", entity.getWorderId())
                );
                List<String> snList = worderInformationDao.getSnByWorderNo(worderNo);
                if (attributeEntityList != null && attributeEntityList.size() > 0) {
                    //校验出库单是否还在预录状态
                    String leaveNum = attributeEntityList.get(0).getAttributeValue();
                    String leaveStatus = worderInformationDao.getLeaveStatus(leaveNum);
                    if (leaveStatus.equals("0")) {
                        //存在无感出库
                        //校验sn是否还在预占状态
                        for (String s : snList) {
                            //查询可以为空
                            if (StringUtils.isNotBlank(s)) {
                                SnVo snVo = worderInformationDao.checkSnStatus(s, entity.getDotId());
                                if (snVo == null) {
                                    //未查询到
                                    return R.error("无感出库-SN号预占状态校验失败");
                                }
                            }
                        }
                    }
                }
                if (entity == null) {
                    return R.error("未查询到工单，请核查！");
                }

                String userName = workMsgDao.selectUserNameById(entity.getServiceId());
                // 自动生成出库单 -- start
                // 校验增值物料库存

                String err = addMaterialAutoOutbound(entity, userName);
                // 查询是否有配置增值物料
                List<Map<String, Object>> worderInformationAttributeMap = autidOrderMapper.queryAddMaterialConfig(entity.getWorderId());
                if (worderInformationAttributeMap != null && worderInformationAttributeMap.size() > 0) {
                    submitFlag = true;
                }
                if (StringUtils.isNotBlank(err)) {
                    return R.error(err);
                }

                Integer storeId = null;

                //无感出库开始
                //查询属性表是否有出库单单号
                if (attributeEntityList != null && attributeEntityList.size() > 0) {
                    submitFlag = true;
                    //校验出库单是否还在预录状态
                    String leaveNum = attributeEntityList.get(0).getAttributeValue();
                    String leaveStatus = worderInformationDao.getLeaveStatus(leaveNum);
                    if (leaveStatus.equals("0")) {
                        //调用出库完成接口
                        Integer LeaveInfoId = worderInformationDao.getLeaveIdByNum(leaveNum);
                        storeId = worderInformationDao.getStoreIdByNum(leaveNum);
                        JSONObject handleOutCompleteBody = new JSONObject();
                        handleOutCompleteBody.put("id", LeaveInfoId);
                        handleOutCompleteBody.put("leaveNum", leaveNum);
                        handleOutCompleteBody.put("storeId", storeId);
                        handleOutCompleteBody.put("username", getUser().getUsername());
                        JSONObject handleOutCompleteResp = storageBusiness.handleOutComplete(handleOutCompleteBody);
                        // 出库单出库完成失败 记录操作日志
                        if (handleOutCompleteResp.getInteger("code") != 0) {
                            OperationRecord operationRecord = new OperationRecord((long) entity.getServiceId(), userName, "无感出库-出库单出库完成失败-" + handleOutCompleteResp.get("msg"), worderNo);
                            operationRecord.setWorderStatus(Constant.INSTALL);
                            operationRecord.setWorderExecStatus(Constant.INSTALL_NOT_UPLOAD_COMPANY);
                            workMsgDao.insertOperation(operationRecord);
                            // 作废出库单
                            return R.error("无感出库-出库单出库完成失败-" + handleOutCompleteResp.get("msg"));
                        }
                        for (String s : snList) {
                            worderInformationDao.updateSnUse(s);
                        }
                    }
                    //无感出库结束
                }
//                Calendar date = Calendar.getInstance();
//                date.setTime(entity.getCreateTime());
//                Calendar target = Calendar.getInstance(); // 获取指定时间
//
//                // 设置指定时间为2023年7月14日0点0分0秒
//                target.set(Calendar.YEAR, 2023);
//                target.set(Calendar.MONTH, Calendar.JULY);
//                target.set(Calendar.DAY_OF_MONTH, 14);
//                target.set(Calendar.HOUR_OF_DAY, 0);
//                target.set(Calendar.MINUTE, 0);
//                target.set(Calendar.SECOND, 0);
//                if (date.after(target)){
                //首次生成新的属性表信息
                if (submitFlag) {
                    worderInformationAttributeDao.insertFixSubmit(entity.getWorderId(), "首次提交安装审核资料");
                }
                if (worderInformationAttributeEntity != null && StringUtils.isNotBlank(time) && StringUtils.isNotBlank(time2)) {

                    worderOrderService.updateWorderTime(worderNo, time, time2,time3);

                }

//                }
            } catch (Exception e) {
                e.printStackTrace();
                return R.error("提交安装信息失败");
            }
        }


        R r = R.ok().put("num", worderOrderService.updateWorderStatusInstallSubmit(worderStatusVo));
        recodeWorderOperate(worderNo, Constant.INSTALL, Constant.INSTALL_NOT_AUDIT, "提交安装信息审核");
        r.putWorderNo(worderNo).putWorderExecStatus(FieldConstant.INSTALL_NOT_AUDIT)
                .putWorderTriggerEvent(WarningConstant.INSTALL_DATA_SUBMIT);
//        Integer id = brandMapper.selectBrandCarID(worderNo);
//        if(id==14){
//            sysDoloadService.queue(worderNo,3,0,0,2);
//        }
        return r;
    }

    // 获取工单勘测审核信息
    @PostMapping("/convey/audit/info")
    public R listConveyAuditInfo(String worderNo) {
        return R.ok().putList(worderOrderService.listWorderConveyFieldInfo(worderNo))
                .put("auditResult", worderAuditResultService.getWorderAuditResult(worderNo, IntegerEnum.ONE.getValue()));
    }

    // 获得工单安装审核信息
    @PostMapping("/install/audit/info")
    public R listInstallAuditInfo(String worderNo) {
        return R.ok().putList(worderOrderService.listWorderInstallFieldInfo(worderNo))
                .put("auditResult", worderAuditResultService.getWorderAuditResult(worderNo, IntegerEnum.TWO.getValue()));
    }


    // 安装强制签退需要 理由和时间
    @PostMapping("/install/signout")
    public R installSignOut(InstallSignOutVo installSignOutVo) {
        R r = worderOrderService.installForceSignOut(installSignOutVo);
        recodeWorderOperate(installSignOutVo.getWorderNo(), Constant.INSTALL, null, "安装签退离场");
        return r;
    }


    // 工单开票
    public R getWorderBill() {
        return R.ok();
    }

    // 剩余安装签到时间
    @PostMapping("/install/time/get")
    public R getInstallAppointTime(String worderNo) {
        return R.ok().put("installAppointTime", worderOrderService.getInstallAppointTime(worderNo));
    }

    // 工单列表查询
    @PostMapping("/information/list")
    @ApiOperation("工单列表查询")
    public R listWorderInformation(PageVo pageVo) {
        Map map = JSONObject.parseObject(JSONObject.toJSONString(pageVo));
        SysUserEntity sysUserEntity = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        map.put("userId", sysUserEntity.getUserId());
        return R.ok().putList(worderOrderService.listWorderInformation(map)).put("total",
                worderOrderService.getWorderInformationCount(map));
    }

    @PostMapping("/information/queryTypeTitleData")
    @ApiOperation("工单列表查询")
    public R listWorderInformation() {
        Map map = new HashMap();
        SysUserEntity sysUserEntity = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        map.put("userId", sysUserEntity.getUserId());
        return R.ok().put("data", worderOrderService.queryTypeTitleData(map));
    }

    @RequestMapping("/dickey/get")
    @ApiOperation("根据字典ID获取字典数据")
    public R listDicKey(String dictionaryId) {
        return R.ok().putList(worderOrderService.listDicKey(dictionaryId));
    }


    @RequestMapping("/dickeys/get")
    @ApiOperation("根据字典ID数组获取字典数据")
    public R listDicKeys(List<String> ids) {
        return worderOrderService.listDicKeys(ids);
    }

    public boolean worderDataHasValue(List<DataVo> fields) {
        String worderNo = null;
        String fieldId = null;
        if (fields != null && fields.size() > Constant.INTEGER_ZERO) {
            worderNo = fields.get(0).getWorderNo();
            fieldId = fields.get(0).getFieldId();
        }
        Integer fieldInfoNum = worderOrderService.getWorderFieldInfoCount(worderNo, fieldId);
        if (fieldInfoNum > 0) {
            return true;
        } else {
            return false;
        }
    }

    private JSONObject getGoodsInfo(UsedMaterielVo usedMaterielVo, DotPositionAddedMaterielStockVo dotPositionAddedMaterielStockVo) {
        JSONObject goods = new JSONObject();
        goods.put("exNum", usedMaterielVo.getUsedNum());
        goods.put("expectOutNum", usedMaterielVo.getUsedNum());
        goods.put("goodsId", usedMaterielVo.getMaterielId());
        goods.put("goodsType", 4);
        goods.put("leaveNum", usedMaterielVo.getUsedNum());
        goods.put("storePositionId", dotPositionAddedMaterielStockVo.getStorePositionId());
        return goods;
    }


    public boolean worderExtFieldHasValue(List<FieldVo> fields) {
        String worderNo = null;
        String fieldId = null;
        if (fields != null && fields.size() > Constant.INTEGER_ZERO) {
            worderNo = fields.get(0).getWorderNo();
            fieldId = fields.get(0).getFieldId();
        }
        Integer fieldInfoNum = worderOrderService.getWorderFieldInfoCount(worderNo, fieldId);
        if (fieldInfoNum > 0) {
            return true;
        } else {
            return false;
        }
    }

    public void recodeWorderOperate(String worderNo, String worderStatus, String worderExecStatus, String result) {
        SysUserEntity sysUser = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        OperationRecord operationRecord = new OperationRecord(sysUser.getUserId(), sysUser.getUsername(), result, worderNo);
        operationRecord.setWorderStatus(worderStatus);
        operationRecord.setWorderExecStatus(worderExecStatus);
        //添加操作记录
        workMsgDao.insertOperation(operationRecord);
    }

    @PostMapping("/selectByTemplate")
    public R selectByTemplate(String worderNo) {
        WorderInformationEntity byId = worderInformationService.getById(worderNo);
        // todo xxt npe
        WorderTemplateDto templateInfoById = worderTemplateDao.findTemplateInfoById(byId.getTemplateId());
        if (StringUtils.isBlank(templateInfoById.getIsOneself())) {
            return R.result(200);
        } else if ("1".equals(templateInfoById.getIsOneself())) {
            return R.result(100);
        } else {
            return R.result(200);
        }
    }

    /**
     * 查询字典表里的固定物料名称
     *
     * @return
     */
    @PostMapping("/getMaterielName")
    public R getMaterielName() {
        List<FixedMaterialNameDto> list = materialInforDao.getMaterielName();
        return R.ok().put("materielNameList", list);
    }

    @PostMapping("/getMaterielBrand")
    public R getMaterielBrand(String materielName, String worderId) {
        List<FixedMaterialBrandDto> materielBrand = materialInforDao.getMaterielBrand(materielName, worderId);
        return R.ok().put("materielBrandList", materielBrand);
    }

    @PostMapping("/getMaterielSpec")
    public R getMaterielSpec(String materielName, String materielBrandValue, String worderId) {
        List<FixedMaterielSpecDto> materielSpec = materialInforDao.getMaterielSpec(materielName, materielBrandValue, worderId);
        return R.ok().putList(materielSpec);
    }

    @PostMapping("/getFixDocSubmit")
    public R getFixDocSubmit(String worderId) {
        List<WorderInformationAttributeEntity> list = worderInformationAttributeDao.selectList(
                new QueryWrapper<WorderInformationAttributeEntity>().eq("attribute_code", "InstallFixSubmit")
                        .eq("attribute", "FixDocSubmit").eq("is_delete", "0").eq("worder_id", worderId)
        );
        if (list.size() > 0) {
            return R.ok().put("data", "true");
        } else {
            return R.ok().put("data", "false");
        }
    }

    @PostMapping("/getFixDocSubmitByWorderNo")
    public R getFixDocSubmitByWorderNo(String worderId) {
        WorderInformationEntity entity = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderId).eq("is_delete", "0"));
        List<WorderInformationAttributeEntity> list = worderInformationAttributeDao.selectList(
                new QueryWrapper<WorderInformationAttributeEntity>().eq("attribute_code", "InstallFixSubmit")
                        .eq("attribute", "FixDocSubmit").eq("is_delete", "0").eq("worder_id", entity.getWorderId())
        );
        if (list.size() > 0) {
            return R.ok().put("data", "true");
        } else {
            return R.ok().put("data", "false");
        }
    }
}
