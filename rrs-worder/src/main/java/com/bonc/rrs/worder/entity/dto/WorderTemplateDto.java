package com.bonc.rrs.worder.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyibo on 2020-03-13 16:48
 */

@Data
public class WorderTemplateDto implements Serializable {
    // 工单模版id
    private Integer id;
    // 工单模版编号
    private Integer templateId;
    // 工单模版名称
    private String templateName;
    // 品牌ID
    private String brandId;
    // 品牌名称
    private String brandName;
    // 结算方式
    private String settleWay;
    //是否支持自动派单：0：支持，1：不支持，默认支持
    private Integer autoSend;
    //服务类型
    private Integer serviceTypeEnum;

    private String settleWayId;
    //工单ID
    private Integer worderId;




    public void setSettleWayId(String settleWayId) {
        this.settleWayId = settleWayId;
        this.settleWay = settleWayId;
    }

    // 物料套包ID
    private String suiteId;
    // 物料套包名称
    private String suiteName;
    // 工单规则-车企ID
    private String  worderAutoCompanyBalanceId;
    // 工单规则-车企名称
    private String worderAutoCompanyBalanceName;
    // 工单规则-网点ID
    private String worderBranchBalanceId;
    // 工单规则-网点名称
    private String worderBranchBalanceName;
    // 增项规则-网点ID
    private String addBranchBalanceId;
    // 增项规则-网点名称
    private String addBranchBalanceName;
    // 增项规则-用户ID
    private String addUserBalanceId;
    // 增项规则-用户名称
    private String addUserBalanceName;
    /**
     * 质押金比例(%)-车企
     */
    private Integer companyPledgeMoneyPercentage;
    /**
     * 质押金比例(%)-网点
     */
    private Integer dotPledgeMoneyPercentage;

    //用户本人签字 1：是  ，2：否
    private String isOneself;

}
