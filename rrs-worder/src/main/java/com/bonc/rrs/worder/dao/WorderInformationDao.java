package com.bonc.rrs.worder.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bonc.rrs.balanceprocess.vo.NoInstallReasonVo;
import com.bonc.rrs.bydbaobiao.domain.BydBaoBiaoResult;
import com.bonc.rrs.bydbaobiao.domain.BydBaobiaoParam;
import com.bonc.rrs.supervise.entity.SuperviseInfomation;
import com.bonc.rrs.supervise.entity.SuperviseOperationRecord;
import com.bonc.rrs.worder.dto.dto.*;
import com.bonc.rrs.worder.dto.vo.*;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.AreaInfoDTO;
import com.bonc.rrs.worder.entity.dto.CustormerWorderNumDto;
import com.bonc.rrs.worder.entity.dto.WorderInfoDTO;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.entity.po.BrandPo;
import com.bonc.rrs.worder.entity.po.CollecDetailPo;
import com.bonc.rrs.worder.entity.po.WorderTemplatePo;
import com.bonc.rrs.worder.entity.vo.CSCockpitVo;
import com.bonc.rrs.worder.entity.vo.UserTransferVo;
import com.bonc.rrs.worderapp.Vo.SnStoreVo;
import com.bonc.rrs.worderapp.Vo.SnVo;
import com.bonc.rrs.worderapp.entity.po.FieldPo;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDicEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 工单信息持久层
 *
 * <AUTHOR>
 */
@Mapper
public interface WorderInformationDao extends BaseMapper<WorderInformationEntity> {

    /**
     * 未开票
     * @param brandId
     * @param templateId
     * @return
     */
    List<WorderInformationEntity> listNoInvoiceWorderByBrandOrTemplate(@Param("brandId") Integer brandId,
                                                                       @Param("templateId") Integer templateId);

    /**
     * 已开票待记账
     * @param brandId
     * @param templateId
     * @return
     */
    List<WorderInformationEntity> listInvoiceNoAcsWorderByBrandOrTemplate(@Param("brandId") Integer brandId,
                                                                       @Param("templateId") Integer templateId);

    List<WorderInformationEntity> listWorderByInovice(@Param("companyInvoiceNo") String companyInvoiceNo);


    Integer getCountByCompanyOrderNumberAndBrand(@Param("companyOrderNumber") String companyOrderNumber,
                                                 @Param("brandId") String brandId);

    /**
     * 获取工单信息
     * @param pageNotI
     * @param params
     * @return
     */
    IPage<WorderInfoEntity> getWorderInformation(IPage<WorderInfoEntity> pageNotI, @Param("p") Map<String, Object> params);

    /**
     * 客服获取工单信息
     * @param pageNotI
     * @param params
     * @return
     */
    IPage<WorderInfoEntity> getListCustomer(Page<WorderInfoEntity> pageNotI, @Param("p") Map<String, Object> params);

    /**
     * 查询多个用户角色的不同工单列表的色标数量
     * @param params
     * @return
     */
    Integer getColorLabelCount(Map<String, Object> params);
    /**
     * 服务经理获取工单信息
     * @param pageNotI
     * @param params
     * @return
     */
    IPage<WorderInfoEntity> getListPM(Page<WorderInfoEntity> pageNotI, @Param("p") Map<String, Object> params);

    /**
     * 服务兵获取工单信息
     * @param pageNotI
     * @param params
     * @return
     */
    IPage<WorderInfoEntity> getListService(Page<WorderInfoEntity> pageNotI, @Param("p") Map<String, Object> params);


    /**
     * 网点获取工单信息
     * @param pageNotI
     * @param params
     * @return
     */
    IPage<WorderInfoEntity> getListBranch(Page<WorderInfoEntity> pageNotI,  @Param("p")Map<String, Object> params);

    /**
     * 获取所有工单信息
     * @param pageNotI
     * @param params
     * @return
     */
    IPage<WorderInformationEntity> getOrderAllList(Page<WorderInformationEntity> pageNotI,  @Param("p")Map<String, Object> params);

    /**
     * 获取今日超时工单信息
     * @param pageNotI
     * @param params
     * @return
     */
    IPage<WorderInformationEntity> queryWorderOvertimeList(Page<WorderInformationEntity> pageNotI,  @Param("p")Map<String, Object> params);

    List<ExportWorderOvertimeVo> exportWorderOvertimeList(@Param("p")Map<String, Object> params);

    List<WorderInformationEntity> queryWorderOvertimeListNoLimit(@Param("p")Map<String, Object> params);
    /**
     * 获取所有合作网点工单信息
     * @param pageNotI
     * @param params
     * @return
     */
    IPage<WorderInformationEntity> getOrderThirdList(Page<WorderInformationEntity> pageNotI,  @Param("p")Map<String, Object> params);


    /**
     * 根据品牌车型筛选匹配的工单模板
     * @param params
     * @return
     */
    List<WorderTemplatePo> getWorderTemplate(@Param("p") Map<String,Object> params);
    /**
     * 根据品牌车型筛选匹配的工单模板
     * @param params
     * @return
     */
    List<WorderTemplateDto> getWorderTemplateInfos(@Param("p") Map<String,Object> params);

    /**
     * 查询工单模板字段
     * @param template
     * @return
     */
    List<ExtFieldEntity> getExtField(Integer template);
    /**
     * 查询品牌
     * @param template
     * @return
     */
    BrandPo getBrand(Integer template);

    /**
     * 根据工单编号获取品牌
     * @param worderNo
     * @return
     */
    BrandPo getBrandByWorderNo(String worderNo);

    /**
     * 查询车型
     * @param template
     * @return
     */
    List<String> getCarType(Integer template);

    /**
     * 查询模板工单类型
     * @param template
     * @return
     */
    List<WorderTypeEntity> getWorderType(Integer template);

    /**
     * 查询地区
     * @param template
     * @return
     */
    List<BizRegionVo> getBizRegion(Integer template);

    /**
     * 查询模板结算对象
     * @param templateId
     * @return
     */
    SettleObjectVo getSettleObject(Integer templateId);

    /**
     * 根据worderId获取工单信息
     * @param worderId
     * @return
     */
    WorderInfoEntity getByWorderId(Integer worderId);
    /**
     * 根据worderId获取工单物料信息
     * @param worderId
     * @return
     */
    List<WorderMaterielVo> getListMateriel(Integer worderId);

    CompanyInformationEntity getByCompanyId(Integer companyId);

    /**
     * 更新数据
     * @param worderExtField
     */
    void updateWorderExtField(@Param("p") WorderExtFieldEntity worderExtField);

    /**
     * 更新执行状态
     * @param map
     * @return
     */
    boolean updateWorderExecStatus(@Param("p") Map<String,Object> map);

    boolean updateWorderFinishTime(@Param("p") Map<String,Object> map);

    /**
     * 更新主状态
     * @param map
     * @return
     */
    boolean updateWorderStatus(@Param("p") Map<String,Object> map);

    /**
     * 更新工单结算状态
     * @param map
     * @return
     */
    boolean updateWorderSetStatus(@Param("p") Map<String,Object> map);

    /**
     * 查询操作记录
     * @param worderNo
     * @return
     */
    List<String> getOperationRecord(String worderNo);


    /**
     * 查询第三方平台操作记录
     * @param worderNo
     * @return
     */
    List<String> getThirdOperationRecord(String worderNo);

    /**
     * 获取网点id
     * @param userId
     * @return
     */
    Integer getDotId(Long userId);

    /**
     * 获取网点名称
     * @param userName
     * @return
     */
    String getDotName(@Param("userName") String userName);

    /**
     * 厂商未支付的最早的发票开票时间
     * @param companyId
     * @return
     */
    LocalDate getNoPayVendorDate(Integer companyId);

    /**
     * 查询对应厂商已完成但未收款的工单金额
     * @param companyId
     * @return
     */
    BigDecimal getFinishNoPay(Integer companyId);

    /**
     * 查询对应厂商未完成的工单的套包价格
     * @param companyId
     * @return
     */
    BigDecimal getNoFinishSuitPrice(Integer companyId);

    /**
     * 工单类型下拉框数据
     * @return
     */
    List<WorderTypeDto> listWorderType();

    /**
     * 查询工单结算费用明细
     * @param worderId
     * @return
     */
    List<FeeDetailDto> getFeeDetail(Integer worderId);

    /**
     * 查询增项费用
     * @param worderId
     * @return
     */
    List<IncreaseFeeDto> getIncreaseFee(Integer worderId);

    /**
     * 查询收款明细
     * @param worderId
     * @return
     */
    CollecDetailPo queryCollecDetailByWorderId(@Param("worderId") Integer worderId);

    void addWorderInfo(WorderInfoEntity worderInfo);

    String getFactoryName(@Param("code") String code);

    void  updateWorderInfo(WorderInfoEntity worderInfo);
    /** 更新开票状态  */
    int updateOpenTicketStatus(@Param("worderNo") String worderNo,@Param("ticketStatus") int ticketStatus);
    /** 批量更新开票状态 */
    int updateMoreVoteCountingStatus(@Param("worderNOs") List<String> worderNOs,@Param("ticketStatus") int ticketStatus);
    int updateMoreVoteCounting(@Param("worderNOs") List<String> worderNOs,@Param("ticketStatus") int ticketStatus,
        @Param("viewUrl") String viewUrl, @Param("pdfUnsignedUrl") String pdfUnsignedUrl);

    /** 通过工单编号获取区域名称  */
    List<AreaInfoDTO> findAreaNameByWorderNO(@Param("worderNOs") List<String> worderNOs, @Param("type") Integer type);
    /** 获取工单信息 */
    WorderInfoDTO findWorderInfo(@Param("worderNo") String worderNo);
    /** 获取工单信息 */
    WorderInfoDTO findWorderInfoByOrderNo(@Param("orderNo") String OrderNo);

    int updateWorderInfoAmount(@Param("worderNo") String worderNo,@Param("discountAmount") BigDecimal discountAmount,@Param("userActualCost") BigDecimal userActualCost);
    /** 通过开票的流水获取工单信息 */
    WorderInfoDTO findWorderInfoBySerialNo(String serialNo);

    /**
     * 预约服务，预约勘测，预约安装等
     * @param map
     */
    void updateAppointTime(@Param("p") Map<String,Object> map);

    void updateTimeAppoint(@Param("p") Map<String,Object> map);

    /**
     * 首次电联用户时间
     * @param map
     */
    void updateFirstCallTime(@Param("p") Map<String,Object> map);

    /**
     * 电力报桩
     * @param map
     */
    void updatePowerPile(@Param("p") Map<String,Object> map);

    /**
     * 设置网点奖惩
     * @param worderPmStimulateVo
     */
    void addStimulate(WorderPmStimulateVo worderPmStimulateVo);

    /**
     * 修改网点奖惩
     * @param worderPmStimulateVo
     */
    void updateStimulate(WorderPmStimulateVo worderPmStimulateVo);


    /**
     *奖惩对象类型获取
     */
    List<Map<String,Object>> getStimulateType();

    /**
     *日日顺奖惩类型，即激励原因
     */
    List<Map<String,Object>> getStimulateReason();

    /**
     *日日顺奖惩类型，即负激励原因
     */
    List<Map<String,Object>> getStimulateNegativeReason();

    /**
     *网点奖惩类型，即激励原因
     */
    List<Map<String,Object>> getStimulateDotReason();

    /**
     *网点奖惩类型，即负激励原因
     */
    List<Map<String,Object>> getStimulateDotNegativeReason();

    List<Map<String,Object>> listStimulateReason(String dicName);

    /**
     * 激励列表
     * @param
     * @return
     */
    List<WorderPmStimulateVo> getStimulateList(WorderPmStimulateVo worderPmStimulateVo);

    /**
     * 激励id列表
     * @param
     * @return
     */
    List<Integer> getStimulateIdList(@Param("ids") List<Integer> ids);

    List<WorderPmStimulateExportVo> getStimulateListNoLimit(WorderPmStimulateVo worderPmStimulateVo);
    //网点管理员可以查看的激励列表
    List<WorderPmStimulateVo> getStimulateToIstrator(WorderPmStimulateVo worderPmStimulateVo);

    List<WorderPmStimulateExportVo> getStimulateToIstratorNoLimit(WorderPmStimulateVo worderPmStimulateVo);
    //查询当前用户的网点Id
    Integer getUserDotId(@Param("userId") Long userId);
    Integer getStimulateCount(WorderPmStimulateVo worderPmStimulateVo);
    Integer getStimulateCountToIstrator(WorderPmStimulateVo worderPmStimulateVo);

    /**
     * 激励详情信息
     * @param stimulateId
     * @return
     */
    WorderPmStimulateVo getStimulateInfo(String stimulateId);

    Integer queryStimulateByIdAndStatus(@Param("id") Integer id, @Param("status") Integer status);

    Integer queryPublishByBatchNoAndStatus(@Param("batchNo") String batchNo, @Param("batchStatus") Integer batchStatus);

    /**
     * 修改激励状态
     * @param worderPmStimulateVo
     */
    void updateStimulateStatus(WorderPmStimulateVo worderPmStimulateVo);

    /**
     * 撤销激励
     * @param worderPmStimulateVo
     */
    void updateStimulateCancel(WorderPmStimulateVo worderPmStimulateVo);

    /**
     * 客户满意度回访
     * @param clientSatisfactionVo
     */
    void addClientSatisfaction(ClientSatisfactionVo clientSatisfactionVo);

    /**
     * 根据userId获取对应的员工名称
     * @param userId
     * @return
     */
    String getUserName(Long userId);

    /**
     * 根据工单状态去获取字典表中对应值
     * @param worderStatus
     * @return
     */
    String getStatusName(@Param("worderStatus") Integer worderStatus);

    /**
     * 新增工单物料时获取物料名称
     * @return
     */
    List<Map<String,Object>> getMateriel();

    /**
     * 根据物料id获取该物料的品牌和规格
     * @param materielId
     * @return
     */
    Map<String,Object> getMaterielBrand(@Param("materielId") Integer materielId);

    Map<String,Object> getMaterielById(@Param("materielId") Integer materielId);
    /**
     * 修改工单物料时的回显
     * @param materielId
     * @param worderId
     * @return
     */
    Map<String,Object> getMaterielInfo(@Param("materielId") Integer materielId,@Param("worderId") Integer worderId);


    /**
     * 根据工单编号获取工单字段信息
     * @param worderNo
     * @return
     */
    List<FieldPo> listWorderFieldByWorderNo(String worderNo);

    /**
     * 获取开关状态
     * @param key
     * @return
     */
    Integer getSwitchStatus(@Param("key") String key);

    /**
     * 修改开关状态
     * @param key
     */
    void updateSwitchStatus(@Param("status") Integer status, @Param("key") String key);

    void deleteQuota();

    void addQuota(@Param("list") List<CustomerGuaranteeQuotaEntity> quota);

    /**
     * 获取360额度
     * @param ccusCode
     * @return
     */
    CustomerGuaranteeQuotaEntity getQuota(@Param("ccusCode") String ccusCode,@Param("cdepCode") String cdepCode);

    /**
     * 获取业务系统中已经达到“开票状态”的订单总额
     * @param params
     * @return
     */
    BigDecimal getCompanyFeeSum(@Param("p") Map<String,Object> params);

    /**
     * 获取要发送通知的邮箱
     * @param noticeType
     * @return
     */
    List<String> getMailList(@Param("noticeType") String noticeType);

    /**
     * 获取360的4月之前的逾期额度
     * @param ccusCode
     * @param cdepCode
     * @return
     */
    BigDecimal getBeforAprilIdue(@Param("ccusCode") String ccusCode,@Param("cdepCode") String cdepCode);

    /**
     * 获取处理工单数量最少的客服
     *
     * @return
     */
    List<CustormerWorderNumDto> getCustormerWorderNum(@Param("brandId") String brandId);

    /**
     * 根据区域和品牌查询客服
     */
    List<SysUserEntity> getCustormerWorderNumByArea(@Param("regcode")String regcode, @Param("brandId")String branId,@Param("serviceType")Integer serviceType);

    /**
     * 查询某个客服的处理工单数量
     * @param userId
     * @return
     */
    CustormerWorderNumDto getNumBycustormerId(@Param("custormerId") Long userId,@Param("brandId") String brandId);

    /**
     * 新增一条客服处理的工单数量
     * @param custormerWorderNumDto
     */
    void insertWorderNum(CustormerWorderNumDto custormerWorderNumDto);

    /**
     * 修改客服处理的工单数量
     * @param custormerWorderNumDto
     */
    void updateWorderNum(CustormerWorderNumDto custormerWorderNumDto);

    List<String> getCompanyId(@Param("brandId")String brandId);

    Integer getCompanyType(@Param("companyId") String company);

    List<String> findWoderNoByIncreStatus(@Param("worderIncreStatus") Integer worderIncreStatus);

    /**
     * 查询用户增项费用开票状态
     * @param worderId
     * @return
     */
    Integer getInvoiceStatus(@Param("worderId") Integer worderId);


    List<ManagerAreaBrandDto> getUserAreaBrand(Long userId);

    int updateWorderInformation(@Param("worderNo") String worderNo);

    int updateWorderInformationIncreStatus(@Param("worderId") Integer worderId);



    /**
     * 员工离职后工单转移
     * @param userTransferVo
     * @return
     */
    int userTransfer(UserTransferVo userTransferVo);

    /**
     * 员工离职后督办单转移
     * @param userTransferVo
     * @return
     */
    int userSuperviseTransfer(UserTransferVo userTransferVo);

    /**
     * 员工离职后督办单来电记录转移
     * @param userTransferVo
     * @return
     */
    int userSuperviseTeleTransfer(UserTransferVo userTransferVo);

    List<SuperviseInfomation> querySuperviseTransfer(UserTransferVo userTransferVo);

    Integer querySuperviseByWorderNo(@Param("worderNo") String worderNo);

    List<String> queryWorderNoTransfer(UserTransferVo userTransferVo);

    int insertSuperviseOperationRecord(SuperviseOperationRecord superviseOperationRecord);
    /**
     * 获取未结算开票的工单
     * @param worderNo
     * @return
     */
    List<WorderInformationEntity> getNoInvoicedWorder(@Param("worderNo") String worderNo);

    /**
     * 根据工单模板查询车企定价方式
     * @param templateId
     * @return
     */
    Integer getCompanyPriceTypeByTemplateId(@Param("templateId") Integer templateId);

    /**
     * 根据工单编号获取工单状态信息
     * @param worderNo
     * @return
     */
    List<WorderInformationEntity> getWorderStatusByWorderNo(@Param("worderNo") String worderNo);

    /**
     * 根据工单id获取工单状态信息
     * @param worderId
     * @return
     */
    WorderInformationEntity getWorderStatusByWorderId(@Param("worderId") Integer worderId);

    /**
     * 查询车企订单号是否重复
     * @param companyOrderNo
     * @param worderNo
     * @return
     */
    List<WorderInformationEntity> queryCompanyOrderNumber(@Param("companyOrderNumber") String companyOrderNo, @Param("worderNo")String worderNo);


    int updateStatusWorder(@Param("worderNo")String worderNo);


    /**
     * 根据工单Id查询品牌
     * @param worderId
     * @return
     */
    Integer selectBrandByTemp(@Param("worderId") Integer worderId);

    /**
     * 根据网点ID查询关联的品牌
     * @param dotId
     * @return
     */
    List<Integer> selectBrandsByDot(@Param("dotId") Integer dotId);


    List<Map<Integer, Object>> getAddedMaterielTypeList();

    List<ExtFieldEntity> getDotRuleExtFieldList();


    List<Map<Integer, Object>> getDotBalanceRuleMaterielInfo(@Param("dotBalanceRuleId")Integer dotBalanceRuleId);

    /**
     * 导出运营看板超时数据查询
     * @param params
     * @return
     */
    List<ExportCSCockpitDataVo> queryExportCSCockpitData(@Param("p") Map<String, Object> params);

    /**
     * 超时数据报表总览
     * @param params
     * @return
     */
    List<CSCockpitVo> queryCSCockpitListData(@Param("p") Map<String, Object> params);

    /**
     * 查询驾驶舱看板数据
     * @param params
     * @return
     */
    Map<String, Object> queryCockpitData(@Param("p") Map<String, Object> params);
    /**
     * 查询驾驶舱看板今日超时数据
     * @param params
     * @return
     */
    Map<String, Object> queryCockpitCSData(@Param("p") Map<String, Object> params);

    /**
     * 查询驾驶舱看板今日工单
     * @param params
     * @return
     */
    Map<String, Object> queryCockpitJRData(@Param("p") Map<String, Object> params);

    /**
     * 查询驾驶舱看板明日工单
     * @param params
     * @return
     */
    Map<String, Object> queryCockpitMRData(@Param("p") Map<String, Object> params);

    /**
     * 查询驾驶舱看板后日工单
     * @param params
     * @return
     */
    Map<String, Object> queryCockpitHRData(@Param("p") Map<String, Object> params);
    /**
     * 查询驾驶舱看板权限配置
     * @return
     */
    List<SysDictionaryDetailEntity> queryCockpitPermission();


    List<CockpitDataStatisticsDto> queryCockpitDataDetail(@Param("p") Map<String, Object> params);

    List<CockpitDataStatistics2Dto> queryCockpitDataDetail2(@Param("p") Map<String, Object> params);



    void deleteByStimulateId(@Param("stimulateId")String stimulateId);

	List<NoInstallReasonVo> getNoInstallReasonList();

	List<NoInstallReasonVo> getWorderLevelList();

    /**根据车企订单号查询 是否存在订单 */
    WorderInformationEntity isExsitWorder(@Param("companyOrderNumber") String companyOrderNumber);

    WorderInformationEntity getPresurveyWorder(@Param("userPhone") String userPhone, @Param("postcode") String postcode, @Param("brandId") Integer brandId);

    List<Integer> getStoreIdByWorderNo(@Param("worderNo") String worderNo,@Param("outSn") List<String> outSn);

    SnStoreVo checkSnInfo(@Param("sn") String sn, @Param("dotId") Integer dotId);

    String getStoreNameById(@Param("storeId")Integer storeId);

    List<SnStoreVo> getStoreInfoByBrandId(@Param("brandId") String brandId, @Param("dotId") Integer dotId,@Param("storeId")Integer storeId);

    Integer getExNum(@Param("storeId") String storeId,@Param("positionId") String positionId,@Param("materielId") String materielId);

    void updateSnStatus(@Param("sn") String sn);

    void updateSnUse(@Param("sn") String sn);

    List<String> selectSnByWorderId(@Param("worderId") Integer woderId);

    void updateLeaveOrder(@Param("leaveNum") String leaveOrder);

    void updateLeaveInfo(@Param("leaveNum") String leaveOrder);

    Integer getLeaveIdByNum(@Param("leaveNum")String leaveNum);

    Integer getStoreIdByNum(@Param("leaveNum")String leaveNum);

    SnVo checkSnStatus(@Param("sn")String sn,@Param("dotId")Integer dotId);

    List<String> getSnByWorderNo(@Param("worderNo")String worderNo);

    String getLeaveStatus(@Param("leaveNum")String leaveNum);

    void updateSnStatusUse(@Param("sn") String sn);

    List<String> checkSnNum(@Param("outSn")List<String> sn,@Param("worderId") Integer worderId);

    ClientSatisfactionVo getSatisfactionByWorderNo(@Param("worderNo") String worderNo);

    void updateClientSatisfaction(ClientSatisfactionVo clientSatisfactionVo);

    /**
     * 获取超时负激励检测开关 Y：开起，N：关闭
     * @return
     */
    String getCockpitCSNegativeStimulateSwitch();

    /**
     * 获取超时负激励扣减金额
     * @return
     */
    String getCockpitCSNegativeStimulateMoney();

    /**
     * 获取超时负激励检测环境
     * @return
     */
    List<SysDicEntity> getCockpitCSNegativeStimulateLink();

    /**
     * 超时负激励检测工单类型（包含）
     * @return
     */
    String getCockpitCSNegativeStimulateCheckWorderType();

    /**
     * 超时负激励检测品牌（排除）
     * @return
     */
    String getCockpitCSNegativeStimulateCheckBrand();

    List<CockpitStatisticsDto> queryCockpitStatistics(@Param("p") Map<String, Object> params);

    void callGenerateKanbanRealtime();

    IPage<WorderInformationEntity> companyCancelAuditQueryAll(Page<WorderInformationEntity> pageNotI,  @Param("p")Map<String, Object> params);

    IPage<WorderInformationEntity> companyReviewFailedQueryAll(Page<WorderInformationEntity> pageNotI,  @Param("p")Map<String, Object> params);

    void updateMaterial(@Param("worderNo") String worderNo);

    List<Integer> selectCaWorderInfo();

    List<BydBaoBiaoResult> selectpjazsxList(@Param("param") BydBaobiaoParam param);


    List<BydBaoBiaoResult> selectazjslList(@Param("param") BydBaobiaoParam param);

    List<BydBaoBiaoResult> selectazlList(@Param("param") BydBaobiaoParam param);

    WorderInformationEntity getDotInfoByWorderNo(@Param("worderNo") String worderNo);

    List<BydBaoBiaoResult> selectpjazsxDotList(@Param("param") BydBaobiaoParam param);


    List<BydBaoBiaoResult> selectazjslDotList(@Param("param") BydBaobiaoParam param);


    List<BydBaoBiaoResult> selectazlDotList(@Param("param") BydBaobiaoParam param);

}
