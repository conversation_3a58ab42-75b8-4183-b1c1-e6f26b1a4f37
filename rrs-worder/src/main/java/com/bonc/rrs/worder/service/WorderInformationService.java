package com.bonc.rrs.worder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.bydbaobiao.domain.BydBaobiaoParam;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.worder.dto.dto.ExtFieldDictionaryDto;
import com.bonc.rrs.worder.dto.dto.FeeDetailDto;
import com.bonc.rrs.worder.dto.dto.IncreaseFeeDto;
import com.bonc.rrs.worder.dto.dto.WorderTypeDto;
import com.bonc.rrs.worder.dto.vo.*;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.WorderRemarkLogEntity;
import com.bonc.rrs.worder.entity.dto.WorderInfoDTO;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.entity.po.BrandPo;
import com.bonc.rrs.worder.entity.po.CarTypePo;
import com.bonc.rrs.worder.entity.po.WorderTemplatePo;
import com.bonc.rrs.worder.entity.vo.CSCockpitVo;
import com.bonc.rrs.worder.entity.vo.ColorLabelCount;
import com.bonc.rrs.worder.entity.vo.UserTransferVo;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 工单信息业务层
 *
 * <AUTHOR>
 */
public interface WorderInformationService extends IService<WorderInformationEntity> {

    String worderNoMaker(String abbreviation);

    /**
     * 获取品牌信息
     * @return
     */
    List<BrandPo> getBrandList(Integer templateId);
    /**
     * 获取品牌信息
     * @return
     */
    List<BrandEntity> getBrandListInfo(Integer templateId);

    List<BrandPo> listBrands();

    List<BrandEntity> listBrandInfos();

    /**
     * 获取支持导入的品牌列表
     * @return
     */
    List<BrandEntity> listImportBrandInfos();

    /**
     * 根据品牌查询车型
     * @param brandId
     * @return
     */
    List<CarTypePo> getCarTypeList(String brandId);

    /**
     * 根据品牌车型筛选匹配的工单模板
     * @return
     */
    List<WorderTemplatePo> getWorderTemplate(Map<String,Object> params);
    /**
     * 工单详情信息 根据品牌车型筛选匹配的工单模板
     * @return
     */
    List<WorderTemplatePo> worderTemplate(Map<String,Object> params);
    /**
     * 根据品牌车型筛选匹配的工单模板
     * @return
     */
    List<WorderTemplateDto> getWorderTemplateInfos(Map<String,Object> params);

    /**
     * 根据模板id获取工单扩展字段
     * @param templateId
     * @return
     */
    WorderInformationVo getWorderFieldList(Integer templateId);
    /**
     * 根据工单编号获取工单字段信息
     * @param worderNo
     * @return
     */
    WorderInformationVo getWorderExtInformation(String worderNo);

    /**
     * 校验所选厂商是否能够创建工单
     * @param company
     */
    CheckResultVo checkCompany(CheckResultVo company);

    /**
     * 验证车企订单号
     * @param companyOrderNumber
     * @return
     */
    boolean validateCompanyOrderNumberExsit(String companyOrderNumber);

    /**
     * 验证车企订单号+工单模版品牌
     * @param companyOrderNumber
     * @return
     */
    boolean validateCompanyOrderNumberAndBrandExsit(String companyOrderNumber, Integer templateId);

    boolean validateCompanyOrderNumberAndBrandExsitByBrandId(String companyOrderNumber, String brandId);

    /**
     * 新增工单信息
     * @param worderInformation
     */
    String saveWorderInformation(WorderInfoEntity worderInformation,Map<Integer, WorderTemplateDto> templateDtoMap);

    /**
     * 推送工单入库
     * @param worderInformation
     * @return
     */
    R saveWorderInformationByServiceProvider(WorderInfoEntity worderInformation);
    /**
     * 批量导入工单信息
     * @param worderInformation
     */
    R importBatchSaveWorderInformation(WorderInfoEntity worderInformation);

    /**
     * 查询工单信息
     * @param params
     * @return
     */
    PageUtils queryList(Map<String, Object> params);

    /**
     * 查询多个用户角色的不同工单列表的色标数量
     * @return
     */
    ColorLabelCount getColorLabelCount();

    PageUtils queryPage(Map<String, Object> params);
    /**
     *
     * 项目经理查询
     */
    PageUtils queryPagePM(Map<String, Object> params);

    /**
     *
     * 网点查询
     */
    PageUtils queryPageBranch(Map<String, Object> params);

    /**
     *
     * 服务兵查询
     */
    PageUtils queryService(Map<String, Object> params);

    /**
     * 查询所有工单
     * @param params
     * @return
     */
    PageUtils queryAll(Map<String, Object> params);

    PageUtils companyCancelAuditQueryAll(Map<String, Object> params);

    PageUtils companyReviewFailedQueryAll(Map<String, Object> params);

    PageUtils queryWorderOvertime(Map<String, Object> params);

    List<ExportWorderOvertimeVo> exportWorderOvertime(Map<String, Object> params);
    /**
     * 查询所有工单
     * @param params
     * @return
     */
    PageUtils queryThird(Map<String, Object> params);

    /**
     * 根据worderId获取工单信息
     * @param worderId
     * @return
     */
    WorderInfoEntity getByWorderId(Integer worderId);

    WorderInformationEntity getByWorderNo(String worderNo);

    WorderInformationEntity getByCompanyWorderNo(String companyWorderNo);

    /**
     * 检查工单是否存在
     * @param worderNo
     * @return true 工单存在
     */
    Boolean checkExists(String worderNo);

    /**
     * 查询物料信息
     * @param worderId
     * @return
     */
    List<WorderMaterielVo> getMateriel(Integer worderId);

    /**
     * 查询操作记录
     * @param worderNo
     * @return
     */
    List<String> queryOperationRecord(String worderNo);

    /**
     * 查询第三方平台操作记录
     * @param worderNo
     * @return
     */
    List<String> queryThirdOperationRecord(String worderNo);

    /**
     * 查询工单备注
     * @param worderNo
     * @return
     */
    List<WorderRemarkLogEntity> queryRemarkLog(String worderNo);

    /**
     * 工单类型下拉框数据
     * @return
     */
    List<WorderTypeDto> listWorderType();

    /**
     * 查询工单结算费用明细
     * @param worderId
     */
    List<FeeDetailDto> getFeeDetail(Integer worderId);

    /**
     * 查询用户增项费用
     * @param worderId
     */
    IncreaseFeeDto getIncreaseFee(Integer worderId);

    WorderInfoEntity handleRedundant(WorderInfoEntity worderInformation);
    /**
     * 更新工单信息
     * @param worderInformation
     */
    R updateWorderInformation(WorderInfoEntity worderInformation);

    /**
     * 更新状态
     * @param worderId
     * @param flag
     */
    String updateStatus(Integer worderId,Integer flag);

    /**
     * 取消服务
     * @param worderRemarkLog
     */
    void cancelService(WorderRemarkLogEntity worderRemarkLog);

    /**
     * 取消服务
     * @param worderNo
     * @param companyOrderNumber
     */
    void cancelWorder(String worderNo, String companyOrderNumber);

    /**
     * 根据工单id查询收款明细
     * @param worderId
     * @return
     */
    R queryCollecDetailByWorderId(Integer worderId);

    /**
     * 预约服务，预约勘测，预约安装等
     * @param appointServiceVo
     */
    R updateAppointTime(AppointServiceVo appointServiceVo);

    /**
     * 首次电联用户时间
     * @param firstCallTimeVo
     */
    void updateFirstCallTime(FirstCallTimeVo firstCallTimeVo);

    /**
     * 电力报桩
     */
    void powerPile(PowerPileVo powerPileVo);

    /**
     *设置网点奖惩
     */
    void setStimulate(WorderPmStimulateVo worderPmStimulateVo);

    /**
     *修改网点奖惩
     */
    void updateStimulate(WorderPmStimulateVo worderPmStimulateVo);

    /**
     *奖惩对象类型获取
     */
    List<Map<String,Object>> listStimulateType();

    /**
     *奖惩类型，即激励原因获取
     */
    List<Map<String,Object>> listStimulateReason(String dicName);

    R getStimulateReason();

    R getStimulateList(WorderPmStimulateVo worderPmStimulateVo);

    List<WorderPmStimulateExportVo> getExportStimulateList(WorderPmStimulateVo worderPmStimulateVo);

    List<WorderPmStimulateExportVo> getExportStimulateListFromSlave(WorderPmStimulateVo worderPmStimulateVo);

    /**
     * 撤销激励
     * @param worderPmStimulateVo
     * @return
     */
    R cancelStimulate(WorderPmStimulateVo worderPmStimulateVo);

    WorderPmStimulateVo getStimulateInfo(String stimulateId);

    Integer queryStimulateByIdAndStatus(Integer id, Integer status);

    Integer queryPublishByBatchNoAndStatus(String batchNo, Integer batchStatus);

    void updateStimulateStatus(WorderPmStimulateVo worderPmStimulateVo);

    /**
     * 客户满意度回访
     * @param clientSatisfactionVo
     */
    void addClientSatisfaction(ClientSatisfactionVo clientSatisfactionVo);

    /**
     * 新增工单物料时获取物料名称
     * @return
     */
    List<Map<String,Object>> listMateriel();
    /**
     * 根据物料id获取该物料的品牌和规格
     * @param materielId
     * @return
     */
    Map<String,Object> getMaterielBrandSpec(Integer materielId);
    /**
     * 修改工单物料时的回显
     * @param materielId
     * @param worderId
     * @return
     */
    Map<String,Object> getMaterielInfo(Integer materielId,Integer worderId);

    /**
     * 获取开关状态
     * @return
     */
    Integer getSwitchStatus();

    /**
     * 修改开关状态
     */
    void updateSwitchStatus(Integer status);

    /**
     * 添加360额度数据
     */
    void addGuaranteeQuota();

    /**
     * 查询字典值
     * @param dicNumber
     * @return
     */
    List<ExtFieldDictionaryDto> queryStatus(String dicNumber);

    /**
     *勘测物料以及后续结算处理
     */
    R materielBalanceAccounts(Integer worderId);

    /**
     * 员工离职后工单转移
     * @param userTransferVo
     * @return
     */
    R userTransfer(UserTransferVo userTransferVo);

    R incentiveImport(MultipartFile file, Integer[] fileIds) throws IOException;

    R incentiveCarImport(MultipartFile file, Integer[] fileIds) throws IOException;

    /**
     * 获取未结算开票工单
     * @param worderNo
     * @return
     */
    List<WorderInformationEntity> getNoInvoicedWorder(String worderNo);

    R selectWorderInformationByWorderNo(String worderNo);

    /**
     * 校验工单类型
     * @param worderId
     * @return
     */
    boolean checkBanlanRule(Integer worderId);

    Integer getCompanyPriceTypeByTemplateId(Integer templateId);

    Integer updateWorderStatus(String worderNo);

    List<Map<Integer, Object>> getListDotName();

    Integer selectBrandByTemp(Integer worderId);

    List<Integer> selectBrandsByDot(Integer dotId);

    List<Map<Integer, Object>> getAddedMaterielTypeList();

    R getDotRuleExtFieldList();

    List<Map<Integer, Object>> getDotBalanceRuleMaterielInfo(Integer dotBalanceRuleId);

    /**
     * 查询超时数据报表
     * @param params
     * @return
     */
    List<CSCockpitVo> queryCSCockpitListData(Map<String, Object> params);

    List<ExportCSCockpitDataVo> exportCSCockpitData(Map<String, Object> params);

    List<Map<String, Object>> queryCockpitData(Map<String, Object> params);

    R queryCockpitDetailData(Map<String, Object> params);

    R queryCockpitDetailData2(Map<String, Object> params);

    R getNoInstallReasonList();

    R getWorderLevelList();

    Results goAutoSendWorder(String worderNo, String username, String conveyWorderNo);
	
	List<Integer> getStoreIdByWorderNo(String woderNo,List<String> outSn);

    String getStoreNameById(Integer storeId);

    ClientSatisfactionVo getSatisfactionByWorderNo(String worderNo);

    R cancelAuditReviewFailed(Integer worderId , String content);

    R companyFallback(Integer worderId, String content);

    R companyCancelAuditNotice(Integer worderId);

    /**
     * 是否有转单标识
     * @param worderNo
     * @return
     */
    Boolean checkTransferOrder(String worderNo);

    /**
     * 回退修改物料
     * @param worderNo
     * @return
     */
    String updateMaterial(String worderNo, Integer type);

    List<WorderInformationEntity> getInvoicedWordersByWorderNoList(List<String> allWorderNos);

    List<WorderInformationEntity> getFinishedWorders(List<String> allWorderNos);

    /**
     * 回退到安装资料无误待上传车企
     */
    void rollbackWorderStatusTo16(List<WorderInformationEntity> finishedWorders);

    void updateWorderTemplate(String templateId, List<String> worderNoList);

    WorderInfoDTO findWorderInfo(String worderNo);

    List<Integer> selectCaWorderInfo();

    WorderInfoEntity getWorderInfoEntityByWorderId(Integer worderId);

    R proCockpitData(BydBaobiaoParam param);

    WorderInformationEntity getDotInfoByWorderNo(String worderNo);

    String updateWorderExecStatus(String worderNo, int worderStatus, int worderExecStatus, String nextFlowChildCode);

    R proDotCockpitData(BydBaobiaoParam param);
}
