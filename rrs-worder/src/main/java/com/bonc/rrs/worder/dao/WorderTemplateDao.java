package com.bonc.rrs.worder.dao;

import com.bonc.rrs.worder.entity.WorderTemplateAddedMaterialTypeEntity;
import com.bonc.rrs.worder.entity.dto.*;
import com.bonc.rrs.worder.entity.po.*;
import com.bonc.rrs.worder.entity.query.BalanceRuleNameQuery;
import com.bonc.rrs.worder.entity.query.FieldNameQuery;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * Created by zhangyibo on 2020-03-12 16:15
 */

@Mapper
public interface WorderTemplateDao {


    /**
     * 根据结算类型和结算对象获取结算名称列表
     * @param balanceRuleNameQuery
     * @return
     */
    List<BalanceRuleNamePo> listBalanceRuleName(BalanceRuleNameQuery balanceRuleNameQuery);

    /**
     * 获取所有套包列表
     * @return
     */
    List<SuiteNamePo> listSuiteName();

    /**
     * 根据字段用途和字段分类获取字段名称列表
     * @param fieldNameQuery
     * @return
     */
    List<FieldNamePo> listFieldNameByPurposeAndClass(FieldNameQuery fieldNameQuery);

    /**
     * 获取所有工单类型
     * @return
     */
    List<WorderTypePo> listWorderType();

    /**
     * 获取一级工单类型列表
     * @return
     */
    List<WorderTypePo> listFirstWorderType();

    /**
     * 根据工单类型ID获取下一级工单类型
     * @param worderTypeId
     * @return
     */
    List<WorderTypePo> listNextWorderType(String worderTypeId);

    /**
     * 获得所有品牌列表
     * @return
     */
    List<BrandPo> listByDotId(@Param("userId") Long userId);

    /**
     * 获得所有品牌列表
     * @return
     */
    List<BrandPo> listBrand(@Param("templateId") Integer templateId,@Param("userId") Long userId);
    /**
     * 获得所有品牌列表
     * @return
     */
    List<BrandEntity> listBrandInfo(@Param("templateId") Integer templateId,@Param("userId") Long userId);

    List<BrandPo> listBrands();


    List<BrandEntity> listBrandInfos();

    /**
     * 获取支持导入的品牌列表
     * @return
     */
    List<BrandEntity> listImportBrandInfos();

    /**
     * 根据品牌ID获取车型列表
     * @param brandId
     * @return
     */
    List<CarTypePo> listCarTypeByBrandId(String brandId);

    /**
     * 根据模版ID获取地区
     * @param templateId
     * @return
     */
    List<RegionPo> listRegionByTemplateId(String templateId);

    /**
     * 根据模版ID获取字段
     * @param templateId
     * @return
     */
    List<FieldNamePo> listFieldByTemplateId(String templateId);

    /**
     * 根据模版ID获取车型
     * @param templateId
     * @return
     */
    List<CarTypePo> listCarTypeByTemplateId(String templateId);

    /**
     * 根据工单模版ID获取工单类型
     * @param templateId
     * @return
     */
    List<WorderTypePo> listWorderTypeByTemplateId(String templateId);

    /**
     * 根据公司类型获取公司列表
     * @param companyType
     * @return
     */
    List<CompanyNamePo> listCompanyNameByCompanyType(String companyType);

    /**
     * 查询固定结算项信息
     * @param templateId
     * @return
     */
    List<WorderTemplateFixedMaterialDto> listWorderTemplateFixedMaterial(Integer templateId);

    List<WorderTemplateAddedMaterialTypeEntity> listWorderTemplateAddedMaterialType(Integer templateId);


    /**
     * 新增工单模版关联工单类型信息
     * @param worderTemplateWorderTypes
     * @return
     */
    Integer saveWorderTemplateWorderType(List<WorderTemplateWorderTypeDto> worderTemplateWorderTypes);

    /**
     * 新增工单模版关联字段信息
     * @param worderTemplateFields
     * @return
     */
    Integer saveWorderTemplateField(List<WorderTemplateFieldDto> worderTemplateFields);

    /**
     * 新增工单模版关联车型信息
     * @param worderTemplateCars
     * @return
     */
    Integer saveWorderTemplateCar(List<WorderTemplateCarDto> worderTemplateCars);

    /**
     * 查询所有固定字段ID
     * @return
     */
    List<String> listNecessaryField();

    /**
     * 排除当前模板根据模版名获取模版数量
     * @param templateName
     * @param templateId
     * @return
     */
    Integer getCountByTemplateName(@Param("templateName") String templateName,@Param("templateId") Integer templateId);

    /**
     * 新增工单模版关联地区信息
     * @param worderTemplateRegions
     * @return
     */
    Integer saveWorderTemplateRegion(List<WorderTemplateRegionDto> worderTemplateRegions);

    /**
     * 新增工单模版信息
     * @param worderTemplate
     * @return
     */
    Integer saveWorderTemplate(WorderTemplateDto worderTemplate);

    Integer saveWorderTemplateFixedMaterial(List<WorderTemplateFixedMaterialDto> worderTemplateFixedMaterialDtos);

    Integer saveWorderTemplateAddedMaterialType(List<WorderTemplateAddedMaterialTypeEntity> worderTemplateAddedMaterialTypes);

    /**
     * 工单模版列表
     * @param worderTemplateQueryDto
     * @return
     */
    List<WorderTemplatePo> listWorderTemplate(WorderTemplateQueryDto worderTemplateQueryDto);


    int listWorderTemplateCount(WorderTemplateQueryDto worderTemplateQueryDto);

    /**
     * 根据工单模版ID获取模版信息
     * @param templateId
     * @return
     */
    List<WorderTemplateDto> getWorderTemplateByTemplateId(String templateId);

    /**
     * 根据模版ID获取车型
     * @param templateId
     * @return
     */
    List<CarTypePo> getCarTypeByTemplateId(String templateId);


    /**
     * 修改工单模版
     * @param worderTemplateDto
     * @return
     */
    Integer updateWorderTemplate(WorderTemplateDto worderTemplateDto);

    /**
     * 修改工单模版关联地区
     * @param worderTemplateRegionDto
     * @return
     */
    Integer updateTemplateRegion(WorderTemplateRegionDto worderTemplateRegionDto);

    /**
     * 修改工单模版关联工单类型
     * @param worderTemplateWorderTypeDto
     * @return
     */
    Integer updateWorderTemplateWorderType(WorderTemplateWorderTypeDto worderTemplateWorderTypeDto);

    /**
     * 修改工单模版关联工单字段
     * @param worderTemplateFieldDto
     * @return
     */
    Integer updateWorderTemplateField(WorderTemplateFieldDto worderTemplateFieldDto);

    /**
     * 修改工单模版关联车型
     * @param worderTemplateCarDto
     * @return
     */
    Integer updateWorderTemplateCar(WorderTemplateCarDto worderTemplateCarDto);

    /**
     * 删除模版地区
     * @param templateId
     * @return
     */
    Integer deleteTemplateRegion(String templateId);

    /**
     * 删除模版工单类型
     * @param templateId
     * @return
     */
    Integer deleteWorderTemplateWorderType(String templateId);

    /**
     * 删除模版字段
     * @param templateId
     * @return
     */
    Integer deleteWorderTemplateField(String templateId);

    /**
     * 删除模版车型
     * @param templateId
     * @return
     */
    Integer deleteWorderTemplateCar(String templateId);

    /**
     * 删除固定结算项
     * @param templateId
     * @return
     */
    Integer deleteWorderTemplateFixedMaterial(Integer templateId);

    Integer deleteWorderTemplateAddedMaterialType(Integer templateId);

    /**
     * 删除工单模版
     * @param templateId
     * @return
     */
    Integer deleteWorderTemplate(String templateId);

    WorderTemplateDto findTemplateInfoById(@Param("id") Integer id);

    /**
     * 根据工单ID获取工单模集合
     * @param worderIds
     * @return
     */
    List<WorderTemplateDto> getListByWorderIds(@Param("worderIds") Set<Integer> worderIds);
    /**
     * 修改工单模板
     * @param templateId
     * @return
     */
    Integer updateByWorderTempId(@Param("templateId") String templateId,@Param("isOneself") String isOneself);


    /**
     * 根据品牌、区域和工单类型查询模板信息
     * @param brandId
     * @param worderTypeId
     * @param provinceCode
     * @return
     */
    List<WorderTemplateDto> findTemplateInfoByBrandIdAndWorderTypeIdAndRegion(@Param("brandId") Integer brandId,
                                                                              @Param("worderTypeId") Integer worderTypeId,
                                                                              @Param("regionId") Integer regionId);
}
