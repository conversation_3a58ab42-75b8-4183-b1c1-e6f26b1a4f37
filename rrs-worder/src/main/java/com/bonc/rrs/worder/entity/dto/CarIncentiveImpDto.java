package com.bonc.rrs.worder.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import lombok.Data;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.io.Serializable;

/**
 * @Description: 车企激励导入模板
 * @Author: jing<PERSON><PERSON>n
 * @Date: 2022/3/10 15:27
 * @Version: 1.0
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(15)
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER)
public class CarIncentiveImpDto implements Serializable {

    @ExcelProperty(value = "工单号", index = 0)
    @ColumnWidth(20)
    private String worderNo;

    @ExcelProperty(value = "正负激励", index = 1)
    @ColumnWidth(20)
    private String incentiveType;

    @ExcelProperty(value = "奖惩原因编码", index = 2)
    @ColumnWidth(20)
    private String stimulateReason;

    @ExcelProperty(value = "奖惩金额", index = 3)
    @ColumnWidth(20)
    private String stimulateFee;
}
