package com.bonc.rrs.worder.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("worder_template_region")
@Data
public class WorderTemplateRegionEntity {

    @TableId(value = "id")
    private Integer id;

    @TableField(value = "template_id", exist = true)
    private Integer templateId;

    @TableField(value = "region_id", exist = true)
    private Integer regionId;
}
