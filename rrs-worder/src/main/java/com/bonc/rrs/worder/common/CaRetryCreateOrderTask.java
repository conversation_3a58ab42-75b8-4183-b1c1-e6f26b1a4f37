package com.bonc.rrs.worder.common;

import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.ca.domain.CaOrderApiVO;
import com.bonc.rrs.ca.service.CaBizService;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.util.SmsUtil;
import com.bonc.rrs.util.UserUtil;
import com.bonc.rrs.worder.entity.WorderIntfMessageEntity;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.service.WorderIntfMessageService;
import com.gexin.fastjson.JSON;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.util.Pair;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 推送订单入库定时任务
 * @Date 2024/04/3 10:06
 */
@Slf4j
@Component("caRetryCreateOrderTask")
@RequiredArgsConstructor
public class CaRetryCreateOrderTask implements ITask {
    private final WorderIntfMessageService worderIntfMessageService;
    private final WorderInformationService worderInformationService;
    private final CaBizService caBizService;

    @Override
    public void run(String params) {
        UserUtil.createDefaultLoginUser();
        log.info("长安订单入库定时任务 ---- 开始");
        List<WorderIntfMessageEntity> worderIntfMessageEntityList = worderIntfMessageService.queryNotSaveOrder(1);
        for (WorderIntfMessageEntity messageEntity : worderIntfMessageEntityList) {
            Pair<Long, String> worderIdNoPair;
            try {
                CaOrderApiVO caOrderApiVO = JSON.parseObject(messageEntity.getData(), CaOrderApiVO.class);
                worderIdNoPair = caBizService.doParseDataAndSaveOrder(caOrderApiVO);
                worderIntfMessageService.updateWorderIdById(messageEntity.getId(), Math.toIntExact(worderIdNoPair.getFirst()));
            } catch (Exception e) {
                log.error(messageEntity.getOrderCode() + "重试失败", e.getMessage());
                SmsUtil.sendSms("15910305046", "长安订单重试失败" + messageEntity.getMessageType() + ",车企订单号:" + messageEntity.getOrderCode(), "【到每家科技服务】");
                worderIntfMessageService.updateMessageTypeById(messageEntity.getId(), messageEntity.getMessageType() + 1, e.getMessage());
                continue;
            }

            try {
                Results results = worderInformationService.goAutoSendWorder(worderIdNoPair.getSecond(), ConstantPool.NEWS_OPERATOR_NAME, null);
                if (results.getCode() != 0) {
                    throw new RRException(results.getCode() + results.getMsg());
                }
                // 修改工单状态 0
                worderInformationService.updateWorderStatus(worderIdNoPair.getSecond());
            } catch (Exception e) {
                log.error("车企订单号{}派单失败", messageEntity.getOrderCode(), e);
                SmsUtil.sendSms("15910305046", "门到门订单派单失败,原因:" + e.getMessage() + ",车企订单号:" + messageEntity.getOrderCode(), "【到每家科技服务】");
            }
        }

        log.info("推送订单入库定时任务 ---- 结束");
    }
}
