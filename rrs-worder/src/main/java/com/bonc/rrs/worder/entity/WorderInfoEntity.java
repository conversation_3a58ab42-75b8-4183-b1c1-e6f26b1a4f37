package com.bonc.rrs.worder.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.bonc.rrs.sparesettlement.model.entity.BillingRecodeDTO;
import com.bonc.rrs.worder.aspect.CreateTime;
import com.bonc.rrs.worder.aspect.UpdateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 工单信息表
 *
 * <AUTHOR>
 */
@Data
@TableName("worder_information")
@ApiModel(value = "工单主表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorderInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "工单ID")
	@TableId(value = "worder_id", type = IdType.AUTO)
	private Integer worderId;

	@ApiModelProperty(value = "工单类型")
	private Integer worderTypeId;

	@ApiModelProperty(value = "工单类型名称")
	@TableField(exist = false)
	private String worderTypeName;

	@ApiModelProperty(value = "工单编号（省市简称+创建日期+工单类型+0-1000的自增序列）")
	private String worderNo;

	@ApiModelProperty(value = "工单来源类型（数据字典worder_source_type）")
	//@DicMarker(key = "worder_source_type")
	private Integer worderSourceType;

	@ApiModelProperty(value = "工单来源ID号（个人表及企业表ID）")
	private String worderSourceId;

	@ApiModelProperty(value = "工单状态（worder_status）")
	//@DicMarker(key = "worder_status")
	private Integer worderStatus;

	@ApiModelProperty(value = "创建时间")
	@CreateTime
	private Date createTime;

	@ApiModelProperty(value = "修改时间")
	@UpdateTime
	private Date modifyTime;

	@ApiModelProperty(value = "是否删除")
	@TableLogic(value = "0" ,delval = "1")
	private Integer isDelete;

	@ApiModelProperty(value = "工单来源类型冗余")
	private String worderSourceTypeValue;

	@ApiModelProperty(value = "工单状态冗余")
	private String worderStatusValue;

	@ApiModelProperty(value = "充电桩规格")
	private String chargeStandard;

	@ApiModelProperty(value = "4s店名称")
	private String sellShop;

	@ApiModelProperty(value = "充电桩型号")
	private String chargeModel;

	@ApiModelProperty(value = "车辆品牌")
	private String carBrand;

	@ApiModelProperty(value = "充电桩编号")
	private String chargeCode;

	@ApiModelProperty(value = "客户购车日期")
	private Date buyDate;

	@ApiModelProperty(value = "车辆型号")
	private String carModel;

	@ApiModelProperty(value = "车辆车架号")
	private String carVin;

	@ApiModelProperty(value = "厂商联系人")
	private String factoryLinkMan;

	@ApiModelProperty(value = "充电器cd号")
	private String chargeCd;

	@ApiModelProperty(value = "厂商联系人电话")
	private String linkManPhone;

	@ApiModelProperty(value = "用户姓名")
	private String userName;

	@ApiModelProperty(value = "客户联系电话")
	private String userPhone;

	@ApiModelProperty(value = "客户邮编")
	private String postcode;

	@ApiModelProperty(value = "车位位置冗余")
	private String parkTypeValue;

	@ApiModelProperty(value = "车位位置（数据字典 park_type）")
	//@DicMarker(key = "park_type")
	private String parkType;

	@ApiModelProperty(value = "个人：身份证，企业：营业执照")
	private String userCertificate;

	@ApiModelProperty(value = "电力类型（数据字典 electric_type）")
	//@DicMarker(key = "electric_type")
	private String electricType;

	@ApiModelProperty(value = "车位形式类型冗余")
	private String parkingTypeValue;

	@ApiModelProperty(value = "车位形式类型（数据字典 parking_type）")
	//@DicMarker(key = "parking_type")
	private String parkingType;

	@ApiModelProperty(value = "安装数量")
	private String installNum;

	@ApiModelProperty(value = "用户地址")
	//@AddressMarker
	private String address;

	@ApiModelProperty(value = "客户提车日期")
	private Date getDate;

	@ApiModelProperty(value = "用户地址冗余")
	private String addressDup;

	@ApiModelProperty(value = "电力类型冗余")
	private String electricTypeValue;

	@ApiModelProperty(value = "营业执照或身份证照片")
	private String files;

	@ApiModelProperty(value = "当前办理人")
	private String candidate;

	@ApiModelProperty(value = "创建人")
	private Long creator;
	@TableField(exist = false)
	private String creatorName;

	@ApiModelProperty(value = "客服")
	//@CreateBy
	private Long createBy;

	@TableField(exist = false)
	private String createByName;
	@ApiModelProperty(value = "最后修改人")
	//@UpdateBy
	private Long updateBy;

	@ApiModelProperty(value = "项目组")
	private String candidatePm;

	@ApiModelProperty(value = "网点组")
	private String candidateBranch;

	@ApiModelProperty(value = "服务组")
	private String candidateAttendant;

	@ApiModelProperty(value = "工单执行状态（数据字典worder_exec_status）")
	//@DicMarker(key="worder_exec_status")
	private Integer worderExecStatus;

	@ApiModelProperty(value = "工单结算状态（数据字典worder_set_status）")
	//@DicMarker(key="worder_set_status")
	private Integer worderSetStatus;

	@ApiModelProperty(value = "工单增项结算状态（数据字典worder_Incre_status）")
	//@DicMarker(key="worder_Incre_status")
	private Integer worderIncreStatus;

	@ApiModelProperty(value = "工单激励状态（数据字典worder_exci_status）")
	//@DicMarker(key="worder_exci_status")
	private Integer worderExciStatus;

	@ApiModelProperty(value = "工单执行状态冗余")
	private String worderExecStatusValue;

	@ApiModelProperty(value = "工单结算状态冗余")
	private String worderSetStatusValue;

	@ApiModelProperty(value = "工单增项结算状态冗余")
	private String worderIncreStatusValue;

	@ApiModelProperty(value = "工单激励状态冗余")
	private String worderExciStatusValue;

	@ApiModelProperty(value = "服务经理id")
	private Integer pmId;

	@ApiModelProperty(value = "网点id")
	private Integer dotId;

	@ApiModelProperty(value = "服务兵id")
	private Integer serviceId;

	@ApiModelProperty(value = "工单模板id")
	private Integer templateId;

	@ApiModelProperty(value = "厂商结算总金额")
	private BigDecimal companyBalanceFee;

	@ApiModelProperty(value = "网点工单结算总金额")
	private BigDecimal dotBalanceFee;

	@ApiModelProperty(value = "网点增项结算总金额")
	private BigDecimal dotIncreBalanceFee;

	@ApiModelProperty(value = "网点增项结算含税价")
	private BigDecimal dotIncreBalanceFeeSum;

	@ApiModelProperty(value = "网点给客户的优惠金额")
	private BigDecimal dotIncreDiscountAmount;

	@ApiModelProperty(value = "服务兵结算总金额")
	private BigDecimal attendantBalanceFee;

	@ApiModelProperty(value = "发票id")
	private Integer invoiceId;

	@ApiModelProperty(value = "工单结算发布时间")
	private Date worderPublishTime;

	@ApiModelProperty(value = "增项结算发表时间")
	private Date worderIncrePublishTime;

	@ApiModelProperty(value = "工单完成时间")
	private Date worderFinishTime;

	@ApiModelProperty(value = "区级id")
	private Integer areaId;

	@ApiModelProperty(value = "客户类型")
	private Integer userType;

	@ApiModelProperty(value = "厂商id")
	private Integer companyId;

	@ApiModelProperty(value = "车企订单号")
	private String companyOrderNumber;

	@ApiModelProperty(value = "金牌订单号")
	private String medalOrderNumber;

	@ApiModelProperty(value = "勘测单号")
	private String conveyOrderNumber;

	@ApiModelProperty(value = "安装单号")
	private String installOrderNumber;

	@ApiModelProperty(value = "开票状态")
	private Integer ticketStatus;

	@TableField(exist = false)
	private String isPay;

	@TableField(exist = false)
	private List<WorderTypeEntity> worderTypeList;

	@TableField(exist = false)
	private DotInformationEntity dotInformationEntity;

	@TableField(exist = false)
	private List<WorderExtFieldEntity> worderExtFieldList = new ArrayList<>();
	@TableField(exist = false)
	private Long isNo;

	/**
	 * 是否支持自动派单 0:支持 1:不支持
	 */
	@TableField(exist = false)
	private Integer autoSend;

	/**
	 * 服务类型
	 */
	@TableField(exist = false)
	private Integer serviceTypeEnum;
	/**
	 * 流程实例id
	 */
	@ApiModelProperty(value = "流程实例id", required = false)
	private String procInstId;

	@ApiModelProperty(value = "客户申请电力报桩时间")
	private String cusApplyPowerTime;

	@ApiModelProperty(value = "客户电力报桩预计完成时间")
	private String cusExpectPowerTime;

	@ApiModelProperty(value = "客户电力报桩实际完成时间")
	private String cusRealPowerTime;

	@ApiModelProperty(value = "电力报桩备注")
	private String description;

	@ApiModelProperty(value = "确认到桩 1:到桩")
	private String chargeReach;

	// 勘测预约时间
	@ApiModelProperty(value = "勘测预约时间")
	private String conveyAppointTime;
	// 安装预约时间
	@ApiModelProperty(value = "安装预约时间")
	private String installAppointTime;
	// 勘测签退时间
	@ApiModelProperty(value = "勘测签退时间")
	private Date conveySignOutTime;
	// 安装签退时间
	@ApiModelProperty(value = "安装签退时间")
	private Date installSignOutTime;
	@ApiModelProperty(value = "用户增项结算总金额(不含税)")
	private BigDecimal userBalanceFee;
	@ApiModelProperty(value = "用户增项结算税额")
	private BigDecimal userBalanceFeeTax;
	@ApiModelProperty(value = "用户增项结算总金额(含税)")
	private BigDecimal userBalanceFeeSum;
	@ApiModelProperty(value = "用户增项结算实际收费金额")
	private BigDecimal userActualCost;
	@ApiModelProperty(value = "首次电联时间")
	private Date firstCallTime;
	@ApiModelProperty(value = "下次联系时间")
	private String nextContactTime;





	/**
	 *记录说明
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "操作记录内容")
	private String record;


	// 车企订单id
	@TableField(exist = false)
	@ApiModelProperty(value = "车企订单id")
	private String companyOrderNumberId;

	@TableField(exist = false)
	private String conveyWorderNo;

	/**
	 * 色标标签
	 *
	 */
	private Integer colorLabel;
	/**
	 * 开票信息
	 */
	private BillingRecodeDTO billingRecodeDTO;

	/**
	 * 支付订单状态（0:未知 1:支付中 2:支付成功 3:支付失败 4:作废）
	 */
	private Integer worderPayStatus;
	/**
	 * 发票链接
	 */
	private String receiptUrl;
	/**
	 * 场景配置权限
	 */
	private Map<String, Object> scenePermissions;

	/**
	 * 安装资料是否已提交
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "安装资料是否已提交")
	private String fixSubmit;

	@TableField(exist = false)
	@ApiModelProperty(value = "是否特斯拉预勘测单转正功能")
	private boolean tslPresurvey = false;

	@TableField(exist = false)
	@ApiModelProperty(value = "网点管理员联系电话")
	private String contactsPhone;

	@TableField(exist = false)
	@ApiModelProperty(value = "品牌id")
	private Integer brandId;

	@ApiModelProperty(value = "工单级别")
	private String worderLevel;

	@TableField(exist = false)
	@ApiModelProperty(value = "工单跳闸次数")
	private Integer superviseNum;

	@TableField(exist = false)
	@ApiModelProperty(value = "转单标识")
	private String transferOrder;

	@TableField(exist = false)
	@ApiModelProperty(value = "暂停标识")
	private String susPendOrder;

	@TableField(exist = false)
	@ApiModelProperty(value = "推送工单来源")
	private String pushOrderWorderSource;

	@TableField(exist = false)
	private String noSettleReason;

	@TableField(exist = false)
	private String noSettleRemark;

	//byd维修  是否可维护: 0-否 该订单不支持回传） 1-是
	@TableField(exist = false)
	private String isMaintainable;

}
