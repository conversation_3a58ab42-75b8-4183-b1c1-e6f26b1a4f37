package com.bonc.rrs.worder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.worder.dto.dto.FieldDto;
import com.bonc.rrs.worderapp.entity.vo.DataVo;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.youngking.lenmoncore.common.utils.R;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 工单扩展字段记录表（行模式）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-09 16:43:59
 */
public interface WorderExtFieldService extends IService<WorderExtFieldEntity> {
    List<FieldDto> getDtoListByWorderNo(String worderNo);

    PageUtils queryPage(Map<String, Object> params);

    R listWorderExtFieldByPrimaryKey(Map params);

    R getConveyWorderByInstallWorderNo(String worderNo);

    WorderExtFieldEntity getPrimaryKeyField(String worderNo);

    List<DataVo> getFieldIdByWorderNo(String worderNo,String purpose);

    /**
     * 校验必填资料必填
     * @param worderNo
     * @return
     */
    R checkFieldRequired(String worderNo, Integer fieldPurpose);

    List<WorderExtFieldEntity> getFieldsByWorderNo(String worderNo);

    void setFieldValueNull(Integer id);

    /**
      * @param purpose 字段用途 1创建 2勘测 3安装
     */
    List<Long> getFieldIdsByWorderNoAndPurpose(String worderNo, String purpose);

    List<WorderExtFieldEntity> getSpecificFields(String worderNo, Collection<Integer> fieldId);
}

