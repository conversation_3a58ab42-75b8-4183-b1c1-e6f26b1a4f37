package com.bonc.rrs.worder.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.insurancequota.InsuranceQuotaService;
import com.bonc.rrs.sapreceivable.InType;
import com.bonc.rrs.sapreceivable.OutType;
import com.bonc.rrs.sapreceivable.SapReceiveService;
import com.bonc.rrs.sparesettlement.service.impl.InvoiceAPIServiceImpl;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.warning.annotation.Warning;
import com.bonc.rrs.worder.common.IdempotentCheck;
import com.bonc.rrs.worder.constant.IdempotentConstant;
import com.bonc.rrs.worder.constant.RedisConstant;
import com.bonc.rrs.worder.dto.NoSettlementUpdateDTO;
import com.bonc.rrs.worder.dto.dto.ExtFieldDictionaryDto;
import com.bonc.rrs.worder.dto.dto.FeeDetailDto;
import com.bonc.rrs.worder.dto.dto.IncreaseFeeDto;
import com.bonc.rrs.worder.dto.dto.WorderTypeDto;
import com.bonc.rrs.worder.dto.vo.*;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.WorderInformationSub;
import com.bonc.rrs.worder.entity.WorderRemarkLogEntity;
import com.bonc.rrs.worder.entity.dto.CarIncentiveImpDto;
import com.bonc.rrs.worder.entity.dto.IncentiveImpDto;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.entity.po.*;
import com.bonc.rrs.worder.entity.vo.ColorLabelCount;
import com.bonc.rrs.worder.entity.vo.UserTransferVo;
import com.bonc.rrs.worder.service.CockpitService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.service.WorderInformationSubService;
import com.bonc.rrs.worder.service.WorderTemplateService;
import com.bonc.rrs.worder.service.NewWorderInformationService;
import com.bonc.rrs.worderinformationaccount.service.WorderInformationAccountService;
import com.bonc.rrs.worderinformationaccount.vo.WorderInformationAccountVO;
import com.bonc.rrs.worderinvoice.entity.WorderWaitAccountEntity;
import com.bonc.rrs.worderinvoice.service.WorderWaitAccountService;
import com.bonc.rrs.workManager.service.AutoSendService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.entity.ExcelInput;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.*;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysUserDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.MagrAreaBrand;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import io.swagger.annotations.*;
import lombok.extern.log4j.Log4j2;
import org.activiti.engine.impl.util.CollectionUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工单信息相关接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("worder/worderinformation")
@Api(tags = {"工单信息相关接口"})
@Log4j2
public class WorderInformationController {

    @Autowired
    private WorderInformationService worderInformationService;

    @Autowired
    private WorderWaitAccountService worderWaitAccountService;

    @Autowired
    private InsuranceQuotaService insuranceQuotaService;
    @Autowired
    private SapReceiveService sapReceiveService;
    @Autowired
    private WorderInformationAccountService worderInformationAccountService;
    @Autowired
    private InvoiceAPIServiceImpl invoiceAPIService;

    @Autowired
    private IdempotentCheck idempotentCheck;

    @Resource
    public SysUserDao sysUserDao;
    @Autowired(required = false)
    public WorderInformationSubService worderInformationSubService;

    @Autowired
    private WorderTemplateService worderTemplateService;

    @Autowired(required = false)
    private AutoSendService autoSendService;

    @Autowired
    private CockpitService cockpitService;

    @Autowired
    private NewWorderInformationService newWorderInformationService;

    /**
     * 根据模板获取工单字段
     *
     * @return
     */
    @GetMapping("/updateMaterial")
    @ApiOperation(value = "回退修改物料", notes = "回退修改")
    public R updateMaterial(String worderNo,Integer type) {
        String msg = worderInformationService.updateMaterial(worderNo,type);
        if(StringUtils.isBlank(msg)){
            return R.ok();
        }
        return R.error(msg);
    }


    /**
     * 回退至待勘测资料未提交     *
     * @return
     */
    @GetMapping("/updateWorderExecStatus")
    @ApiOperation(value = "回退至待勘测资料未提交", notes = "回退修改")
    public R updateWorderStatus(String worderNo) {
        String msg = worderInformationService.updateWorderExecStatus(worderNo,1,4,"kanceziliaoweitijiao");
        if(StringUtils.isBlank(msg)){
            return R.ok();
        }
        return R.error(msg);
    }

    @RequestMapping("/getWorderIdsByExcelAndParams")
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public R getWorderIdsByExcel(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        String companyId = request.getParameter("companyId");
        if (StringUtils.isBlank(companyId)) {
            return R.error("厂商id不能为空");
        }
        String companyOrderNumber = request.getParameter("companyOrderNumber");
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        String worderMainStatus = request.getParameter("worderMainStatus");
        WorderInformationAccountVO worderInformation = new WorderInformationAccountVO();
        worderInformation.setCompanyId(Integer.valueOf(companyId));
        worderInformation.setCompanyOrderNumber(companyOrderNumber);
        worderInformation.setStartTime(DateUtils.stringToDate(startTime, DateUtils.DATE_PATTERN));
        worderInformation.setEndTime(DateUtils.stringToDate(endTime, DateUtils.DATE_PATTERN));
        worderInformation.setWorderMainStatus(Integer.valueOf(worderMainStatus));
        ExcelInput excelInput = InputUtils.analysisExcel(file);
        List companyOrderNumbers = InputUtils.getColumnList(excelInput.getWorkbook(), IntegerEnum.ZERO.getValue());
        String orderNumbers = null;
        if (companyOrderNumbers.size() > IntegerEnum.ZERO.getValue()) {
            orderNumbers = StringUtils.appendSqureBeforeAndAfter("'" + org.apache.commons.lang.StringUtils.join(companyOrderNumbers, "', '") + "'");
//			StringUtils
        }
        worderInformation.setCompanyOrderNumbers(orderNumbers);
        List list = worderInformationAccountService.queryWorderByCompanyOrderNumbers(worderInformation);
//		QueryWrapper queryWrapper = new QueryWrapper();
//		queryWrapper.select("worder_id worderId, company_order_number companyOrderNumber, worder_no worderNo").in(
//				"company_order_number", companyOrderNumbers);
//
//		queryWrapper.in("worder_status", 3, 5);
//		queryWrapper.eq("worder_set_status", 1);
//		List list = worderInformationService.list(queryWrapper);
        return R.ok().putList(list).putNum(list.size());
    }


    //待结算导入
    @RequestMapping("/getWorderIdsByExcel")
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public R getWorderIdsByExcel(@RequestParam("file") MultipartFile file) {
        ExcelInput excelInput = InputUtils.analysisExcel(file);
        List companyOrderNumbers = InputUtils.getIsNoColumnList(excelInput.getWorkbook(), IntegerEnum.FOUR.getValue());
        if(CollectionUtil.isEmpty(companyOrderNumbers)){
            throw new RRException("未选择工单");
        }
        Workbook wb = excelInput.getWorkbook();
        List<Map> list = new ArrayList();
        Sheet sheet = wb.getSheetAt(0);
        for (int i = 0, len = sheet.getLastRowNum(); i <= len; i++) {
            Row row = sheet.getRow(i);
            String isNo = InputUtils.getCellValue(row, 16);
            if(isNo!=null && isNo.equals(Constant.Y)){
                String worderNo = InputUtils.getCellValue(row, IntegerEnum.ZERO.getValue());
                String stimulate = InputUtils.getCellValue(row, IntegerEnum.ONE.getValue());
                String companyNo = InputUtils.getCellValue(row, IntegerEnum.FOUR.getValue());
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("worder_no",worderNo);
                WorderInformationEntity worderInformationEntity = worderInformationService.getOne(queryWrapper);
                if(worderInformationEntity == null){
                    return R.error("工单：" + worderNo + "未查询工单信息");
                }
                Integer worderId = worderInformationEntity.getWorderId();
                QueryWrapper query = new QueryWrapper();
                query.eq("worder_id",worderId);
                if (StringUtils.isNotBlank(stimulate)){
                    query.eq("worder_invoice_type",1);
                    query.eq("stimulate_id",stimulate);
                }else{
                    query.eq("worder_invoice_type",0);
                }
                WorderWaitAccountEntity waitAccountEntity = worderWaitAccountService.getOne(query);
                Map<String,Object> jsonObject = new HashMap<>();
                jsonObject.put("companyOrderNumber",companyNo);
                jsonObject.put("worderId",worderId);
                if (StringUtils.isNotBlank(stimulate)){
                    jsonObject.put("stimulateId",stimulate);
                }
                jsonObject.put("worderNo",worderNo);
                jsonObject.put("id",waitAccountEntity.getId());
                list.add(jsonObject);
            }
        }
        return R.ok().putList(list).putNum(list.size());

//        List companyOrderNumbers = InputUtils.getIsNoColumnList(excelInput.getWorkbook(), IntegerEnum.FOUR.getValue());
//        if(CollectionUtil.isEmpty(companyOrderNumbers)){
//            throw new RRException("未选择工单");
//        }
//        QueryWrapper queryWrapper = new QueryWrapper();
//        queryWrapper.select("worder_id worderId, company_order_number companyOrderNumber, worder_no worderNo").in(
//                "company_order_number", companyOrderNumbers);
//        queryWrapper.eq("is_delete", 0);
//        queryWrapper.ne("worder_status", 6);
//        List list = worderInformationService.list(queryWrapper);
//        return R.ok().putList(list).putNum(list.size());
    }

    /**
     * 根据多个车企订单号查询工单worderId
     *
     * @return
     */
    @RequestMapping("/getWorderIdsByCompanyOrderNumbers")
    public R getWorderIdsByCompanyOrderNumbers(@RequestBody String params) {
        JSONObject jsonObject = JSON.parseObject(params);
        String str = jsonObject.getString("companyOrderNumbers");
        String[] companyOrderNumbers = StringUtils.isNotBlank(str) ? str.split(",") : null;
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.select("worder_id worderId").in("company_order_number", companyOrderNumbers);
        List list = worderInformationService.list(queryWrapper);
        return R.ok().putList(list);
    }

    /**
     * 列表
     */
    @GetMapping("/list")
    //@RequiresPermissions("worder:worderinformation:list")
    @ApiOperation(value = "获取列表", notes = "获取列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "params", value = "参数", required = false)
    })
    public R list(@RequestParam Map<String, Object> params) {
        PageUtils page = worderInformationService.queryList(params);
        return R.ok().put("page", page);
    }



    @RequestMapping("/colorLabelCount")
    public R getColorLabelCount() {
        ColorLabelCount colorLabelCount = worderInformationService.getColorLabelCount();
        return R.ok().put("colorLabelCount", colorLabelCount);
    }


    @GetMapping("/orderAll")
    @ApiOperation(value = "获取所有工单", notes = "获取所有工单")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "params", value = "参数", required = false)
    })
    public R queryAll(@RequestParam Map<String, Object> params) {
        PageUtils page = worderInformationService.queryAll(params);
        return R.ok().put("page", page);
    }

    @GetMapping("/companyCancelAudit/orderAll")
    @ApiOperation(value = "车企取消审核订单查询", notes = "车企取消审核订单查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "params", value = "参数", required = false)
    })
    public R companyCancelAuditQueryAll(@RequestParam Map<String, Object> params) {
        PageUtils page = worderInformationService.companyCancelAuditQueryAll(params);
        return R.ok().put("page", page);
    }

    @GetMapping("/companyReviewFailed/orderAll")
    @ApiOperation(value = "车企审核不通过订单查询", notes = "车企审核不通过订单查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "params", value = "参数", required = false)
    })
    public R companyReviewFailedQueryAll(@RequestParam Map<String, Object> params) {
        PageUtils page = worderInformationService.companyReviewFailedQueryAll(params);
        return R.ok().put("page", page);
    }

    @GetMapping("/companyCancelAudit/reviewFailed")
    @ApiOperation(value = "车企取消审核不通过", notes = "车企取消审核不通过")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "params", value = "参数", required = false)
    })
    public R cancelReviewFailed(@RequestParam(value = "worderId") Integer worderId,@RequestParam(value = "content") String content) {
        return worderInformationService.cancelAuditReviewFailed(worderId,content);
    }

    @GetMapping("/companyCancelAudit/notice")
    @ApiOperation(value = "车企取消审核通过通知", notes = "车企取消审核不通过")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "params", value = "参数", required = false)
    })
    public R notice(@RequestParam(value = "worderId") Integer worderId) {
        return worderInformationService.companyCancelAuditNotice(worderId);
    }

    @GetMapping("/companyReviewFailed/fallback")
    @ApiOperation(value = "车企回退", notes = "车企回退")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "params", value = "参数", required = false)
    })
    public R companyFallback(@RequestParam(value = "worderId") Integer worderId,@RequestParam(value = "content") String content) {
        return worderInformationService.companyFallback(worderId,content);
    }

    @GetMapping("/queryWorderOvertime")
    @ApiOperation(value = "获取今日超时工单", notes = "获取今日超时工单")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "params", value = "参数", required = false)
    })
    public R queryWorderOvertime(@RequestParam Map<String, Object> params) {
        PageUtils page = worderInformationService.queryWorderOvertime(params);
        return R.ok().put("page", page);
    }


    @GetMapping("/orderThird")
    @ApiOperation(value = "获取合作网点所有工单", notes = "获取合作网点所有工单")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "params", value = "参数", required = false)
    })
    public R queryThird(@RequestParam Map<String, Object> params) {
        PageUtils page = worderInformationService.queryThird(params);
        return R.ok().put("page", page);
    }

    @GetMapping("/queryUserBrand")
    @ApiOperation(value = "获取当前用户有的品牌", notes = "获取当前用户有的品牌")
    public R queryUserBrand() {
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        List<Long> roleIdList = user.getRoleIdList();
        List<MagrAreaBrand> brands;
        if(roleIdList.size()==1 && roleIdList.contains(2L)){
            brands = sysUserDao.listUserBrand(user.getUserId());
        }else{
            brands = sysUserDao.listBrand();
        }
        return R.ok().put("brands", brands);

    }


    @GetMapping("/queryUserShowBrand")
    @ApiOperation(value = "获取当前用户有的品牌", notes = "获取当前用户有的品牌")
    public R queryUserShowBrand() {
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        List<Long> roleIdList = user.getRoleIdList();
        List<MagrAreaBrand> brands = sysUserDao.queryUserShowBrand(user.getUserId());

        return R.ok().put("brands", brands);

    }

    @GetMapping("/tslPresurvey")
    @ApiOperation(value = "特斯拉预勘测单转正接口", notes = "特斯拉预勘测单转正接口")
    public R tslPresurvey(Integer worderId) {
        return autoSendService.tslPresurvey(worderId);
    }

    /**
     * 获取详情信息
     */
    @GetMapping("/info/{worderId}")
    //@RequiresPermissions("worder:worderinformation:info")
    @ApiOperation(value = "获取详情", notes = "获取详情")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "Integer", name = "id", value = "id", required = true)
    })
    public R info(@PathVariable("worderId") Integer worderId) throws ParseException {
        WorderInfoEntity worderInformation = worderInformationService.getByWorderId(worderId);
//        if (StringUtils.isNotBlank(worderInformation.getNextContactTime())){
//            String time = worderInformation.getNextContactTime().substring(0,10);
//            worderInformation.setNextContactTime(time);
//        }
//        if (StringUtils.isNotBlank(worderInformation.getConveyAppointTime())){
//            String time = worderInformation.getConveyAppointTime().substring(0,10);
//            worderInformation.setConveyAppointTime(time);
//        }
//        if (StringUtils.isNotBlank(worderInformation.getInstallAppointTime())){
//            String time = worderInformation.getInstallAppointTime().substring(0,10);
//            worderInformation.setInstallAppointTime(time);
//        }
        QueryWrapper<WorderInformationSub> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.eq("worder_id",worderId);
        List<WorderInformationSub> worderInformationSubs = worderInformationSubService.list(objectQueryWrapper);
        if (CollectionUtil.isNotEmpty(worderInformationSubs)) {
            worderInformation.setIsNo(worderInformationSubs.get(0).getElectricityState());
            worderInformation.setNoSettleReason(worderInformationSubs.get(0).getNoSettlementReason());
            worderInformation.setNoSettleRemark(worderInformationSubs.get(0).getNoSettlementRemark());
        }
        return R.ok().put("worderInformation", worderInformation);
    }

    /**
     * 校验所选厂商是否能够创建工单
     *
     * @param company
     * @return
     */
    @PostMapping("/check")
    @ApiOperation(value = "校验厂商", notes = "校验厂商")
    public R check(@RequestBody CheckResultVo company) {
        //Integer checkResult = worderInformationService.checkCompany(companyId);
        //return R.ok().put("checkResult",checkResult);
        CheckResultVo resultVo = worderInformationService.checkCompany(company);
        return R.ok().put("resultVo", resultVo);
    }

    /**
     * 保存
     */
    @PostMapping("/save")
    //@RequiresPermissions("worder:worderinformation:save")
    @ApiOperation(value = "新增记录", notes = "新增记录")
    @Transactional
    public R save(@ApiParam @RequestBody WorderInfoEntity worderInformation) {
        Map<Integer, WorderTemplateDto> templateDtoMap = new HashMap<>();
        // 验证车企订单号+品牌
        if (worderInformationService.validateCompanyOrderNumberAndBrandExsit(worderInformation.getCompanyOrderNumber(),
                worderInformation.getTemplateId())) {
            return R.error("车企订单号已存在，无法创建工单");
        }
        String worderNo = worderInformationService.saveWorderInformation(worderInformation,templateDtoMap);
        if (worderNo.equals("errorLength")){
            return R.error("卡泰驰SN编码长度应为24位，请检查后再提交。");
        }

        if (worderNo.contains("error:")){
            String[] split = worderNo.split(":");
            return R.error("相同区域,品牌和服务类型有多个客服["+ split[1] +"],请账户设置正确后再提交。");
        }

        //去自动派单
        String username = ((SysUserEntity) SecurityUtils.getSubject().getPrincipal()).getUsername();
        Results results = worderInformationService.goAutoSendWorder(worderNo, username, worderInformation.getConveyWorderNo());
        // 修改工单状态 0
        worderInformationService.updateWorderStatus(worderNo);
        return R.Result(results.getCode(), results.getMsg());
    }

    /**
     * 保存
     */
    @PostMapping("/importBatchSave")
    //@RequiresPermissions("worder:worderinformation:save")
    @ApiOperation(value = "批量导入记录", notes = "批量导入记录")
    @Transactional
    public R importBatchSave(@ApiParam @RequestBody WorderInfoEntity worderInformation) {
        // 验证车企订单号+品牌
        if (worderInformationService.validateCompanyOrderNumberAndBrandExsit(worderInformation.getCompanyOrderNumber(),
                worderInformation.getTemplateId())) {
            return R.error("车企订单号已存在，无法创建工单");
        }
        return worderInformationService.importBatchSaveWorderInformation(worderInformation);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    //@RequiresPermissions("worder:worderinformation:update")
    @ApiOperation(value = "修改记录", notes = "修改记录")
    public R update(@ApiParam @RequestBody WorderInfoEntity worderInformation) {
        return worderInformationService.updateWorderInformation(worderInformation);


    }


    /**
     * 删除
     */
    @PostMapping("/delete")
    //@RequiresPermissions("worder:worderinformation:delete")
    @ApiOperation(value = "删除记录", notes = "删除记录")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "String", name = "ids", value = "ids", required = true)
    })
    public R delete(@RequestBody Integer worderId) {
        worderInformationService.removeById(worderId);

        return R.ok();
    }

    /**
     * 获取品牌信息
     *
     * @return
     */
    @GetMapping("/queryBrand")
    public R queryBrand(@RequestParam(value = "templateId", defaultValue = "") Integer templateId) {
        List<BrandPo> brandPos = worderInformationService.getBrandList(templateId);
        return R.ok().put("brandPos", brandPos);
    }


    @GetMapping("/queryBrands")
    public R queryBrands() {
        List<BrandPo> brandPos = worderInformationService.listBrands();
        return R.ok().put("brandPos", brandPos);
    }

    /**
     * 获取品牌信息
     * @return
     */
    @GetMapping("/queryBrandInfo")
    public R queryBrandInfo(@RequestParam(value = "templateId", defaultValue = "") Integer templateId){
        List<BrandEntity> brandPos = worderInformationService.getBrandListInfo(templateId);
        return R.ok().put("brandPos",brandPos);
    }

    @GetMapping("/queryBrandInfos")
    public R queryBrandInfos(){
        List<BrandEntity> brandPos = worderInformationService.listBrandInfos();
        return R.ok().put("brandPos",brandPos);
    }
    @GetMapping("/queryImportBrandInfos")
    public R queryImportBrandInfos(){
        List<BrandEntity> brandPos = worderInformationService.listImportBrandInfos();
        return R.ok().put("brandPos",brandPos);
    }


    /**
     * 获取服务类型
     * @return
     */
    @GetMapping("/queryServiceType")
    public R queryServiceType() {
        List<ServiceTypePo> serviceTypes = worderTemplateService.getServiceType();
        return R.ok().put("serviceTypes", serviceTypes);
    }

    /**
     * 根据品牌id获取车型
     *
     * @param brandId
     * @return
     */
    @GetMapping("/queryCarType")
    public R queryCarType(String brandId) {
        List<CarTypePo> carTypes = worderInformationService.getCarTypeList(brandId);
        return R.ok().put("carTypes", carTypes);
    }

    /**
     * 根据品牌车型筛选匹配的工单模板
     *
     * @return
     */
    @GetMapping("/queryWorderTemplate")
    @ApiOperation(value = "查询工单模板", notes = "查询工单模板")
    public R worderTemplateList(@RequestParam Map<String, Object> params) {
        List<WorderTemplatePo> worderTemplatePos = worderInformationService.getWorderTemplate(params);
        return R.ok().put("worderTemplatePos", worderTemplatePos);
    }
    /**
     * 修改工单信息 根据品牌车型筛选匹配的工单模板
     *
     * @return
     */
    @GetMapping("/WorderTemplate")
    @ApiOperation(value = "查询工单模板", notes = "查询工单模板")
    public R worderTemplate(@RequestParam Map<String, Object> params) {
        List<WorderTemplatePo> worderTemplatePos = worderInformationService.worderTemplate(params);
        return R.ok().put("worderTemplatePos", worderTemplatePos);
    }

    /**
     * 根据品牌车型筛选匹配的工单模板
     * @return
     */
    @GetMapping("/queryWorderTemplateInfo")
    @ApiOperation(value = "查询工单模板", notes = "查询工单模板")
    public R queryWorderTemplateInfo(@RequestParam Map<String ,Object> params){
        List<WorderTemplateDto> worderTemplatePos = worderInformationService.getWorderTemplateInfos(params);
        return R.ok().put("worderTemplatePos",worderTemplatePos);
    }
    /**
     * 根据模板获取工单字段
     *
     * @return
     */
    @GetMapping("/show")
    @ApiOperation(value = "获取工单字段", notes = "获取工单字段")
    public R worderFieldList(Integer templateId) {
        WorderInformationVo worderInformationVo = worderInformationService.getWorderFieldList(templateId);
        return R.ok().put("worderInformationVo", worderInformationVo);
    }

    /**
     * 根据模板获取工单字段
     *
     * @return
     */
    @GetMapping("/getInfoByNo")
    @ApiOperation(value = "获取工单字段信息", notes = "获取工单字段信息")
    public R getWorderExtInformation(String worderNo) {
        WorderInformationVo worderInformationVo = worderInformationService.getWorderExtInformation(worderNo);
        return R.ok().put("worderInformationVo", worderInformationVo);
    }

    /**
     * 查询物料信息
     *
     * @param worderId
     * @return
     */
    @GetMapping("/queryMateriel")
    @ApiOperation(value = "查询物料信息", notes = "查询物料信息")
    public R queryMateriel(Integer worderId) {
        List<WorderMaterielVo> materiel = worderInformationService.getMateriel(worderId);
        return R.ok().putList(materiel);
    }

    /**
     * 获取操作记录列表
     *
     * @param worderNo
     * @return
     */
    @GetMapping("/queryRecord")
    @ApiOperation(value = "查询操作记录", notes = "查询操作记录")
    public R queryOperationRecord(String worderNo) {
        List<String> operationRecords = worderInformationService.queryOperationRecord(worderNo);
        return R.ok().putList(operationRecords);
    }

    /**
     * 获取第三方平台操作记录列表
     *
     * @param worderNo
     * @return
     */
    @GetMapping("/queryThirdRecord")
    @ApiOperation(value = "查询操作记录", notes = "查询操作记录")
    public R queryThirdOperationRecord(String worderNo) {
        List<String> operationRecords = worderInformationService.queryThirdOperationRecord(worderNo);
        return R.ok().putList(operationRecords);
    }

    /**
     * 获取工单备注
     *
     * @param worderNo
     * @return
     */
    @GetMapping("/queryRemarkLog")
    @ApiOperation(value = "查询工单备注", notes = "查询工单备注")
    public R queryRemarkLog(String worderNo) {
        List<WorderRemarkLogEntity> worderRemarkLogs = worderInformationService.queryRemarkLog(worderNo);
        return R.ok().putList(worderRemarkLogs);
    }

    /**
     * 查询工单类型下拉框数据
     *
     * @return
     */
    @GetMapping("/listWorderType")
    @ApiOperation(value = "查询工单类型", notes = "查询工单类型")
    public R listWorderType() {
        List<WorderTypeDto> worderTypeDtos = worderInformationService.listWorderType();
        return R.ok().put("worderTypeDtos", worderTypeDtos);
    }

    /**
     * 查询工单结算费用明细
     *
     * @return
     */
    @GetMapping("/queryFeeDetail")
    @ApiOperation(value = "查询工单结算费用明细", notes = "查询工单结算费用明细")
    public R queryFeeDetail(Integer worderId) {
        List<FeeDetailDto> feeDetailDtos = worderInformationService.getFeeDetail(worderId);
        return R.ok().put("feeDetailDtos", feeDetailDtos);
    }

    /**
     * 查询用户增项费用
     *
     * @return
     */
    @GetMapping("/queryUserIncreaseFee")
    @ApiOperation(value = "查询用户增项费用", notes = "查询用户增项费用")
    public R queryIncreaseFee(Integer worderId) {
        IncreaseFeeDto userIncreaseFee = worderInformationService.getIncreaseFee(worderId);
        return R.ok().put("userIncreaseFee", userIncreaseFee);
    }

    /**
     * 车企确认勘测完成
     * 车企确认安装，车企退回
     *
     * @return
     */
    @RequestMapping("/confirm")
    @ApiOperation(value = "确认勘测，安装，车企退回", notes = "确认勘测，安装，车企退回")
    public R confirmInstall(@RequestParam Integer worderId, @RequestParam Integer flag) {
        // 重复校验获取执行状态
        IdempotentPo idempotentPo = idempotentCheck.functionBegin(RedisConstant.Prefix.BUSINESS
                , "confirm"
                , worderId
                , flag);

        // 首次执行完成将返回首次结果
        if (IdempotentConstant.Result.FINISH.equals(idempotentPo.getCode())) {
            return R.ok().put("result", idempotentPo.getValue());
        // 首次调用未执行完成放回重复调用
        } else if (!IdempotentConstant.Result.SUCCESS.equals(idempotentPo.getCode())) {
            return R.ok().put("result", IdempotentConstant.Result.getName(idempotentPo.getCode()));
        }


        //安装中增加校验勘测不增加校验
        if (flag == 2 && worderInformationService.checkBanlanRule(worderId)) {
            return R.error(201, "该工单未匹配到工程结算规则！");
        }
        String result = worderInformationService.updateStatus(worderId, flag);

        // 执行结束更新执行状态
        idempotentCheck.functionFinish(RedisConstant.Prefix.BUSINESS
                , result
                , "confirm"
                , worderId
                , flag);
        return R.ok().put("result", result);
    }

    /**
     * 取消服务
     *
     * @return
     */
    @PostMapping("/cancelService")
    @ApiOperation(value = "取消服务", notes = "取消服务")
    public R cancelService(@RequestBody WorderRemarkLogEntity worderRemarkLog) {
        worderInformationService.cancelService(worderRemarkLog);
        return R.ok();
    }


    /**
     * @return
     */
    @GetMapping("/queryCollecDetailByWorderId")
    @ApiOperation(value = "根据工单id查询收款明细", notes = "根据工单id查询收款明细")
    public R queryCollecDetailByWorderId(@RequestParam Integer worderId) {
        try {
            return worderInformationService.queryCollecDetailByWorderId(worderId);
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("查询失败");
        }

    }


    /**
     * 预约服务
     *
     * @param appointServiceVo
     * @return
     */
    @Warning("预约服务")
    @PostMapping("/appointService")
    @ApiOperation("预约服务")
    @Lock4j(keys = {"#appointServiceVo.worderNo", "#appointServiceVo.flag"})
    public R updateConveyApoint(@RequestBody AppointServiceVo appointServiceVo) {
        return worderInformationService.updateAppointTime(appointServiceVo);
    }

    /**
     * 首次电联用户时间
     *
     * @param firstCallTimeVo
     * @return
     */
    @Warning("首次电联用户")
    @PostMapping("/firstCallTime")
    @ApiOperation("首次电联用户时间")
    public R updateFirstCallTime(@RequestBody FirstCallTimeVo firstCallTimeVo) {
        worderInformationService.updateFirstCallTime(firstCallTimeVo);
        return R.ok().putWorderNo(firstCallTimeVo.getWorderNo()).putWorderExecStatus(null)
                .putWorderTriggerEvent(WarningConstant.FIRST_CONTACT);
    }

    /**
     * 电力报桩
     *
     * @param powerPileVo
     * @return
     */
    @PostMapping("/powerPile")
    @ApiOperation("电力报桩")
    public R powerPile(@RequestBody PowerPileVo powerPileVo) {
        worderInformationService.powerPile(powerPileVo);
        return R.ok();
    }

    /**
     * 设置网点奖惩
     *
     * @param worderPmStimulateVo
     * @return
     */
    @PostMapping("/setStimulate")
    @ApiOperation("设置网点奖惩")
    public R setStimulate(@RequestBody WorderPmStimulateVo worderPmStimulateVo) {
        if (IntegerEnum.TEN.getValue().equals(worderPmStimulateVo.getStimulateType()) && worderPmStimulateVo.getDotId() == null) {
            return R.error("无网点信息不能设置网点奖惩");
        }
        worderInformationService.setStimulate(worderPmStimulateVo);
        return R.ok();
    }

    /**
     * 修改网点奖惩
     *
     * @param worderPmStimulateVo
     * @return
     */
    @PostMapping("/updateStimulate")
    @ApiOperation("修改网点奖惩")
    public R updateStimulate(@RequestBody WorderPmStimulateVo worderPmStimulateVo) {
        worderInformationService.updateStimulate(worderPmStimulateVo);
        return R.ok();
    }


    /**
     * 奖惩对象类型获取
     *
     * @param
     * @return
     */
    @GetMapping("/listStimulateType")
    @ApiOperation("奖惩对象类型获取")
    public R listStimulateType() {
        List<Map<String, Object>> stimulateTypeList = worderInformationService.listStimulateType();
        return R.ok().put("stimulateTypeList", stimulateTypeList);
    }

    /**
     * 奖惩类型，即激励原因获取
     *
     * @param
     * @return
     */
    @GetMapping("/listStimulateReason")
    @ApiOperation("奖惩类型，即激励原因获取")
    public R listStimulateReason(String dicName) {
        if (StringUtils.isBlank(dicName)) {
            dicName = "incentive_reason";
        }
        List<Map<String, Object>> stimulateReasonList = worderInformationService.listStimulateReason(dicName);
        return R.ok().put("stimulateReasonList", stimulateReasonList);
    }

    @PostMapping("/getStimulateReason")
    @ApiOperation("获取激励原因")
    public R getStimulateReason() {
        return worderInformationService.getStimulateReason();
    }

    @PostMapping("/queryStimulateList")
    @ApiOperation("激励列表查询")
    public R queryStimulateList(@RequestBody WorderPmStimulateVo worderPmStimulateVo) {
        return worderInformationService.getStimulateList(worderPmStimulateVo);
    }

    @PostMapping("/cancelStimulate")
    @ApiOperation("激励撤销")
    public R cancelStimulate(@RequestBody WorderPmStimulateVo worderPmStimulateVo) {
        return worderInformationService.cancelStimulate(worderPmStimulateVo);
    }

    @PostMapping("/incentiveImport")
    @ApiOperation("网点激励列表批量导入")
    @ResponseBody
    public R incentiveImport(@RequestParam("file") MultipartFile file, @RequestParam("fileIds") Integer[] fileIds) throws IOException {
        //获取文件名
        String fileName = file.getOriginalFilename();
        //获取文件的后缀名为xlsx
        assert fileName != null;
        String fileXlsx = fileName.substring(fileName.length() - 5);
        String fileXls = fileName.substring(fileName.length() - 4);
        //校验文件扩展名
        if (!(fileXlsx.equals(".xlsx") || fileXls.equals(".xls"))) {
            return R.error().put("msg", "文件类型不正确！");
        }
        return worderInformationService.incentiveImport(file, fileIds);
    }

    @PostMapping("/incentiveCarImport")
    @ApiOperation("车企激励列表批量导入")
    @ResponseBody
    public R incentiveCarImport(@RequestParam("file") MultipartFile file, @RequestParam("fileIds") Integer[] fileIds) throws IOException {
        //获取文件名
        String fileName = file.getOriginalFilename();
        //获取文件的后缀名为xlsx
        assert fileName != null;
        String fileXlsx = fileName.substring(fileName.length() - 5);
        String fileXls = fileName.substring(fileName.length() - 4);
        //校验文件扩展名
        if (!(fileXlsx.equals(".xlsx") || fileXls.equals(".xls"))) {
            return R.error().put("msg", "文件类型不正确！");
        }
        return worderInformationService.incentiveCarImport(file, fileIds);
    }


    /**
     * 下载车企模板
     * @param response
     * @throws IOException
     */
    @PostMapping(value = "/downLoadCar")
    @ApiOperation("下载车企模板")
    public void downLoadCar(HttpServletResponse response) throws Exception {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("车企激励导入模板", "utf-8") + ".xlsx");
        List<CarIncentiveImpDto> list = new ArrayList<>();
        EasyExcelFactory.write(response.getOutputStream(), CarIncentiveImpDto.class).autoCloseStream(true).sheet(0, "激励模板").doWrite(list);
    }

    /**
     * 下载网点模板
     * @param response
     * @throws IOException
     */
    @PostMapping(value = "/downLoadDot")
    @ApiOperation("下载网点模板")
    public void downLoadDot(HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("网点激励导入模板", "utf-8") + ".xlsx");
        List<IncentiveImpDto> list = new ArrayList<>();
        EasyExcelFactory.write(response.getOutputStream(), IncentiveImpDto.class).autoCloseStream(true).sheet(0, "激励模板").doWrite(list);
    }



    @GetMapping("/getStimulateInfo")
    @ApiOperation("激励详情信息")
    public R getStimulateInfo(String stimulateId) {
        WorderPmStimulateVo stimulateInfo = worderInformationService.getStimulateInfo(stimulateId);
        return R.ok().put("stimulateInfo", stimulateInfo);
    }

    @PostMapping("/updateStimulateStatus")
    @ApiOperation("修改激励状态")
    public R updateStimulateStatus(@RequestBody WorderPmStimulateVo worderPmStimulateVo) {
        // 重复校验获取执行状态
        IdempotentPo idempotentPo = idempotentCheck.functionBegin(RedisConstant.Prefix.BUSINESS
                , "updateStimulateStatus"
                , worderPmStimulateVo.getId()
                , worderPmStimulateVo.getWorderId()
                , worderPmStimulateVo.getStatus());

        // 首次执行完成将返回首次结果
        if (IdempotentConstant.Result.FINISH.equals(idempotentPo.getCode())) {
            return R.error("禁止重复调用");
            // 首次调用未执行完成放回重复调用
        } else if (!IdempotentConstant.Result.SUCCESS.equals(idempotentPo.getCode())) {
            return R.error("禁止重复调用");
        }

        if (17 == worderPmStimulateVo.getStatus()) {
            Integer size = worderInformationService.queryStimulateByIdAndStatus(worderPmStimulateVo.getId(), 10);
            if (size < 1) {
                return R.error("激励单已不在一次待审核状态");
            }
        }

        if (18 == worderPmStimulateVo.getStatus()) {
            Integer size = worderInformationService.queryStimulateByIdAndStatus(worderPmStimulateVo.getId(), 17);
            if (size < 1) {
                return R.error("激励单已不在二次审核状态");
            }
        }

        worderInformationService.updateStimulateStatus(worderPmStimulateVo);

        // 执行结束更新执行状态
        idempotentCheck.functionFinish(RedisConstant.Prefix.BUSINESS
                , "success"
                , "updateStimulateStatus"
                , worderPmStimulateVo.getId()
                , worderPmStimulateVo.getWorderId()
                , worderPmStimulateVo.getStatus());
        return R.ok();
    }

    /**
     * 客户满意度回访
     *
     * @param
     * @return
     */
    @PostMapping("/clientSatisfaction")
    @ApiOperation("客户满意度回访")
    public R clientSatisfaction(@RequestBody ClientSatisfactionVo clientSatisfactionVo) {
        worderInformationService.addClientSatisfaction(clientSatisfactionVo);
        return R.ok();
    }

    /**
     * 客户满意度评分查询
     *
     * @param
     * @return
     */
    @GetMapping("/getSatisfactionByWorderNo")
    @ApiOperation("客户满意度评分查询")
    public R getSatisfactionByWorderNo(@RequestParam String worderNo) {
        ClientSatisfactionVo clientSatisfactionVo = worderInformationService.getSatisfactionByWorderNo(worderNo);
        return R.ok().put("clientSatisfaction",clientSatisfactionVo);
    }

    /**
     * 新增工单物料时获取物料名称
     *
     * @param
     * @return
     */
    @GetMapping("/listMateriel")
    @ApiOperation("新增工单物料时获取物料名称")
    public R listMateriel() {
        List<Map<String, Object>> listMateriel = worderInformationService.listMateriel();
        return R.ok().put("listMateriel", listMateriel);
    }

    /**
     * 根据物料id获取该物料的品牌和规格
     *
     * @param
     * @return
     */
    @GetMapping("/getMaterielBrandSpec")
    @ApiOperation("根据物料id获取该物料的品牌和规格")
    public R getMaterielBrandSpec(Integer materielId) {
        Map<String, Object> materielBrandSpec = worderInformationService.getMaterielBrandSpec(materielId);
        return R.ok().put("materielBrandSpec", materielBrandSpec);
    }

    /**
     * 修改工单物料时的回显
     *
     * @param
     * @return
     */
    @GetMapping("/getMaterielInfo")
    @ApiOperation("修改工单物料时的回显")
    public R getMaterielInfo(@RequestParam("materielId") Integer materielId, @RequestParam("worderId") Integer worderId) {
        Map<String, Object> materielInfo = worderInformationService.getMaterielInfo(materielId, worderId);
        return R.ok().put("materielInfo", materielInfo);
    }

    @GetMapping("/getAmount")
    @ApiOperation("调用360保险额度接口返回信息")
    public R getAmount() {
        /*List<JwWorkordersDTO> list = insuranceAmountService.getInsuranceAmount();
		return R.ok().put("list",list);*/
        //worderInformationService.addGuaranteeQuota();
        /*List<CustomerGuaranteeQuotaEntity> quota = insuranceQuotaService.getInsuranceAmount();
		return R.ok().put("quota",quota);*/
        return R.ok();
    }


    /**
     * 员工离职后，工单移交
     * @param userTransferVo
     * @return
     */
    @PostMapping("/userTransfer")
    @ApiOperation("员工转移工单")
    public R userTransfer(@RequestBody UserTransferVo userTransferVo){
        return worderInformationService.userTransfer(userTransferVo);
    }


    @PostMapping("/getSap")
    @ApiOperation("调用应收sap接口返回信息")
    public R getSap(@RequestBody InType in) {
        OutType outSap = sapReceiveService.outTypeList(in);
        return R.ok().put("outSap", outSap);
    }

    @GetMapping("/getSwitchStatus")
    @ApiOperation("工单开关状态查询")
    public R getSwitchStatus() {
        Integer switchStatus = worderInformationService.getSwitchStatus();
        return R.ok().put("switchStatus", switchStatus);
    }

    @GetMapping("/updateSwitchStatus")
    @ApiOperation("工单开关状态修改")
    public R updateSwitchStatus(Integer status) {
        worderInformationService.updateSwitchStatus(status);
        return R.ok();
    }

    @GetMapping("/queryStatus")
    @ApiOperation("查询状态")
    public R queryStatus(String dicNumber) {
        List<ExtFieldDictionaryDto> statusList = worderInformationService.queryStatus(dicNumber);
        return R.ok().put("statusList", statusList);
    }

    @GetMapping("/getCompanyPriceTypeByTemplateId")
    @ApiOperation("查询车企定价方式")
    public R getCompanyPriceTypeByTemplateId(Integer templateId) {
        return R.ok().put("priceType", worderInformationService.getCompanyPriceTypeByTemplateId(templateId));
    }

    @PostMapping("/updateStatusWorder")
    @ApiOperation("更改状态")
    public R updateStatusWorder(@RequestBody WorderInfoEntity worderInfoEntity){
        worderInformationService.updateWorderStatus(worderInfoEntity.getWorderNo());
        return R.ok();
    }

    /**
     * 查询网点名称与id
     *
     * @return
     */
    @RequestMapping("/listDotInfo")
    @ResponseBody
    public R listDotInfo(){
        return R.ok().put("dotInfo", worderInformationService.getListDotName());
    }


    @GetMapping("/queryDotBalanceRuleMaterielList")
    @ApiOperation("查询工单结算规则-网点关联物料")
    public R queryDotBalanceRuleMaterielList(Integer dotBalanceRuleId) {
        return R.ok().put("dotBalanceRuleMaterielList", worderInformationService.getDotBalanceRuleMaterielInfo(dotBalanceRuleId));
    }


    @GetMapping("/queryAddedMaterielTypeList")
    @ApiOperation("查询增值物料类型")
    public R queryAddedMaterielTypeList() {
        return R.ok().put("addedMaterialTypeList", worderInformationService.getAddedMaterielTypeList());
    }

    @GetMapping("/queryDotRuleExtFieldList")
    @ApiOperation("查询网点固定物料项额外信息")
    public R queryDotRuleExtFieldList() {
        return worderInformationService.getDotRuleExtFieldList();
    }

    @GetMapping("/queryCockpitData")
    @ApiOperation("查询驾驶舱看板数据")
    public R queryCockpitData(@RequestParam Map<String, Object> params) {
        List<Map<String, Object>> list = worderInformationService.queryCockpitData(params);
        Map<Object, List<Map<String, Object>>> map = list.stream().collect(Collectors.groupingBy(item -> item.get("type2"), Collectors.toList()));
        Map<String, List<Map<String, String>>> type2CsGroup = new HashMap<>();
        Map<String, List<Map<String, String>>> type2JrGroup = new HashMap<>();
        // 分类二下的分类三
        map.forEach((k, v) -> {
            List<Map<String, String>> csGroup = v.stream().map(item -> new HashMap<String, String>(){{
                put("name", item.get("type3") + "");
                put("queryType", item.get("csType") + "");
            }}).collect(Collectors.toList());
            List<Map<String, String>> jrGroup = v.stream().map(item -> new HashMap<String, String>(){{
                put("name", item.get("type3") + "");
                put("queryType", item.get("jrType") + "");
            }}).collect(Collectors.toList());
            type2CsGroup.put(k + "", csGroup);
            type2JrGroup.put(k + "", jrGroup);
        });
        return R.ok().put("cockpitData", list)
                .put("type2CsGroup", type2CsGroup)
                .put("type2JrGroup", type2JrGroup);
    }

    @GetMapping("/queryCockpitDetailData")
    @ApiOperation("查询驾驶舱看板弹框统计数据")
    public R queryCockpitDetailData(@RequestParam Map<String, Object> params) {
        return worderInformationService.queryCockpitDetailData(params);
    }

    @GetMapping("/queryCockpitDetailData2")
    @ApiOperation("查询驾驶舱看板弹框统计数据")
    public R queryCockpitDetailData2(@RequestParam Map<String, Object> params) {
        return worderInformationService.queryCockpitDetailData2(params);
    }

    @PostMapping("getNoInstallReasonList")
    @ApiOperation("获取未安装原因列表")
    public R getNoInstallReasonList(){
        return worderInformationService.getNoInstallReasonList();
    }

    @PostMapping("getWorderLevelList")
    @ApiOperation("获取未安装原因列表")
    public R getWorderLevelList(){
        return worderInformationService.getWorderLevelList();
    }

    @GetMapping("/queryCockpitStatistics")
    @ApiOperation("查询运营看板统计百分比数据")
    public R queryCockpitStatistics(@RequestParam Map<String, Object> params) {
        return R.ok().put("data", cockpitService.queryCockpitStatistics(params));
    }

    @GetMapping("/queryCSCockpitListData")
    @ApiOperation("查询运营看板超时报表数据")
    public R queryCSCockpitListData(@RequestParam Map<String, Object> params) {
        return R.ok().put("data", worderInformationService.queryCSCockpitListData(params));
    }

    @PostMapping("/updateNoSettlementStatus")
    @ApiOperation("更新未结算状态和原因")
    public R updateNoSettlementStatus(@RequestBody NoSettlementUpdateDTO request) {
        newWorderInformationService.updateNoSettlementStatus(request);
        return R.ok();
    }

}
