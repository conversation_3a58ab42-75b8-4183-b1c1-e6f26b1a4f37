package com.bonc.rrs.worder.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.worder.entity.WorderIntfMessageEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单报文记录信息dao
 *
 * <AUTHOR>
 */
@Mapper
public interface WorderIntfMessageDao extends BaseMapper<WorderIntfMessageEntity> {

    /**
     * 更新是否转单
     * @param worderId
     * @param isTransfer 0:未转单,1:已转单
     * @return
     */
    Integer updateTransfer(@Param("worderId") Integer worderId, @Param("isTransfer") Integer isTransfer);

    /**
     * 获取工单报文
     * @param worderId
     * @return
     */
    String getDataByWorderId(@Param("worderId") Integer worderId);

    /**
     * 查询比亚迪推送工单网点下未转单工单报文
     * @param dotId
     * @return
     */
    List<WorderIntfMessageEntity> queryNoTransferDataByDotId(@Param("dotId") Integer dotId);

    /**
     * 查询未入库报文 (一次500条)
     * @return
     */
    List<WorderIntfMessageEntity> queryNotSaveOrder(Integer bid);

    /**
     * 更改报文状态
     * @param id
     * @param messageType 报文状态(0:未入库,1:已入库,2:入库失败)
     * @param errorMsg 失败信息
     * @return
     */
    Integer updateMessageTypeById(@Param("id") Integer id,
                                  @Param("messageType") Integer messageType,
                                  @Param("errorMsg") String errorMsg);

    /**
     * 入库成功关联工单id
     * @param id
     * @param worderId
     * @return
     */
    Integer updateWorderIdById(@Param("id") Integer id,
                               @Param("worderId") Integer worderId);

    WorderIntfMessageEntity getOneByOrderCode(@Param("bid") String bid, @Param("orderCode") String orderCode);
}
