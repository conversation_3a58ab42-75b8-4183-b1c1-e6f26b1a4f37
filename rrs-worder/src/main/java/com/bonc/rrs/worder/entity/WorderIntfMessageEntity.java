package com.bonc.rrs.worder.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/3/12 17:30
 */
@Data
@TableName("worder_intf_message")
@ApiModel(value = "工单报文记录表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorderIntfMessageEntity {

    private Integer id;

    /**
     * 品牌大类ID
     */
    private Integer bid;

    /**
     * 工单id
     */
    private Integer worderId;
    /**
     * 接口编码
     */
    private String intfCode;
    /**
     * json报文
     */
    private String data;
    /**
     * 是否转单 0:未转单,1:已转单
     */
    private Integer isTransfer;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 报文状态(0:未入库,1:已入库,2:入库失败)
     * 2-5 长安重试
     * > 6 长安失败
     */
    private Integer messageType;
    /**
     * 失败信息
     */
    private String errorMsg;
    /**
     * 车企订单号
     */
    private String orderCode;
}
