package com.bonc.rrs.worder.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.bonc.rrs.worder.aspect.CreateTime;
import com.bonc.rrs.worder.aspect.UpdateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 工单信息表
 *
 * <AUTHOR>
 */
@Data
@TableName("worder_information")
@ApiModel(value = "工单主表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorderInformationEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "工单ID")
	@TableId(value = "worder_id", type = IdType.AUTO)
	private Integer worderId;

	@ApiModelProperty(value = "工单类型")
	private Integer worderTypeId;

	@ApiModelProperty(value = "工单编号（省市简称+创建日期+工单类型+0-1000的自增序列）")
	private String worderNo;

	@ApiModelProperty(value = "工单来源类型（数据字典worder_source_type）")
	//@DicMarker(key = "worder_source_type")
	private Integer worderSourceType;

	@ApiModelProperty(value = "工单来源ID号（个人表及企业表ID）")
	private String worderSourceId;

	@ApiModelProperty(value = "工单状态（worder_status）")
	//@DicMarker(key = "worder_status")
	private Integer worderStatus;

	@ApiModelProperty(value = "创建时间")
	@CreateTime
	private Date createTime;

	@ApiModelProperty(value = "修改时间")
	@UpdateTime
	private Date modifyTime;

	@ApiModelProperty(value = "是否删除")
	@TableLogic(value = "0" ,delval = "1")
	private Integer isDelete;

	@ApiModelProperty(value = "工单来源类型冗余")
	private String worderSourceTypeValue;

	@ApiModelProperty(value = "工单状态冗余")
	private String worderStatusValue;

	@ApiModelProperty(value = "充电桩规格")
	private String chargeStandard;

	@ApiModelProperty(value = "4s店名称")
	private String sellShop;

	@ApiModelProperty(value = "充电桩型号")
	private String chargeModel;

	@ApiModelProperty(value = "车辆品牌")
	private String carBrand;

	@ApiModelProperty(value = "充电桩编号")
	private String chargeCode;

	@ApiModelProperty(value = "客户购车日期")
	private Date buyDate;

	@ApiModelProperty(value = "车辆型号")
	private String carModel;

	@ApiModelProperty(value = "车辆车架号")
	private String carVin;

	@ApiModelProperty(value = "厂商联系人")
	private String factoryLinkMan;

	@ApiModelProperty(value = "充电器cd号")
	private String chargeCd;

	@ApiModelProperty(value = "厂商联系人电话")
	private String linkManPhone;

	@ApiModelProperty(value = "用户姓名")
	private String userName;

	@ApiModelProperty(value = "客户联系电话")
	private String userPhone;

	@ApiModelProperty(value = "客户邮编")
	private String postcode;

	@ApiModelProperty(value = "车位位置冗余")
	private String parkTypeValue;

	@ApiModelProperty(value = "车位位置（数据字典 park_type）")
	//@DicMarker(key = "park_type")
	private Integer parkType;

	@ApiModelProperty(value = "个人：身份证，企业：营业执照")
	private String userCertificate;

	@ApiModelProperty(value = "电力类型（数据字典 electric_type）")
	//@DicMarker(key = "electric_type")
	private Integer electricType;

	@ApiModelProperty(value = "车位形式类型冗余")
	private String parkingTypeValue;

	@ApiModelProperty(value = "车位形式类型（数据字典 parking_type）")
	//@DicMarker(key = "parking_type")
	private Integer parkingType;

	@ApiModelProperty(value = "安装数量")
	private Integer installNum;

	@ApiModelProperty(value = "用户地址")
	//@AddressMarker
	private String address;

	@ApiModelProperty(value = "客户提车日期")
	private Date getDate;

	@ApiModelProperty(value = "用户地址冗余")
	private String addressDup;

	@ApiModelProperty(value = "电力类型冗余")
	private String electricTypeValue;

	@ApiModelProperty(value = "营业执照或身份证照片")
	private String files;

	@ApiModelProperty(value = "当前办理人")
	private String candidate;

	@ApiModelProperty(value = "客服")
	//@CreateBy
	private Long createBy;

	@ApiModelProperty(value = "工单创建人")
	private Integer creator;

	@TableField(exist = false)
    private String createByName;
	@ApiModelProperty(value = "最后修改人")
	//@UpdateBy
	private Long updateBy;

	@ApiModelProperty(value = "项目组")
	private String candidatePm;

	@ApiModelProperty(value = "网点组")
	private String candidateBranch;

	@ApiModelProperty(value = "服务组")
	private String candidateAttendant;

	@ApiModelProperty(value = "工单执行状态（数据字典worder_exec_status）")
	//@DicMarker(key="worder_exec_status")
	private Integer worderExecStatus;

	@ApiModelProperty(value = "工单结算状态（数据字典worder_set_status）")
	//@DicMarker(key="worder_set_status")
	private Integer worderSetStatus;

	@ApiModelProperty(value = "工单增项结算状态（数据字典worder_Incre_status）")
	//@DicMarker(key="worder_Incre_status")
	private Integer worderIncreStatus;

	@ApiModelProperty(value = "工单激励状态（数据字典worder_exci_status）")
	//@DicMarker(key="worder_exci_status")
	private Integer worderExciStatus;

	@ApiModelProperty(value = "工单执行状态冗余")
	private String worderExecStatusValue;

	@ApiModelProperty(value = "工单结算状态冗余")
	private String worderSetStatusValue;

	@ApiModelProperty(value = "工单增项结算状态冗余")
	private String worderIncreStatusValue;

	@ApiModelProperty(value = "工单激励状态冗余")
	private String worderExciStatusValue;

	@ApiModelProperty(value = "服务经理id")
	private Integer pmId;

	@ApiModelProperty(value = "网点id")
	private Integer dotId;

	@ApiModelProperty(value = "服务兵id")
	private Integer serviceId;

	@ApiModelProperty(value = "工单模板id")
	private Integer templateId;

	@ApiModelProperty(value = "厂商结算总金额")
	private BigDecimal companyBalanceFee;


	//厂商结算税额
	private BigDecimal companyBalanceFeeTax;
	//厂商结算总金额(含税)
	private BigDecimal companyBalanceFeeSum;

	private BigDecimal dotBalanceFeeSum;

	private BigDecimal dotBalanceFeeTax;

	@ApiModelProperty(value = "网点工单结算总金额")
	private BigDecimal dotBalanceFee;

	@ApiModelProperty(value = "网点增项结算总金额")
	private BigDecimal dotIncreBalanceFee;

	@ApiModelProperty(value = "网点给客户的优惠金额")
	private BigDecimal dotIncreDiscountAmount;

	@ApiModelProperty(value = "服务兵结算总金额")
	private BigDecimal attendantBalanceFee;

	@ApiModelProperty(value = "发票id")
	private Integer invoiceId;

	@ApiModelProperty(value = "工单结算发布时间")
	private Date worderPublishTime;

	@ApiModelProperty(value = "增项结算发表时间")
	private Date worderIncrePublishTime;

	@ApiModelProperty(value = "工单完成时间")
	private Date worderFinishTime;

	@ApiModelProperty(value = "区级id")
	private Integer areaId;

	@ApiModelProperty(value = "客户类型")
	private Integer userType;

	@ApiModelProperty(value = "厂商id")
	private Integer companyId;

	@ApiModelProperty(value = "车企订单号")
	private String companyOrderNumber;

	@ApiModelProperty(value = "金牌订单号")
	private String medalOrderNumber;

	@ApiModelProperty(value = "勘测单号")
	private String conveyOrderNumber;

	@ApiModelProperty(value = "安装单号")
	private String installOrderNumber;

	@TableField(exist = false)
	private List<WorderTypeEntity> worderTypeList;

	@TableField(exist = false)
	private String dotShortName;

	@TableField(exist = false)
	private List<WorderExtFieldEntity> worderExtFieldList;
	/**
	 * 流程实例id
	 */
	@ApiModelProperty(value = "流程实例id", required = false)
	private String procInstId;

	@ApiModelProperty(value = "客户申请电力报桩时间")
	private String cusApplyPowerTime;

	@ApiModelProperty(value = "客户电力报桩预计完成时间")
	private String cusExpectPowerTime;

	@ApiModelProperty(value = "客户电力报桩实际完成时间")
	private String cusRealPowerTime;

	@ApiModelProperty(value = "电力报桩备注")
	private String description;

	@ApiModelProperty(value = "确认到桩 1:到桩")
	private String chargeReach;

	// 勘测预约时间
	@ApiModelProperty(value = "勘测预约时间")
	private String conveyAppointTime;
	// 安装预约时间
	@ApiModelProperty(value = "安装预约时间")
	private String installAppointTime;
	// 勘测签退时间
	@ApiModelProperty(value = "勘测签退时间")
	private String conveySignOutTime;
	// 安装签退时间
	@ApiModelProperty(value = "安装签退时间")
	private String installSignOutTime;
	@ApiModelProperty(value = "用户增项结算实际收费金额")
	private BigDecimal userActualCost;

	// 勘测上传图片地址
	@TableField(exist = false)
	private String conveySignUrl;
	// 安装上传图片地址
	@TableField(exist = false)
	private String installSignUrl;
	@TableField(exist = false)
	private StringBuilder reason;
	@TableField(exist = false)
	private String carName ;
	@TableField(exist = false)
	private  String remark;
	@TableField(exist = false)
	private String carVinDe ;
	@TableField(exist = false)
	private String shopName;
	@TableField(exist = false)
	private String province;
	@TableField(exist = false)
	private String city;
	@TableField(exist = false)
	private String district;
	@TableField(exist = false)
	private String region;
	@TableField(exist = false)
	private String workType;
	@TableField(exist = false)
	private String carType;
	@TableField(exist = false)
	private String clienter;
	@TableField(exist = false)
	private String createTimeDesc;

	@TableField(exist = false)
	private String rnNumber;
	@TableField(exist = false)
	private String cdNumber;
	@TableField(exist = false)
	private String jfPerdon;
	@TableField(exist = false)
	private String orderType;
	@TableField(exist = false)
	private String planTime;

	@TableField(exist = false)
	private String qsPerson;

	@TableField(exist = false)
	private String orderNumber;


	@TableField(exist = false)
	private String processCode;

	@TableField(exist = false)
	private String processStatus;

	/**
	 * 网点管理员联系电话
	 */
	@TableField(exist = false)
	private String contactsPhone;

	/**
	 * 品牌id
	 */
	@TableField(exist = false)
	private Integer brandId;
}
