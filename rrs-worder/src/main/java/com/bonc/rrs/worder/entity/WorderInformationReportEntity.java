package com.bonc.rrs.worder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 工单固化表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("worder_information_report")
@ApiModel(value="WorderInformationReportEntity对象", description="工单固化表")
public class WorderInformationReportEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "工单编号")
    private String worderNo;

    @ApiModelProperty(value = "车企订单号")
    private String companyOrderNumber;

    @ApiModelProperty(value = "工单创建日期")
    private LocalDateTime createTimeNew;

    @ApiModelProperty(value = "车企派单日期")
    private LocalDateTime companyOrderTime;

    @ApiModelProperty(value = "RN号")
    private String rnNo;

    @ApiModelProperty(value = "套包名称")
    private String suitName;

    @ApiModelProperty(value = "省份")
    private String pro;

    @ApiModelProperty(value = "地市")
    private String city;

    @ApiModelProperty(value = "区县")
    private String county;

    @ApiModelProperty(value = "工贸名称")
    private String branchName;

    @ApiModelProperty(value = "品牌")
    private String brandName;

    @ApiModelProperty(value = "客户姓名")
    private String userName;

    @ApiModelProperty(value = "服务经理")
    private String candidatePm;

    @ApiModelProperty(value = "客服")
    private String employeeName;

    @ApiModelProperty(value = "网点")
    private String candidateBranch;

    @ApiModelProperty(value = "服务兵")
    private String candidateAttendant;

    @ApiModelProperty(value = "工单模板")
    private String templateId;

    @ApiModelProperty(value = "工单主状态")
    private String detailNamea;

    @ApiModelProperty(value = "工单执行状态")
    private String detailNameb;

    @ApiModelProperty(value = "工单结算状态")
    private String detailNamec;

    @ApiModelProperty(value = "工单增项结算状态")
    private String detailNamed;

    @ApiModelProperty(value = "工单激励状态")
    private String detailNamee;

    @ApiModelProperty(value = "增项付费状态")
    private String paystate;

    @ApiModelProperty(value = "申请开票状态")
    private String apply;

    @ApiModelProperty(value = "发票编号")
    private String invoiceCode;

    @ApiModelProperty(value = "客服派单给项目经理的时间")
    private LocalDateTime custPmTime;

    @ApiModelProperty(value = "项目经理派单给网点时间")
    private LocalDateTime pmSendDotTime;

    @ApiModelProperty(value = "网点分配给服务兵时间")
    private LocalDateTime dotAttendTime;

    @ApiModelProperty(value = "预约勘测成功时间")
    private LocalDateTime conveyAppointTime;

    @ApiModelProperty(value = "实际勘测完成日期")
    private LocalDateTime actConveyDate;

    @ApiModelProperty(value = "预约安装成功时间")
    private LocalDateTime installAppointTime;

    @ApiModelProperty(value = "实际安装完成日期")
    private LocalDateTime actInstallDate;

    @ApiModelProperty(value = "勘测签到时间")
    private LocalDateTime conveySignTime;

    @ApiModelProperty(value = "安装签到时间")
    private LocalDateTime installSignTime;

    @ApiModelProperty(value = "项目经理审核勘测资料不通过时间")
    private LocalDateTime pmConveyAuditFailTime;

    @ApiModelProperty(value = "客服审核勘测资料不通过时间")
    private LocalDateTime custConveyAuditFailTime;

    @ApiModelProperty(value = "项目经理审核安装资料不通过时间")
    private LocalDateTime pmInstallAuditFailTime;

    @ApiModelProperty(value = "客服审核安装资料不通过时间")
    private LocalDateTime custInstallAuditFailTime;

    @ApiModelProperty(value = "服务兵提交勘测资料时间")
    private LocalDateTime conveyFileTime;

    @ApiModelProperty(value = "项目经理确认勘测资料时间")
    private LocalDateTime pmConveyAuditTime;

    @ApiModelProperty(value = "客服确认勘测资料时间")
    private LocalDateTime custConveyAuditTime;

    @ApiModelProperty(value = "车企确认勘测完成时间")
    private LocalDateTime compConveyAuditTime;

    @ApiModelProperty(value = "确认到桩时间")
    private LocalDateTime confirmPowerTime;

    @ApiModelProperty(value = "预约勘测时间")
    private LocalDateTime conveyBookTime;

    @ApiModelProperty(value = "预约安装时间")
    private LocalDateTime installBookTime;

    @ApiModelProperty(value = "服务兵提交安装资料时间")
    private LocalDateTime installDocTime;

    @ApiModelProperty(value = "项目经理确认安装资料时间")
    private LocalDateTime pmInstallAduitTime;

    @ApiModelProperty(value = "客服确认安装资料时间")
    private LocalDateTime confirmCompletionTime;

    @ApiModelProperty(value = "车企确认安装完成时间")
    private LocalDateTime compInstallAduitTime;

    @ApiModelProperty(value = "下次联系时间")
    private LocalDateTime nextContactTime;

    @ApiModelProperty(value = "是否自动派单")
    private String isAuto;

    @ApiModelProperty(value = "工单新增备注")
    private String worderRemark;

    @ApiModelProperty(value = "工单创建时间")
    private String createTime;

    @ApiModelProperty(value = "工单类型冗余")
    private String worderTypeId;

    @ApiModelProperty(value = "区域id")
    private Integer areaId;

    @ApiModelProperty(value = "品牌id")
    private Integer brandId;

    @ApiModelProperty(value = "车企名称冗余")
    private String companyName;
}
