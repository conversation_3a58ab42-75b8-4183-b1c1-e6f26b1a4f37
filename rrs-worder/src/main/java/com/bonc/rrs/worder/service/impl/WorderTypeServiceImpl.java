package com.bonc.rrs.worder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.serviceprovider.service.ProviderBusinessService;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.dao.WorderTypeDao;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.WorderTypeEntity;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.service.WorderTypeService;
import com.bonc.rrs.worderapp.constant.FieldConstant;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 工单类型业务层
 * <AUTHOR>
 * @date 2020/3/2310:18
 */
@Service("worderTypeService")
@Slf4j
public class WorderTypeServiceImpl extends ServiceImpl<WorderTypeDao, WorderTypeEntity> implements WorderTypeService {

    @Autowired(required = false)
    WorderInformationDao worderInformationDao;
    @Autowired(required = false)
    WorderInformationService worderInformationService;
    @Autowired
    ProviderBusinessService providerBusinessService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<WorderTypeEntity> page =  baseMapper.getList(new Query<WorderTypeEntity>().getPageNotI(params),params);

        return new PageUtils(page);
    }

    /**
     * 根据id查询详情
     * @param id
     * @return
     */
    @Override
    public List<WorderTypeEntity> queryById(Integer id){
        List<WorderTypeEntity> worderTypeList = new ArrayList<>();
        WorderTypeEntity worderType = this.getById(id);
        worderTypeList.add(worderType);
        if(!StringUtils.isEmpty(worderType.getPids())){
            String[] pids = worderType.getPids().split(",");
            Arrays.stream(pids).forEach(pid ->{
                worderTypeList.add(this.getById(pid));
            });
        }
        return worderTypeList;
    }

    /**
     * 添加工单类型
     * @param worderType
     */
    @Override
    public void saveWorderType(WorderTypeEntity worderType){
        worderType.setCreateTime(new Date());
        this.save(worderType);
    }

    /**
     * 修改工单类型
     */
    @Override
    public void updateWorderType(WorderTypeEntity worderType){
        worderType.setUpdateTime(new Date());
        this.updateById(worderType);
    }

    /**
     * 提供类型级别级联查询数据
     * @param pid
     * @return
     */
    @Override
    public List<WorderTypeEntity> queryType(Integer pid){
        if(pid != null){
            //查询二级类型
           return this.list(new QueryWrapper<WorderTypeEntity>().eq("pid",pid));
        }else {
            //查询一级类型
           return this.list(new QueryWrapper<WorderTypeEntity>().eq("level",1));
        }
    }

    public R checkWorderType(Integer worderTypeId, String event, WorderInfoEntity worderInfo){
        Integer worderStatus = null;
        Integer worderExecStatus = null;
        if(worderTypeId == null){
            worderTypeId = IntegerEnum.ZERO.getValue();
        }
        switch (worderTypeId){
            // 服务-安装类型
            case 2:
                // 派单给服务兵事件结束
                if (WarningConstant.TRIGGER_EVENT_SEND_WORDER_TO_SOLDIER.equals(event)){
                    worderInformationService.updateStatus(worderInfo.getWorderId(), IntegerEnum.ONE.getValue());
                    worderExecStatus = FieldConstant.WAIT_CHARGING_PILE;
                }
//                worderStatus = FieldConstant.WORDER_INSTALL_STATUS;
//                worderExecStatus = FieldConstant.WAIT_CHARGING_PILE;
//                // 主状态安装中
//                worderInfo.setWorderStatus(worderStatus);
//                // 安装待预约
//                worderInfo.setWorderExecStatus(worderExecStatus);
//                worderInformationDao.updateWorderInfo(worderInfo);
//                log.debug("检查工单类型： 服务-安装类型 【" + worderInfo + "】");
                break;

            // 服务-勘测类型
            case 4:
                // 车企确认勘测完成
                if(WarningConstant.COMPANY_FINISH_CONVEY.equals(event)){
                    // 车企确认安装完成
                    worderInformationService.updateStatus(worderInfo.getWorderId(), IntegerEnum.TWO.getValue());
                    worderExecStatus = FieldConstant.INSTALL_END;
                }
//                worderStatus = FieldConstant.WORDER_FINISHED;
//                worderExecStatus = FieldConstant.INSTALL_END;
//                // 主状态结算中
//                worderInfo.setWorderStatus(worderStatus);
//                // 安装完成
//                worderInfo.setWorderExecStatus(worderExecStatus);
//                // 工单待计算
//                worderInfo.setWorderSetStatus(WorderSetStatusEnum.WORDER_WAIT_CALCULATE.getValue());
//                worderInfo.setWorderFinishTime(new Date());
//                worderInformationDao.updateWorderInfo(worderInfo);
//                log.debug("检查工单类型： 服务-勘测类型 【" + worderInfo + "】");
                break;

            // 服务-勘测安装类型
            case 5:
                log.debug("检查工单类型： 服务-勘测安装类型 【" + worderInfo + "】");
                break;

            // 服务-维修
            case 6:
                // 接口推送的比亚迪维修单跳过逻辑
                if (WarningConstant.TRIGGER_EVENT_SEND_WORDER_TO_SOLDIER.equals(event)
                 && !providerBusinessService.checkBydOrderByWorderId(worderInfo.getWorderId())){
                    worderInformationService.updateStatus(worderInfo.getWorderId(), IntegerEnum.FOUR.getValue());
                    worderExecStatus = FieldConstant.INSTALL_NOT_APPOINT;
                }
//                worderStatus = FieldConstant.WORDER_INSTALL_STATUS;
//                worderExecStatus = FieldConstant.WAIT_CHARGING_PILE;
//                // 主状态安装中
//                worderInfo.setWorderStatus(worderStatus);
//                // 安装待预约
//                worderInfo.setWorderExecStatus(worderExecStatus);
//                worderInformationDao.updateWorderInfo(worderInfo);
//                log.debug("检查工单类型： 服务-安装类型 【" + worderInfo + "】");
                break;
            default:
                log.debug("检查工单类型： 默认 【" + worderInfo + "】");
                break;
        }
        return R.ok().putWorderExecStatus(worderExecStatus);
    }

    /**
     * 检查工单类型修改工单状态
     * @param
     * @return
     */
    @Override
    public R checkWorderTypeByWorderNo(String worderNo, String event){
        R r = new R();
        MapUtils params = new MapUtils().put("worder_no", worderNo);
        List<WorderInformationEntity> worderInformationEntities = worderInformationDao.selectByMap(params);
        Integer worderTypeId = null;
        if(worderInformationEntities.size() > IntegerEnum.ZERO.getValue() && null != worderInformationEntities.get(IntegerEnum.ZERO.getValue())){
            WorderInformationEntity worderInformationEntity = worderInformationEntities.get(IntegerEnum.ZERO.getValue());
            worderTypeId = worderInformationEntity.getWorderTypeId();
            WorderInfoEntity worderInfo = new WorderInfoEntity();
            worderInfo.setWorderId(worderInformationEntity.getWorderId());
            r = checkWorderType(worderTypeId,event, worderInfo);
        }
        return r;
    }

    @Override
    public R checkWorderTypeByWorderId(Integer worderId, String event) {
        R r = new R();
        MapUtils params = new MapUtils().put("worder_id", worderId);
        List<WorderInformationEntity> worderInformationEntities = worderInformationDao.selectByMap(params);
        Integer worderTypeId = null;
        if(worderInformationEntities.size() > IntegerEnum.ZERO.getValue() && null != worderInformationEntities.get(IntegerEnum.ZERO.getValue())){
            WorderInformationEntity worderInformationEntity = worderInformationEntities.get(IntegerEnum.ZERO.getValue());
            worderTypeId = worderInformationEntity.getWorderTypeId();
            WorderInfoEntity worderInfo = new WorderInfoEntity();
            worderInfo.setWorderId(worderId);
            r = checkWorderType(worderTypeId, event, worderInfo);
        }
        return r;
    }

    @Override
    public R selectInfo(Integer worderId) {
        MapUtils params = new MapUtils().put("worder_id", worderId);
        List<WorderInformationEntity> worderInformationEntities = worderInformationDao.selectByMap(params);
        return R.ok().putList(worderInformationEntities);
    }
}
