package com.bonc.rrs.worder.dto.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description 运营数据看板详情数据
 * @Date 2022/12/12 16:04
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CockpitDataStatisticsDto {

    /**
     * 公司名称
     */
    private String brandName;

    /**
     * 省分名称
     */
    private String provinceName;

    /**
     * 品牌下各省分工单数量
     */
    private Integer provinceNum;
}
