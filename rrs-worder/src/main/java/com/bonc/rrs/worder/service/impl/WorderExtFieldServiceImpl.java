package com.bonc.rrs.worder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.worder.dao.WorderExtFieldDao;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.dto.dto.FieldDto;
import com.bonc.rrs.worder.entity.ExtFieldEntity;
import com.bonc.rrs.worder.entity.ExtFieldFactoryEntity;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.general.QuerySpecification;
import com.bonc.rrs.worder.service.ExtFieldFactoryService;
import com.bonc.rrs.worder.service.ExtFieldService;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import com.bonc.rrs.worderapp.entity.vo.DataVo;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WorderExtFieldConstant;
import com.youngking.lenmoncore.common.utils.*;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUploadRecordEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryDetailService;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryService;
import com.youngking.renrenwithactiviti.modules.sys.service.SysUploadRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Service("worderExtFieldService")
public class WorderExtFieldServiceImpl extends ServiceImpl<WorderExtFieldDao, WorderExtFieldEntity> implements WorderExtFieldService {

	private final SysDictionaryService sysDictionaryService;
	private final SysDictionaryDetailService sysDictionaryDetailService;
	private final SysUploadRecordService sysUploadRecordService;
	private final ExtFieldService extFieldService;
	private final ExtFieldFactoryService extFieldFactoryService;

	@Autowired(required = false)
	WorderInformationDao worderInformationDao;

	public WorderExtFieldServiceImpl(SysDictionaryService sysDictionaryService, SysDictionaryDetailService sysDictionaryDetailService, SysUploadRecordService sysUploadRecordService, ExtFieldService extFieldService, ExtFieldFactoryService extFieldFactoryService) {
		this.sysDictionaryService = sysDictionaryService;
		this.sysDictionaryDetailService = sysDictionaryDetailService;
		this.sysUploadRecordService = sysUploadRecordService;
		this.extFieldService = extFieldService;
		this.extFieldFactoryService = extFieldFactoryService;
	}

    /*@Override
	@Transactional
	public boolean saveBatch(Collection<WorderExtFieldEntity> entityList) {
		List<ExtFieldEntity> extFields = new ArrayList<>(extFieldService.listByIds(entityList.stream().map(WorderExtFieldEntity::getFieldId).collect(Collectors.toList())));
		List<ExtFieldEntity> dicList = extFields.stream().filter(extFieldEntity -> extFieldEntity.getFieldType() == 1).collect(Collectors.toList());
		List<ExtFieldEntity> multiDicList = extFields.stream().filter(extFieldEntity -> extFieldEntity.getFieldType() == 2).collect(Collectors.toList());
		List<ExtFieldEntity> urlList = extFields.stream().filter(extFieldEntity -> extFieldEntity.getFieldType() == 3).collect(Collectors.toList());

		dicList.parallelStream().forEach(extFieldEntity -> handleDic(extFieldEntity, entityList.stream().filter(
				worderExtFieldEntity -> worderExtFieldEntity.getFieldId().equals(extFieldEntity.getFieldId())
				).findFirst().get()
		));

		multiDicList.parallelStream().forEach(extFieldEntity -> handleMultiDic(extFieldEntity, entityList.stream().filter(
				worderExtFieldEntity -> worderExtFieldEntity.getFieldId().equals(extFieldEntity.getFieldId())
				).findFirst().get()
		));

		urlList.parallelStream().forEach(extFieldEntity -> handleUrl(extFieldEntity, entityList.stream().filter(
				worderExtFieldEntity -> worderExtFieldEntity.getFieldId().equals(extFieldEntity.getFieldId())
				).findFirst().get()
		));
		return super.saveBatch(entityList);
	}*/

	private void handleDic(ExtFieldEntity extFieldEntity, WorderExtFieldEntity worderExtFieldEntity) {
		worderExtFieldEntity.setFieldValueDup(
				sysDictionaryDetailService.getOne(new QueryWrapper<>(
								SysDictionaryDetailEntity.builder()
										.dictionaryId(
												sysDictionaryService.getById(extFieldEntity.getFiledDicKey()).getId()
										)
										.detailNumber(worderExtFieldEntity.getFieldValue())
										.build()
						)
				).getDetailName()
		);
	}

	private void handleMultiDic(ExtFieldEntity extFieldEntity, WorderExtFieldEntity worderExtFieldEntity) {
		List<String> dics = Arrays.asList(worderExtFieldEntity.getFieldValue().split(","));
		Set<String> values = new HashSet<>();
		dics.parallelStream().forEach(s ->
				values.add(
						sysDictionaryDetailService.getOne(new QueryWrapper<>(
										SysDictionaryDetailEntity.builder()
												.dictionaryId(
														sysDictionaryService.getById(extFieldEntity.getFiledDicKey()).getId()
												)
												.detailNumber(s)
												.build()
								)
						).getDetailName()
				)
		);
		StringBuffer dicDup = new StringBuffer();
		values.forEach(s -> {
			dicDup.append(s);
			dicDup.append(",");
		});
		worderExtFieldEntity.setFieldValueDup(dicDup.substring(0, dicDup.length() - 1));
	}

	private void handleUrl(ExtFieldEntity extFieldEntity, WorderExtFieldEntity worderExtFieldEntity) {
		List<String> urls = this.sysUploadRecordService.list(new QueryWrapper<>(SysUploadRecordEntity.builder()
				.uuid(worderExtFieldEntity.getFieldValue())
				.build())).stream().map(SysUploadRecordEntity::getFileUrl).collect(Collectors.toList());
		StringBuffer urlDup = new StringBuffer();
		urls.forEach(s -> {
			urlDup.append(s);
			urlDup.append(",");
		});
		worderExtFieldEntity.setFieldValueDup(urlDup.substring(0, urlDup.length() - 1));
	}

	@Override
	public List<FieldDto> getDtoListByWorderNo(String worderNo) {
		List<FieldDto> rtn = new ArrayList<>();
		List<WorderExtFieldEntity> worderExtFieldEntities = this.list(new QueryWrapper<>(WorderExtFieldEntity.builder().worderNo(worderNo).build()));
		worderExtFieldEntities.parallelStream().forEach(worderExtFieldEntity ->
				rtn.add(new FieldDto(
						extFieldService.getOne(new QueryWrapper<>(ExtFieldEntity.builder().fieldId(worderExtFieldEntity.getFieldId()).build()))
						, extFieldFactoryService.getOne(new QueryWrapper<>(ExtFieldFactoryEntity.builder().fieldId(worderExtFieldEntity.getFieldId()).companyNo(worderNo).build()))
						, worderExtFieldEntity
				)));
		return rtn;
	}

	@Override
	public PageUtils queryPage(Map<String, Object> params) {
		IPage<WorderExtFieldEntity> page = this.page(
				new Query<WorderExtFieldEntity>().getPage(params),
				new QuerySpecification<WorderExtFieldEntity>().getWrapper(params, WorderExtFieldEntity.class)
		);

		return new PageUtils(page);
	}

	@Override
	public R listWorderExtFieldByPrimaryKey(Map params) {
		R r = R.ok();
		if(params.get("fieldValue") == null || StringUtils.isBlank(params.get("fieldValue") + "")){
			r.putNum(0);
			return r;
		}
		List<WorderExtFieldEntity> worderExtFieldEntities = baseMapper.selectList(new QueryWrapper<WorderExtFieldEntity>()
				.eq("field_id", params.get("fieldId")).eq("field_value", params.get("fieldValue")));
		int worderNum = worderExtFieldEntities.size();
		if(worderNum == IntegerEnum.ONE.getValue() && null != worderExtFieldEntities.get(IntegerEnum.ZERO.getValue())){
			String worderNo = worderExtFieldEntities.get(IntegerEnum.ZERO.getValue()).getWorderNo();
			List<WorderExtFieldEntity> worderExtFieldList = baseMapper.selectList(new QueryWrapper<WorderExtFieldEntity>()
					.eq("worder_no", worderNo));
			r.putList(worderExtFieldList);
			r.putWorderNo(worderNo);
		}
		r.putNum(worderNum);
		return r;
	}

	@Override
	public R getConveyWorderByInstallWorderNo(String worderNo) {
		WorderInformationEntity worderInformationEntity = new WorderInformationEntity();
		String conveyWorderNo = null;
		WorderExtFieldEntity primaryKeyField = getPrimaryKeyField(worderNo);
		if (primaryKeyField != null) {
			List<WorderExtFieldEntity> worderExtFieldEntities = baseMapper.selectList(new QueryWrapper<WorderExtFieldEntity>()
					.eq("field_id", primaryKeyField.getFieldId()).eq("field_value", primaryKeyField.getFieldValue())
					.ne("worder_no", worderNo));
			if(worderExtFieldEntities.size() > IntegerEnum.ZERO.getValue() && null != worderExtFieldEntities.get(IntegerEnum.ZERO.getValue())){
				conveyWorderNo = worderExtFieldEntities.get(IntegerEnum.ZERO.getValue()).getWorderNo();
			}
		}
		if (conveyWorderNo != null) {
			List<WorderInformationEntity> worderInformationEntities = worderInformationDao.selectList(
					new QueryWrapper<WorderInformationEntity>().eq("worder_no", conveyWorderNo));
			if(worderInformationEntities.size() > IntegerEnum.ZERO.getValue()){
				worderInformationEntity = worderInformationEntities.get(IntegerEnum.ZERO.getValue());
			}
		}
		return R.ok().put("worder", worderInformationEntity);
	}

	@Override
	public WorderExtFieldEntity getPrimaryKeyField(String worderNo) {
		WorderExtFieldEntity worderExtFieldEntity = null;
		List<WorderExtFieldEntity> conveyPrimaryKeyFieldList = baseMapper.getConveyPrimaryKey(new MapUtils().put(
				"worderNo", worderNo).put("key", WorderExtFieldConstant.WORDER_PRIMARY_KEY));
		if(conveyPrimaryKeyFieldList.size() > IntegerEnum.ZERO.getValue() && null != conveyPrimaryKeyFieldList.get(IntegerEnum.ZERO.getValue())){
			worderExtFieldEntity = conveyPrimaryKeyFieldList.get(IntegerEnum.ZERO.getValue());
		}
		return worderExtFieldEntity;
	}

	@Override
	public List<DataVo> getFieldIdByWorderNo(String worderNo,String purpose){
		return baseMapper.getFieldIdByWorderNo(worderNo,purpose);
	}

	@Override
	public R checkFieldRequired(String worderNo, Integer fieldPurpose) {
		List<WorderExtFieldEntity> list = this.baseMapper.selectListFieldRequired(worderNo,fieldPurpose);
		if (list == null || list.isEmpty()) {
			return R.ok();
		}
		List<String> msgs = new ArrayList<>();
		for (WorderExtFieldEntity entity : list) {
			if(StringUtils.isBlank(entity.getFieldValue())){
				msgs.add(entity.getFieldName());
			}
		}
		if(!msgs.isEmpty()){
			return R.error("请上传资料：" + String.join(",", msgs));
		}
		return R.ok();
	}

	@Override
	public List<WorderExtFieldEntity> getFieldsByWorderNo(String worderNo) {
		return this.baseMapper.getFieldsByWorderNo(worderNo);
	}

    @Override
    public void setFieldValueNull(Integer id) {
		this.baseMapper.setFieldValueNull(id);
    }

	@Override
	public List<Long> getFieldIdsByWorderNoAndPurpose(String worderNo, String purpose) {
		return this.baseMapper.getFieldIdsByWorderNoAndPurpose(worderNo, purpose);
	}

	@Override
	public List<WorderExtFieldEntity> getSpecificFields(String worderNo, Collection<Integer> fieldIds) {
		 return this.baseMapper.getSpecificFields(worderNo, fieldIds);

	}


}