package com.bonc.rrs.worder.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.worder.entity.WorderInformationAttributeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/18 16:43
 */
@Mapper
public interface WorderInformationAttributeDao extends BaseMapper<WorderInformationAttributeEntity> {
    Integer insertBatch(@Param("worderInformationAttributeList") List<WorderInformationAttributeEntity> worderInformationAttributeList);

    Integer insertOutBound(@Param("worderId") Integer worderId,@Param("fieldName") String fieldName,@Param("leaveCode") String leaveCode);

    Integer updateDelete(@Param("worderId") Integer worderId,@Param("attribute") String attribute,@Param("attributeCode") String attributeCode);

    Integer insertFixSubmit(@Param("worderId") Integer worderId,@Param("fieldName") String fieldName);

    Integer insertCPIMCancelOrder(@Param("worderId") Integer worderId,@Param("fieldName") String fieldName,@Param("fieldValue") String fieldValue,@Param("fieldDesc") String fieldDesc);

    WorderInformationAttributeEntity selectAttributeByWorderNo(@Param("worderNo") String worderNo, @Param("attributeCode") String attributeCode, @Param("attribute") String attribute);

    WorderInformationAttributeEntity selectAttributeByWorderNoAttVal(@Param("worderNo") String worderNo, @Param("attributeCode") String attributeCode, @Param("attribute") String attribute, @Param("attributeValue") String attributeValue);

    WorderInformationAttributeEntity selectAttributeByWorderId(@Param("worderId") String worderId, @Param("attributeCode") String attributeCode, @Param("attribute") String attribute);

    WorderInformationAttributeEntity selectAttributeByCompanyaWorderNo(@Param("companyaWorderNo") String companyaWorderNo, @Param("attributeCode") String attributeCode, @Param("attribute") String attribute);

    Integer insertMdmCancelOrder(@Param("worderId") Integer worderId, @Param("attributeName") String attributeName,
                                 @Param("attributeValue") String attributeValue, @Param("attribute") String fieldDesc);

    WorderInformationAttributeEntity selectByWorderNo(@Param("worderNo") String worderNo, @Param("attributeCode") String attributeCode);
}
