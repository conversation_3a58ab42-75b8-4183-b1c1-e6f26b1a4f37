package com.bonc.rrs.notif.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.byd.domain.PushNotifyReadRecord;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.notif.dao.NotifyMsgMapper;
import com.bonc.rrs.notif.domain.NotifyMsg;
import com.bonc.rrs.notif.vo.NotifyMsgVo;
import com.bonc.rrs.util.OrderStatus;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderInformationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotifyMsgServiceImpl extends ServiceImpl<NotifyMsgMapper, NotifyMsg> implements NotifyMsgService {

    private final NotifyMsgMapper notifyMsgMapper;
    private final IBydApiService bydApiService;
    private final WorderInformationService worderInformationService;

    @Override
    public Page<NotifyMsgVo> getNotifyMsgVoList(Integer notifyType, String orderCode, Integer readStatus, Date startTime, Date endTime, Integer pageSize, Integer pageNum) {
        IPage<NotifyMsg> page = this.lambdaQuery()
                .eq(notifyType != null, NotifyMsg::getNotifyType, notifyType)
                .eq(StringUtils.isNotBlank(orderCode), NotifyMsg::getOrderCode, orderCode)
                .eq(readStatus != null, NotifyMsg::getReadStatus, readStatus)
                .ge(startTime != null, NotifyMsg::getCreateTime, startTime)
                .le(endTime != null, NotifyMsg::getCreateTime, endTime)
                .orderByDesc(NotifyMsg::getId)
                .page(new Page<>(pageNum, pageSize));

        Page<NotifyMsgVo> retPage = new Page<>(pageNum, pageSize);

        if (!page.getRecords().isEmpty()) {
            Map<String, Integer> orderCodeStatusMap = worderInformationService.lambdaQuery()
                    .select(WorderInformationEntity::getCompanyOrderNumber, WorderInformationEntity::getWorderStatus)
                    .in(WorderInformationEntity::getCompanyOrderNumber, page.getRecords().stream().map(NotifyMsg::getOrderCode).collect(Collectors.toList()))
                    .list()
                    .stream()
                    .collect(Collectors.toMap(WorderInformationEntity::getCompanyOrderNumber, WorderInformationEntity::getWorderStatus, (e1, e2) -> e2));

            List<NotifyMsgVo> collect = page.getRecords().stream().map(notifyMsg -> {
                NotifyMsgVo msgVo = new NotifyMsgVo();
                BeanUtils.copyProperties(notifyMsg, msgVo);
                msgVo.setWorderStatus(orderCodeStatusMap.get(msgVo.getOrderCode()));
                msgVo.setWorderStatusValue(OrderStatus.getByCode(msgVo.getWorderStatus()));
                return msgVo;
            }).collect(Collectors.toList());
            retPage.setRecords(collect);
        }
        retPage.setTotal(page.getTotal());
        return retPage;
    }

    @Override
    public List<NotifyMsgVo> exportNotifyMsg(Integer notifyType, String orderCode, Integer readStatus, Date startTime, Date endTime) {
        List<NotifyMsg> list = this.lambdaQuery()
                .eq(notifyType != null, NotifyMsg::getNotifyType, notifyType)
                .eq(StringUtils.isNotBlank(orderCode), NotifyMsg::getOrderCode, orderCode)
                .eq(readStatus != null, NotifyMsg::getReadStatus, readStatus)
                .ge(startTime != null, NotifyMsg::getCreateTime, startTime)
                .le(endTime != null, NotifyMsg::getCreateTime, endTime)
                .list();

        Map<String, Integer> orderCodeStatusMap = worderInformationService.lambdaQuery()
                .select(WorderInformationEntity::getCompanyOrderNumber, WorderInformationEntity::getWorderStatus)
                .in(WorderInformationEntity::getCompanyOrderNumber, list.stream().map(NotifyMsg::getOrderCode).collect(Collectors.toList()))
                .list()
                .stream()
                .collect(Collectors.toMap(WorderInformationEntity::getCompanyOrderNumber, WorderInformationEntity::getWorderStatus, (e1, e2) -> e2));

        return list.stream().map(notifyMsg -> {
            NotifyMsgVo msgVo = new NotifyMsgVo();
            BeanUtils.copyProperties(notifyMsg, msgVo);
            msgVo.setWorderStatus(orderCodeStatusMap.get(msgVo.getOrderCode()));
            msgVo.setWorderStatusValue(OrderStatus.getByCode(msgVo.getWorderStatus()));
            msgVo.setReadStatusValue(msgVo.getReadStatus() == 1 ? "已读" : "未读");
            return msgVo;
        }).collect(Collectors.toList());
    }

    @Override
    public void markNotifyMsgAsRead(List<Long> ids, String username) {
        if (ids == null || ids.isEmpty()) {
            return;
        }

        List<NotifyMsg> list = this.lambdaQuery()
                .eq(NotifyMsg::getReadStatus, 0)
                .in(NotifyMsg::getId, ids)
                .list();
        if (list.isEmpty()) {
            return;
        }

        Date dateDate = new Date();
        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateDate);
        // notifyMsgMapper.updateReadStatus(
        //         list.stream().map(NotifyMsg::getId).collect(Collectors.toList()),
        //         date,
        //         username
        // );
        PushNotifyReadRecord pushNotifyReadRecord = new PushNotifyReadRecord();

        for (NotifyMsg notifyMsg : list) {
            pushNotifyReadRecord.setId(notifyMsg.getOutMsgId());
            pushNotifyReadRecord.setReadByName(username);
            pushNotifyReadRecord.setReadTime(date);
            OtherApiResponse otherApiResponse = null;
            try {
                otherApiResponse = bydApiService.pushNotifyReadRecord(pushNotifyReadRecord, "");
            } catch (Exception e) {
                log.error("pushNotifyReadRecord error, outMsgId:{}", notifyMsg.getOutMsgId(), e);
                this.lambdaUpdate()
                        .set(NotifyMsg::getErrorMsg, e.getMessage())
                        .eq(NotifyMsg::getId, notifyMsg.getId())
                        .update();
            }
            if (otherApiResponse != null && otherApiResponse.getErrno() == 0) {
                notifyMsgMapper.updateSingleReadStatus(
                        notifyMsg.getId(),
                        date,
                        username
                );
            }
        }

    }

}
