package com.bonc.rrs.notif.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息通知表
 */
@Data
@TableName(value = "notify_msg")
public class NotifyMsg implements Serializable {

    private static final long serialVersionUID = 4732213878863716870L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息通知唯一标识，安装商系统需存入，消息已读状态回传时传入此id
     */
    @TableField(value = "out_msg_id")
    private String outMsgId;

    /**
     * 通知类型: 1:订单关闭通知, 2:转单通知, 3:订单审核驳回通知, 4:知识库更新通知, 5:修改安装信息通知, 6:长期未安装即将关闭订单通知
     */
    @TableField(value = "notify_type")
    private Integer notifyType;

    /**
     * 安装订单编号
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 车架号，转单通知传入
     */
    @TableField(value = "vin")
    private String vin;

    /**
     * 知识库文件编号，知识库更新通知传入
     */
    @TableField(value = "cms_code")
    private String cmsCode;

    /**
     * 通知内容
     */
    @TableField(value = "notify_content")
    private String notifyContent;

    /**
     * 是否已读 1:已读 0:未读
     */
    @TableField(value = "read_status")
    private Integer readStatus;

    /**
     * 已读渠道 1:安装商系统接口回传已读 2:cpim系统安装商视角已读 传固定值：1
     */
    @TableField(value = "read_channel")
    private Integer readChannel;

    /**
     * 已读时间
     */
    @TableField(value = "read_time")
    private Date readTime;

    /**
     * 已读人
     */
    @TableField(value = "read_by_name")
    private String readByName;

    @TableField(value = "error_msg")
    private String errorMsg;

    /**
     * 逻辑删除控制（0 未删除1已删除）
     */
    @TableField(value = "is_delete")
    @TableLogic(value = "0", delval = "1")
    private Byte isDelete;

    /**
     * 创建者
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 最后更新日期
     */
    @TableField(value = "update_time")
    private Date updateTime;
}