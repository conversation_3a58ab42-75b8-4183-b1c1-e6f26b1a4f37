package com.bonc.rrs.spider.strategy;

import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.spider.dto.OrderApiDto;
import com.bonc.rrs.spider.util.DefaultUser;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.util.SmsUtil;
import com.bonc.rrs.worder.dao.WorderTemplateDao;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.service.*;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.validator.ValidatorUtils;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractOrderStrategy implements OrderCreationStrategy {

    @Resource
    protected WorderIntfMessageService worderIntfMessageService;
    @Resource
    protected WorderInformationService worderInformationService;
    @Resource
    protected CompanyInformationService companyInformationService;
    @Resource
    protected BizRegionService bizRegionService;
    @Resource
    protected RegionHandler regionHandler;
    @Resource
    protected WorderTemplateDao worderTemplateDao;

    // 模板方法
    @Override
//    @Lock4j(keys = "#dto.companyOrderNo", expire = 30000)
    public OrderContext createOrder(OrderApiDto dto) {
        OrderContext context = OrderContext.builder()
                .requestDto(dto)
                .orderRegion(new OrderRegion())
                .worderInfoEntity(new WorderInfoEntity())
                .build();
        try {
            DefaultUser.createDefaultLoginUser();
            // 1 参数值预处理
            preprocessParameters(context);
            // 2.1 通用基础校验
            validateCommonFields(context);
            WorderInformationEntity entity = worderInformationService.getByCompanyWorderNo(dto.getCompanyOrderNo());
            if (entity != null) {
                context.setWorderNo(entity.getWorderNo());
                return context;
            }
            // 2.2 策略专属校验（由子类实现）
            validateStrategySpecificFields(dto.getExtFields());
            // 3 填充上下文参数
            handleRegion(context);
            handleTemplate(context);
            handleContext(context);
            // 前置处理器
            preProcess(context);
            saveOrder(context);
            //更新客服组
            updateCreateBy(context);
            if (dto.getAutoDispatch() == 0) {
                autoDispatch(context);
            }
            // 后置处理器
            postProcess(context);
            return context;
        } catch (Exception e) {
            // 统一错误处理
            handleContextError(context, e);
            return context;
        } finally {
            // 资源清理或日志记录
            DefaultUser.clearDefaultLoginUser();
            log.info("Order creation process completed for companyOrderNo: {}", dto.getCompanyOrderNo());
        }
    }

    //更新客服组
    protected void updateCreateBy(OrderContext context) {

    };

    // 参数值处理方法，供子类调用
    protected void preprocessParameters(OrderContext context) {
        // 默认实现为空，子类可以重写此方法
    }

    // 公共前置处理
    protected void preProcess(OrderContext context) {

    }

    // 公共后置处理
    private void postProcess(OrderContext context) {
    }

    private void handleTemplate(OrderContext context) {
        WorderTemplateDto templateDto = worderTemplateDao.findTemplateInfoById(context.getRequestDto().getTemplateId());
        if (templateDto == null) {
            throw new RRException("工单模板不存在");
        }
        context.setWorderTemplateDto(templateDto);
    }

    // 新增上下文处理方法（protected允许子类扩展）
    protected void handleContext(OrderContext context) {
        OrderApiDto requestDto = context.getRequestDto();

        WorderInfoEntity worderInfoEntity = context.getWorderInfoEntity();
        //姓名
        worderInfoEntity.setUserName(requestDto.getCustName());
        //手机号
        worderInfoEntity.setUserPhone(requestDto.getCustPhone());
        //公司id
        worderInfoEntity.setCompanyId(requestDto.getCompanyId());
        // 车企订单号
        worderInfoEntity.setCompanyOrderNumber(requestDto.getCompanyOrderNo());
        // 车企订单id
        worderInfoEntity.setCompanyOrderNumberId(requestDto.getCompanyOrderId());
        // 模板id
        worderInfoEntity.setTemplateId(requestDto.getTemplateId());
        // 工单类型
        worderInfoEntity.setWorderTypeId(Integer.valueOf(requestDto.getWorderType()));
        worderInfoEntity.setCarVin(requestDto.getVinNo());

        worderInfoEntity.setAddress(context.getOrderRegion().getSysAddress());
        worderInfoEntity.setCarBrand(requestDto.getCarBrand());
        worderInfoEntity.setCompanyId(requestDto.getCompanyId());
        worderInfoEntity.setPostcode(requestDto.getPostCode());

        worderInfoEntity.setCandidate(ConstantPool.NEWS_OPERATOR_NAME);
        worderInfoEntity.setCreator(ConstantPool.NEWS_OPERATOR);

        List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1, "工单类型", worderInfoEntity.getWorderTypeId()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(5, "车企订单号", worderInfoEntity.getCompanyOrderNumber()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(101, "车企名称", worderInfoEntity.getCompanyId()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(153, "VIN 车架号", worderInfoEntity.getCarVin()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(154, "车企派单日期", requestDto.getDispatchDate()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(306, "工单来源", ""));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(902, "客户姓名", worderInfoEntity.getUserName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(903, "安装地址", context.getOrderRegion().getSysAddress()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(904, "客户邮箱", worderInfoEntity.getPostcode()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(905, "客户手机", worderInfoEntity.getUserPhone()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1391, "工单级别", requestDto.getWorderLevel()));
        worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);
    }

    protected void handleRegion(OrderContext context) {
        OrderRegion region = context.getOrderRegion();
        OrderApiDto requestDto = context.getRequestDto();
        region.setProvinceName(requestDto.getProvinceName());
        region.setCityName(requestDto.getCityName());
        region.setAreaName(requestDto.getAreaName());
        region.setDetailAddress(requestDto.getDetailAddress());
        regionHandler.handleRegionByAmap(region);
        regionHandler.handleRegin(region);

        String address = region.getProvinceCode() + "_" + region.getCityCode() + "_" + region.getAreaCode() + "_" + region.getDetailAddress();
        region.setSysAddress(address);
    }

    // 抽象方法用于子类扩展（注意改为protected）
    protected void validateStrategySpecificFields(Map<String, Object> subDto){

    }

    // 基础字段校验（protected允许子类复用）
    protected void validateCommonFields(OrderContext context) {
        ValidatorUtils.validateEntity(context.getRequestDto());
    }

    // 错误处理与上下文关联
    private void handleContextError(OrderContext context, Throwable e) {
        log.error("Order failed [车企订单号:{}]", context.getRequestDto().getCompanyOrderNo(), e);
        context.setError(e);
    }

    protected void saveOrder(OrderContext context) {
        try {

            R r = worderInformationService.saveWorderInformationByServiceProvider(context.getWorderInfoEntity());

            if (!r.isOk()) {
                throw new RRException("创建工单失败");
            }
            context.setWorderNo((String) r.get("worderNo"));
        } catch (Exception e) {
            log.error("创建工单失败", e);
            SmsUtil.sendSms("15910305046", "下单推送失败,车企订单号:" + context.getRequestDto().getCompanyOrderNo(), "【到每家科技服务】");
            throw e;
        }
    }

    protected void autoDispatch (OrderContext context) {
        String worderNo = context.getWorderNo();
        try {
            Results results = worderInformationService.goAutoSendWorder(worderNo, ConstantPool.NEWS_OPERATOR_NAME, null);
            if (results.getCode() != 0) {
                throw new RRException(results.getCode() + results.getMsg());
            }
            // 修改工单状态 0
            worderInformationService.updateWorderStatus(worderNo);
            context.setWorderNo(worderNo);
        } catch (Exception e) {
            log.error("{}自动派单失败", worderNo, e);
            SmsUtil.sendSms("15910305046", "订单派单失败,原因:" + e.getMessage() + ",车企订单号:" + context.getRequestDto().getCompanyOrderNo(), "【到每家科技服务】");
        }
    }

}
