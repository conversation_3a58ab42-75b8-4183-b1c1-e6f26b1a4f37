package com.bonc.rrs.spider.strategy;

import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.youngking.lenmoncore.common.exception.RRException;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
public class BMWOrderStrategy extends AbstractOrderStrategy {

    private static final Set<Integer> SUPPORTED_TEMPLATE_IDS = new HashSet<>(Arrays.asList(797, 798));

    @Override
    public boolean supports(Integer templateId) {
        return SUPPORTED_TEMPLATE_IDS.contains(templateId);
    }

    @Override
    protected void updateCreateBy(OrderContext context) {
        String worderNo = context.getWorderNo();
        if (worderNo == null || worderNo.isEmpty()) {
            throw new RRException("工单号不能为空");
        }
        //更新客服组为5618-彭玉翠
        boolean updated = worderInformationService.lambdaUpdate()
                .set(WorderInformationEntity::getCreateBy, 5618L)
                .eq(WorderInformationEntity::getWorderNo, worderNo)
                .update();

        if (!updated) {
            throw new RRException("更新客服组失败");
        }

    }

}
