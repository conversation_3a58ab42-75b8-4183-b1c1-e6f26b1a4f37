package com.bonc.rrs.spider.strategy;

import cn.hutool.core.bean.BeanUtil;
import com.bonc.rrs.spider.dto.HwOrderDto;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class HwOrderStrategy extends AbstractOrderStrategy {

    private static final Set<Integer> SUPPORTED_TEMPLATE_IDS = new HashSet<>(Arrays.asList(705,706,752));

    @Override
    public boolean supports(Integer templateId) {
        return SUPPORTED_TEMPLATE_IDS.contains(templateId);
    }

    @Override
    protected void preProcess(OrderContext context) {

        context.getWorderInfoEntity().setCreateBy(5287L);

        HwOrderDto apiDto = BeanUtil.mapToBean(context.getRequestDto().getExtFields(), HwOrderDto.class, true);
        List<WorderExtFieldEntity> extFieldList = context.getWorderInfoEntity().getWorderExtFieldList();
//        查找exfieldList中fieldId为306的记录，如果存在，则删除
        extFieldList.removeIf(extField -> extField.getFieldId().equals(306));
        extFieldList.add(WorderExtFieldEntity.create(306, "工单来源", apiDto.getWorderSource()));
        extFieldList.add(WorderExtFieldEntity.create(940, "车企系统订单编号", context.getRequestDto().getCompanyOrderId()));
    }

}
