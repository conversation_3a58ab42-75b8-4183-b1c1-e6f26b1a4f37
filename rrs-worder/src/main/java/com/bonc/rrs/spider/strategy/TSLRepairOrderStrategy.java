package com.bonc.rrs.spider.strategy;

import cn.hutool.core.bean.BeanUtil;
import com.bonc.rrs.spider.dto.TslOrderDto;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.youngking.lenmoncore.common.utils.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class TSLRepairOrderStrategy extends AbstractOrderStrategy {

    private static final Set<Integer> SUPPORTED_TEMPLATE_IDS = new HashSet<>(Arrays.asList(190,456));

    @Resource
    private WorderInformationService worderInformationService;

    @Override
    public boolean supports(Integer templateId) {
        return SUPPORTED_TEMPLATE_IDS.contains(templateId);
    }

    @Override
    protected void preprocessParameters(OrderContext context) {
        super.preprocessParameters(context);
        String companyOrderNo = getCompanyOrderNo(context);
        context.getRequestDto().setCompanyOrderNo(companyOrderNo);
        String detailAddress = context.getRequestDto().getDetailAddress();
        //如果详细地址为空，则使用省市区镇等详细地址
        if (detailAddress == null || detailAddress.isEmpty()) {
            context.getRequestDto().setDetailAddress(context.getRequestDto().getProvinceName() + context.getRequestDto().getCityName() + context.getRequestDto().getAreaName());
        }
    }

    @Override
    protected void preProcess(OrderContext context) {

        context.getWorderInfoEntity().setCreateBy(2106L);

        TslOrderDto apiDto = BeanUtil.mapToBean(context.getRequestDto().getExtFields(), TslOrderDto.class, true);
        List<WorderExtFieldEntity> worderExtFieldEntityList = context.getWorderInfoEntity().getWorderExtFieldList();
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(147, "安装单号", apiDto.getRnNo()));
    }

    private String getCompanyOrderNo(OrderContext context) {
        String companyOrderNo = context.getRequestDto().getCompanyOrderNo();
        companyOrderNo = companyOrderNo + "维修";
        List<WorderInformationEntity> list = worderInformationService.lambdaQuery()
                .select(WorderInformationEntity::getWorderNo, WorderInformationEntity::getCompanyOrderNumber, WorderInformationEntity::getCreateTime)
                .likeRight(WorderInformationEntity::getCompanyOrderNumber, companyOrderNo)
                .list();
        //如果list不为空，则找到list中最新的一条记录，判断时间跟当前时间在24小时之内，则返回该记录的工单编号，否则返回当前工单编号
        if (list != null && !list.isEmpty()) {
            list.sort(Comparator.comparing(WorderInformationEntity::getCreateTime).reversed());
            WorderInformationEntity worderInformationEntity = list.get(0);
            Date createTime = worderInformationEntity.getCreateTime();
            Date now = new Date();
            long diff = now.getTime() - createTime.getTime();
            long diffHours = diff / (60 * 60 * 1000);
            if (diffHours < 24) {
                companyOrderNo = worderInformationEntity.getCompanyOrderNumber();
            }else {
                //加上当前日期
                companyOrderNo = companyOrderNo + DateUtils.format(new Date(), "MMdd");
            }
        }
        return companyOrderNo;
    }

}
