package com.bonc.rrs.spider.strategy;

import cn.hutool.core.bean.BeanUtil;
import com.bonc.rrs.spider.dto.GqPileOrderDto;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.youngking.lenmoncore.common.constant.WorderTypeEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 广汽移桩
 *
 * <AUTHOR>
 */
@Component
public class GqPileOrderStrategy extends AbstractOrderStrategy {

    private static final Set<Integer> SUPPORTED_TEMPLATE_IDS = new HashSet<>(Collections.singletonList(240));

    private static final Long CUSTOM_SERVICE_GROUP_ID = 4469L;


    @Override
    public boolean supports(Integer templateId) {
        return SUPPORTED_TEMPLATE_IDS.contains(templateId);
    }

    @Override
    protected void preProcess(OrderContext context) {

        GqPileOrderDto apiDto = BeanUtil.mapToBean(context.getRequestDto().getExtFields(), GqPileOrderDto.class, true);
        List<WorderExtFieldEntity> extFieldList = context.getWorderInfoEntity().getWorderExtFieldList();
//        查找exfieldList中fieldId为306的记录，如果存在，则删除
        extFieldList.removeIf(extField -> extField.getFieldId().equals(306));
        extFieldList.add(WorderExtFieldEntity.create(1206, "是否官方移桩服务", apiDto.getIsOfficalMove()));
        extFieldList.add(WorderExtFieldEntity.create(911, "联系信息备注", apiDto.getContactRemark()));
        extFieldList.add(WorderExtFieldEntity.create(1732, "移桩详细地址", apiDto.getOldAddress()));
    }

    @Override
    protected void updateCreateBy(OrderContext context) {
        String worderNo = context.getWorderNo();
        if (worderNo == null || worderNo.isEmpty()) {
            throw new RRException("工单号不能为空");
        }
        //维修工单更新客服组为4469-李文雨
        boolean updated = worderInformationService.lambdaUpdate()
                .set(WorderInformationEntity::getCreateBy, CUSTOM_SERVICE_GROUP_ID)
                .eq(WorderInformationEntity::getWorderNo, worderNo)
                .update();

        if (!updated) {
            throw new RRException("更新客服组失败");
        }

    }
}
