package com.bonc.rrs.message.service.impl;

import com.bonc.rrs.message.dao.MsgDao;
import com.bonc.rrs.message.entity.MessageQueryEntity;
import com.bonc.rrs.message.entity.WorderMessageEntity;
import com.bonc.rrs.warning.entity.MsgEntity;
import com.bonc.rrs.message.service.MsgService;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by z<PERSON>yi<PERSON> on 2020-03-23 10:39
 */

@Service
public class MsgServiceImpl implements MsgService {

    @Autowired(required = false)
    MsgDao msgDao;

    @Override
    public Integer updateWorderMessageRead(List<Integer> ids) {
        return msgDao.updateWorderMessageRead(ids);
    }

    @Override
    public List<WorderMessageEntity> listWorderMessage(MessageQueryEntity messageQueryEntity) {

        return msgDao.listWorderMessage(messageQueryEntity);
    }

    @Override
    public Integer getMessageCount(MessageQueryEntity messageQueryEntity) {
        SysUserEntity sysUserEntity = (SysUserEntity)SecurityUtils.getSubject().getPrincipal();
        messageQueryEntity.setUserId(String.valueOf(sysUserEntity.getUserId()));
        return msgDao.getMessageCount(messageQueryEntity);
    }

    @Override
    public Integer sendMessage(WorderMessageEntity worderMessageEntity) {
        return msgDao.saveWorderMessage(worderMessageEntity);
    }

    @Override
    public void insertWarningDelayMessage(WorderMessageEntity worderMessage) {
        msgDao.insertWarningDelayMessage(worderMessage);
    }

    @Override
    public List<WorderMessageEntity> selectwArningDelayMessage(String processState) {
        return msgDao.selectwArningDelayMessage(processState);
    }

    @Override
    public void updateWarningDelayMessageProcessState(String processState, Integer warningDelayMessageId) {
        msgDao.updateWarningDelayMessageProcessState(processState,warningDelayMessageId);
    }

}
