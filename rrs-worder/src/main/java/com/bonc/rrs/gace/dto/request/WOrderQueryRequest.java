package com.bonc.rrs.gace.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;


/**
 * <AUTHOR>
 */

@Builder
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WOrderQueryRequest extends RequestDto{

    // 工作单类型.基本类型 (RM:故障/缺陷响应, PM:维护保养, INS:点巡检, AM:自主维护, OH:大修/改造, IT:安装调试, OT:其它事项, 01:换货类工单)
    private String wo_type_id_digi_basic_type = "IT";

    // 负责组织.编码;
    // 查询工作单时，【负责组织】传“HERRS”，【执行组织】可传可不传；
    // 查询维修工单时，【负责组织】传“KFGLZX”，【执行组织】传“HERRS”
    private String workcenter_id_number = "HERRS";

    // 执行组织.编码;
    // 查询工作单时，【负责组织】传“HERRS”，【执行组织】可传可不传；
    // 查询维修工单时，【负责组织】传“KFGLZX”，【执行组织】传“HERRS”
    private String woop_workcenter_number;

    // 工单编号（可选）
    private String billno;

    // 工单创建开始时间（可选）"2024-09-14 00:00:00"
    private String starttime;

    // 工单创建结束时间（可选）
    private String endtime;

    // 分页数量（必填）
    @JsonProperty("pageSize")
    private Integer pageSize = 10;

    // 查询页码（可选）
    @JsonProperty("pageNo")
    private Integer pageNo;

    @JsonIgnore
    public String getUrlParams() {
        return "?workcenter_id_number=" + workcenter_id_number +
                "&wo_type_id_digi_basic_type=" + wo_type_id_digi_basic_type +
                (StringUtils.isEmpty(woop_workcenter_number)? "":"&woop_workcenter_number=" + woop_workcenter_number) +
                (StringUtils.isEmpty(billno)? "":"&billno=" + billno) +
                (StringUtils.isEmpty(starttime)? "":"&starttime=" + starttime) +
                (StringUtils.isEmpty(endtime)? "":"&endtime=" + endtime) +
                (StringUtils.isEmpty(pageSize)? "":"&pageSize=" + pageSize) +
                (StringUtils.isEmpty(pageNo)? "":"&pageNo=" + pageNo);
    }

}