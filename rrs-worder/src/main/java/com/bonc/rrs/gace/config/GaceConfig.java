package com.bonc.rrs.gace.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年10月14日 15:51
 * @Description 广汽新能源配置
 */
@Configuration
@ConfigurationProperties(prefix = GaceConfig.PREFIX)
@Data
public class GaceConfig {
    public static final String PREFIX="news.gace";
    private String baseUrl;
    private String appId;
    private String appSecret;
    private String user;
    private String password;
    private String idNumber;
    private Integer pageSize;
    private Integer companyId;
    private Integer queryHours;
    private Integer auditDays;
    private Integer cancelDays;
    private String tel;
    private GaceField field;
    private List<String> templateIds;
    private String woopWcpIdNumber;
    private String repairTemplateId;
    private AmapParam amapParam;

    @Data
    public static class GaceField {
        private int woopId;
        private int assetId;
        private int woopNo;
        private int actFinishTime;
        private int actCheckTime;
        private int faultTreeType;
    }

    @Data
    public static class AmapParam {
        private String url;
        private String key;
    }

}
