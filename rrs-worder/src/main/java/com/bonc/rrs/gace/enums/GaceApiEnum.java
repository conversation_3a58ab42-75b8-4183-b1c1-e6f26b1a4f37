package com.bonc.rrs.gace.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.HttpMethod;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum GaceApiEnum {

    // 创建枚举内部类方法
    GET_APPTOKEN(HttpMethod.POST,"/api/getAppToken.do", "获取AppToken"),
    GET_ACCESSTOKEN(HttpMethod.POST,"/api/login.do", "获取AccessToken"),
    GET_AUDIT_STATUS(HttpMethod.GET,"/kapi/app/digi_maint_pf/getWoStatus", "工单审核结果查询"),
    GET_WORK_ORDERS(HttpMethod.GET,"/kapi/v2/ab38/digi_maint_pf/digi_workorders/getWorkOrders", "工单查询接口"),
    UPDATE_WORK_ORDER(HttpMethod.POST,"/kapi/v2/ab38/digi_maint_pf/digi_workorders/updateWorkOrder", "工单修改接口");

    private final HttpMethod method;
    private final String uri;
    private final String desc;
}