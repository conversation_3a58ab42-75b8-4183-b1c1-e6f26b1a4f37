package com.bonc.rrs.gace.service;

import com.bonc.rrs.gace.config.GaceConfig;
import com.bonc.rrs.gace.dto.request.WOrderUpdateRequest;
import com.bonc.rrs.gace.dto.response.WOrderQueryResponse;
import com.bonc.rrs.gace.enums.GaceOrderTypeEnum;
import com.bonc.rrs.gace.enums.WorkOrderStatusEnum;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.entity.ExtFieldRelation;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationAttributeEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.ExtFieldRelationService;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.youngking.lenmoncore.common.constant.WorderTypeEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 广汽工单推送服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GacePushService {
    @Resource
    private WorderInformationService worderInformationService;
    @Resource
    protected GaceService gaceService;
    @Resource
    private WorderExtFieldService worderExtFieldService;
    @Resource
    private WorderInformationAttributeDao worderInformationAttributeDao;
    @Resource
    private GaceConfig gaceConfig;
    @Resource
    private ExtFieldRelationService extFieldRelationService;
    @Resource
    private SysFilesService sysFilesService;

    /**
     * 1）预约客户上门时间，需要调用该接口，修改工单状态“待处理”；
     * 2）上门勘察，在业务上填写完勘察信息后，修改工单状态“待处理”
     * 3）上门安装，在业务上填写完安装信息后，修改工单状态“处理中”
     * 4) 取消安装
     * 5）工单完成：在下游系统工单内部审核通过后，修改工单状态为“工作完成”
     * 回传工单预计开始&结束时间、实际开始时间&实际结束时间&工单检查项&附件&工单状态、桩编码
     */
    public void pushOrder(String worderNo, WorkOrderStatusEnum status, String logDesc) {
        // 非广汽工单，直接返回
        if (nonGaceOrder(worderNo)) {
            return;
        }

        try {
            //1.获取工单信息
            WorderInformationEntity worderInformation = worderInformationService.lambdaQuery()
                    .select(WorderInformationEntity::getCompanyOrderNumber, WorderInformationEntity::getWorderTypeId, WorderInformationEntity::getAddressDup)
                    .eq(WorderInformationEntity::getWorderNo, worderNo)
                    .one();
            String companyOrderNumber = worderInformation.getCompanyOrderNumber();
            GaceOrderTypeEnum worderType = Objects.equals(WorderTypeEnum.SERVE_INSTALL.getId(), worderInformation.getWorderTypeId()) ? GaceOrderTypeEnum.IT : GaceOrderTypeEnum.RM;

            // 维修工单，直接返回
            if (worderType == GaceOrderTypeEnum.RM) {
                return;
            }

            String addressDup = worderInformation.getAddressDup();

            //2.查询车企工单信息
            WOrderQueryResponse.WOrder wOrder = gaceService.queryOrder(worderType, companyOrderNumber);
            if (wOrder == null) {
                throw new RRException("车企订单不存在或已改派,请检查,车企订单号:" + companyOrderNumber);
            }

            //3.组装工单更新请求参数
            WOrderUpdateRequest orderUpdateRequest = buildOrderUpdateRequest(companyOrderNumber, status, wOrder, logDesc);

            //4.完成工单时: 添加实际完成时间/工单检查项/附件
            if (status == WorkOrderStatusEnum.COMPLETED) {
                completeOrder(worderNo, wOrder, worderType,addressDup, orderUpdateRequest);
            }

            //5.提交工单信息到广汽
            gaceService.updateOrder(orderUpdateRequest);

            //6.修改工单属性状态
            updateAttributeEntity(worderNo, status);
        } catch (Exception e) {
            log.error("请求广汽失败：{}", worderNo, e);
            throw new RRException("请求广汽失败" + e.getMessage());
        }
    }

    private WOrderUpdateRequest buildOrderUpdateRequest(String companyOrderNumber, WorkOrderStatusEnum status, WOrderQueryResponse.WOrder wOrder, String logDesc) {
        WOrderUpdateRequest orderUpdateRequest = new WOrderUpdateRequest();
        WOrderUpdateRequest.WorkOrderOperation workOrderOperation = new WOrderUpdateRequest.WorkOrderOperation();
        workOrderOperation.setId(wOrder.getWorkorder_operations().get(0).getId());
        workOrderOperation.setWoopNumber(wOrder.getWorkorder_operations().get(0).getWoop_number());
        workOrderOperation.setWoopStatus(status);
        workOrderOperation.setWoopWcpIdNumber(gaceConfig.getWoopWcpIdNumber());
        workOrderOperation.setWoopWorkcenterIdNumber("HERRS");

        orderUpdateRequest.setBillno(companyOrderNumber);
        orderUpdateRequest.setAssetIdNumber(wOrder.getAsset_id_number());
        orderUpdateRequest.setStatus(status);
        orderUpdateRequest.setDescription(gaceConfig.getTel());
        orderUpdateRequest.setWorkorderOperations(Collections.singletonList(workOrderOperation));
        orderUpdateRequest.setLogDesc(logDesc);

        return orderUpdateRequest;
    }

    private void completeOrder(String worderNo, WOrderQueryResponse.WOrder wOrder, GaceOrderTypeEnum worderType, String address, WOrderUpdateRequest orderUpdateRequest) {

        String actualFinishTimeStr = null;
        try {
            //获取工单扩展字段-实际完成时间
            List<Integer> fieldIdList = Arrays.asList(gaceConfig.getField().getActFinishTime(),gaceConfig.getField().getActCheckTime());
            Map<Integer, String> fieldMap = worderExtFieldService.getSpecificFields(worderNo, fieldIdList)
                    .stream()
                    .collect(Collectors.toMap(WorderExtFieldEntity::getFieldId, WorderExtFieldEntity::getFieldValue, (e1, e2) -> e2));
            actualFinishTimeStr = fieldMap.get(gaceConfig.getField().getActFinishTime());
            if (worderType == GaceOrderTypeEnum.RM) {
                actualFinishTimeStr = fieldMap.get(gaceConfig.getField().getActCheckTime());
                LocalDateTime parse = LocalDate.parse(StringUtils.substring(actualFinishTimeStr,0,10)).atStartOfDay();
                actualFinishTimeStr = parse.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            LocalDateTime actFinishTime = DateUtils.toLocalDateTime(actualFinishTimeStr);
            orderUpdateRequest.setSchedualedStart(actFinishTime.plusHours(-2L));
            orderUpdateRequest.setSchedualedFinish(actFinishTime);
            orderUpdateRequest.setActualStart(actFinishTime.plusHours(-2L));
            orderUpdateRequest.setActualFinish(actFinishTime);
        } catch (Exception e) {
            log.error("{},解析实际完成时间发生异常", worderNo);
            throw new RRException("解析实际完成时间发生异常,请检查");
        }

        packageDigiWoCheckLists(worderNo, wOrder, address, actualFinishTimeStr, orderUpdateRequest);
        packageAttachmentList(worderNo, orderUpdateRequest);
    }

    private void updateAttributeEntity(String worderNo, WorkOrderStatusEnum status) {
        WorderInformationAttributeEntity attributeEntity = worderInformationAttributeDao.selectAttributeByWorderNo(worderNo, "company_status", "gace");
        if (attributeEntity == null) {
            return;
        }
        attributeEntity.setAttributeValue(status.getCode());
        attributeEntity.setUpdateTime(null);
        worderInformationAttributeDao.updateById(attributeEntity);
    }

    /**
     * 工单检查项
     *
     * @param worderNo
     * @param wOrder
     * @param address
     * @param installSignTime
     * @param orderUpdateRequest
     */
    private void packageDigiWoCheckLists(String worderNo, WOrderQueryResponse.WOrder wOrder, String address, String installSignTime, WOrderUpdateRequest orderUpdateRequest) {

        List<WOrderQueryResponse.WOrder.WorkOrderCheck> list = wOrder.getDigi_wo_checklists();

        if (!list.isEmpty()) {
            List<String> ckNames = list.stream().map(WOrderQueryResponse.WOrder.WorkOrderCheck::getCkName).collect(Collectors.toList());
            Map<String, Integer> nameFields = extFieldRelationService.lambdaQuery()
                    .eq(ExtFieldRelation::getFieldType, 0)
                    .in(ExtFieldRelation::getOutFieldName, ckNames)
                    .list()
                    .stream()
                    .filter(extFieldRelation -> extFieldRelation.getOutFieldName() != null && extFieldRelation.getFieldId() != null)
                    .collect(Collectors.toMap(ExtFieldRelation::getOutFieldName, ExtFieldRelation::getFieldId, (e1, e2) -> e1));

            Map<Integer, String> fieldValues = worderExtFieldService.getSpecificFields(worderNo, nameFields.values())
                    .stream()
                    .filter(worderExtField -> worderExtField.getFieldId() != null && worderExtField.getFieldValue() != null)
                    .collect(Collectors.toMap(WorderExtFieldEntity::getFieldId, WorderExtFieldEntity::getFieldValue, (e1, e2) -> e2));
            List<WOrderUpdateRequest.DigiWoCheckItem> digiWoChecklistList = nameFields.entrySet().stream()
                    .map(nameField -> {
                        String ckName = nameField.getKey();
                        Integer fieldId = nameField.getValue();
                        WOrderUpdateRequest.DigiWoCheckItem digiWoCheckItem = new WOrderUpdateRequest.DigiWoCheckItem();
                        Optional<WOrderQueryResponse.WOrder.WorkOrderCheck> check = list.stream().filter(e -> e.getCkName().equals(ckName)).findFirst();
                        if (check.isPresent()) {
                            String ckValue = fieldValues.get(fieldId);
                            digiWoCheckItem.setId(check.get().getId());
                            digiWoCheckItem.setCkName(ckName);
                            digiWoCheckItem.setCkActualValue(ckValue);
                            switch (ckName) {
                                case "安装位置":
                                    digiWoCheckItem.setCkActualValue(address);
                                    break;
                                case "具备安装条件时间（需上传证明附件）":
                                    digiWoCheckItem.setCkActualValue(installSignTime);
                                    break;
                                case "充电桩编号":
                                    digiWoCheckItem.setCkActualValue(ckValue);
                                    orderUpdateRequest.setAssetIdNumber(ckValue);
                                    orderUpdateRequest.getWorkorderOperations().get(0).setWoopAssetIdNumber(ckValue);
                                    break;
                                case "解决方案":
                                    //故障树类型
                                    List<WorderExtFieldEntity> specificFields = worderExtFieldService.getSpecificFields(worderNo, Collections.singletonList(gaceConfig.getField().getFaultTreeType()));
                                    if (specificFields.isEmpty()) {
                                        digiWoCheckItem.setCkActualValue(ckValue);
                                    } else {
                                        digiWoCheckItem.setCkActualValue(specificFields.get(0).getFieldValue()+"-"+ckValue);
                                    }
                                    break;
                                default:
                                    digiWoCheckItem.setCkActualValue(ckValue);
                                    break;
                            }
                        }
                        return digiWoCheckItem;
                    })
                    .filter(digiWoCheckItem -> StringUtils.isNotBlank(digiWoCheckItem.getCkActualValue()))
                    .collect(Collectors.toList());

            orderUpdateRequest.setDigiWoChecklists(digiWoChecklistList);
        }
    }

    /**
     * 附件
     *
     * @param worderNo           工单号
     * @param orderUpdateRequest 请求参数
     */
    private void packageAttachmentList(String worderNo, WOrderUpdateRequest orderUpdateRequest) {
        // OutName - FieldID
        Map<String, Integer> attIdFieldMap = extFieldRelationService.lambdaQuery()
                .eq(ExtFieldRelation::getFieldType, 1)
                .list()
                .stream()
                .collect(Collectors.toMap(ExtFieldRelation::getOutFieldName, ExtFieldRelation::getFieldId, (e1, e2) -> e1));

        // FieldId - FieldValue
        Map<Integer, String> extFieldMap = worderExtFieldService.getSpecificFields(worderNo, attIdFieldMap.values())
                .stream()
                .collect(Collectors.toMap(WorderExtFieldEntity::getFieldId, WorderExtFieldEntity::getFieldValue, (e1, e2) -> e2));

        List<SysFileEntity> fileList = sysFilesService.getSysFileByIds(extFieldMap.values().stream().filter(StringUtils::isNotBlank).map(String::valueOf).collect(Collectors.joining(",")));
        Map<Integer, String> fileMap = fileList.stream().collect(Collectors.toMap(SysFileEntity::getFileId, SysFileEntity::getNewName, (e1, e2) -> e2));
        List<WOrderUpdateRequest.Attachment> attachments = attIdFieldMap.keySet().stream()
                .map(name -> {
                    WOrderUpdateRequest.Attachment attachment = new WOrderUpdateRequest.Attachment();
                    Integer fieldId = attIdFieldMap.get(name);
                    String inVal = extFieldMap.get(fieldId);
                    Map<String, String> filePath = getFilePath(inVal, fileMap);
                    attachment.setType(name);
                    attachment.setName(filePath.get("name"));
                    attachment.setOriginUrl(filePath.get("url"));
                    return attachment;
                })
                .filter(attachment -> StringUtils.isNotBlank(attachment.getOriginUrl()))
                .collect(Collectors.toList());
        orderUpdateRequest.setAttachments(attachments);

    }

    /**
     * 根据属性ID获取文件oss路径
     *
     * @param fieldValue 文件ID
     * @param fileMap    文件ID与文件路径的映射
     * @return 文件oss路径
     */
    Map<String, String> getFilePath(String fieldValue, Map<Integer, String> fileMap) {
        Map<String, String> paths = new HashMap<>();
        if (StringUtils.isBlank(fieldValue)) {
            return Collections.emptyMap();
        }
        // 字符串逗号分割
        String[] split = fieldValue.split(",");
        if (fileMap.containsKey(Integer.valueOf(split[0]))) {
            paths.put("name", fileMap.get(Integer.valueOf(split[0])));
            paths.put("url", FileUtils.copyImage(fileMap.get(Integer.valueOf(split[0]))));
        }
        return paths;
    }

    private boolean nonGaceOrder(String worderNo) {
        WorderInformationAttributeEntity pushOrder =
                worderInformationAttributeDao.selectAttributeByWorderNoAttVal(worderNo, "worder_source", "pushOrder", "gace");
        return Objects.isNull(pushOrder);
    }

    public void checkGaceFlag(String worderNo) {
        WorderInformationEntity worderInformation = worderInformationService.getByWorderNo(worderNo);
        if (worderInformation == null) {
            throw new RRException("工单不存在");
        }

        List<String> templateIds = new ArrayList<>();
        templateIds.addAll(gaceConfig.getTemplateIds());
        templateIds.add(gaceConfig.getRepairTemplateId());

        if (!templateIds.contains(worderInformation.getTemplateId().toString())) {
            throw new RRException("非广汽工单，不允许提交");
        }
        if (nonGaceOrder(worderNo)) {
            WorderInformationAttributeEntity entity = new WorderInformationAttributeEntity();
            entity.setWorderId(worderInformation.getWorderId());
            entity.setAttributeCode("worder_source");
            entity.setAttributeName("工单来源");
            entity.setAttributeValue("gace");
            entity.setAttribute("pushOrder");
            worderInformationAttributeDao.insert(entity);
        }
    }
}
