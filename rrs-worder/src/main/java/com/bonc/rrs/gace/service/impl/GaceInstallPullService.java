package com.bonc.rrs.gace.service.impl;

import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.gace.dto.request.WOrderQueryRequest;
import com.bonc.rrs.gace.dto.response.WOrderQueryResponse;
import com.bonc.rrs.gace.enums.GaceBrandEnum;
import com.bonc.rrs.gace.enums.GaceOrderTypeEnum;
import com.bonc.rrs.gace.service.GacePullService;
import com.bonc.rrs.gace.util.GaceRegionInfo;
import com.bonc.rrs.gace.util.GeoCode;
import com.bonc.rrs.gace.util.GeoCodeResponse;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youngking.lenmoncore.common.constant.WorderTypeEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;

/**
 * 安装工单拉取服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GaceInstallPullService extends GacePullService {

    @Override
    public GaceOrderTypeEnum getWorderType() {
        return GaceOrderTypeEnum.IT;
    }

    @Override
    protected WOrderQueryRequest createQueryRequest(String startTime, String endTime, String billNo) {
        return WOrderQueryRequest.builder()
                // 查询工作单时，【负责组织】传“HERRS”，【执行组织】可传可不传；
                .workcenter_id_number("HERRS")
                .woop_workcenter_number("HERRS")
                // 工单类型
                .wo_type_id_digi_basic_type(getWorderType().getCode())
                // 设置分页大小
                .pageSize(gaceConfig.getPageSize()).pageNo(1)
                // 设置开始/结束时间
                .starttime(startTime).endtime(endTime)
                // 设置工单号
                .billno(billNo)
                .build();
    }


    @Override
    protected GaceRegionInfo getReginName(WOrderQueryResponse.WOrder wOrder) {

        String provinceName = wOrder.getDigi_attribute3();
        String cityName = wOrder.getDigi_attribute4();
        String areaName = wOrder.getDigi_attribute5();
        String fullAddress = wOrder.getDigi_attribute6();

        if (StringUtils.isAnyBlank(provinceName, cityName, areaName)  && StringUtils.isNotBlank(fullAddress)) {
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("key", gaceConfig.getAmapParam().getKey());
                params.put("address", fullAddress);

                String ampResult = HttpUtil.get(gaceConfig.getAmapParam().getUrl(), params);

                ObjectMapper objectMapper = new ObjectMapper();
                GeoCodeResponse response = objectMapper.readValue(ampResult, GeoCodeResponse.class);
                if (response.getStatus()==1 && response.getCount()>0) {
                    GeoCode geoCode = response.getGeocodes().get(0);
                    provinceName = StringUtils.isBlank(provinceName) ? geoCode.getProvince() : provinceName;
                    List<String> city = geoCode.getCity();
                    if ( !CollectionUtils.isEmpty(city)) {
                        cityName = StringUtils.isBlank(cityName) ? city.get(0) : cityName;
                    }

                    List<String> district = geoCode.getDistrict();
                    if ( !CollectionUtils.isEmpty(district)) {
                        areaName = StringUtils.isBlank(areaName) ? district.get(0) : areaName;
                    }

                }else {
                    log.error("高德地图接口返回异常,异常信息:"+ampResult);
                }
            } catch (Exception e) {
                log.error("高德地图接口异常,异常信息:"+e.getMessage());
            }
        }

        GaceRegionInfo gaceRegionInfo = new GaceRegionInfo();
        gaceRegionInfo.setProvinceName(provinceName);
        gaceRegionInfo.setCityName(cityName);
        gaceRegionInfo.setAreaName(areaName);
        //商城备注 埃安二代7kW充电桩（不含立柱）
        String description = wOrder.getDescription();
        if (StringUtils.contains(description, "（不含立柱）")) {
            fullAddress = fullAddress + "（不含立柱）";
        }else if (StringUtils.contains(description, "（含立柱）")) {
            fullAddress = fullAddress + "（含立柱）";
        }
        gaceRegionInfo.setDetailedAddress(fullAddress);
        return gaceRegionInfo;
    }

    /**
     * 根据订单信息确定品牌ID
     * 此方法通过订单中提供的品牌名称，查询品牌实体的ID返回如果品牌名称为空或对应的品牌实体不存在，则返回0
     *
     * @param wOrder 订单对象，包含品牌信息
     * @return 品牌实体的ID如果找不到对应品牌或品牌名称为空，则返回0
     */
    @Override
    protected Integer determineBrand(WOrderQueryResponse.WOrder wOrder) {
        // 从订单中获取品牌code
        String brandCode = wOrder.getDigi_professional_class_number();
        GaceBrandEnum brandEnum = GaceBrandEnum.fromCode(brandCode);
        if (brandEnum != null) {
            // 判断是否是广汽埃安/云影低配 云影高配    联系人拼接工单类型名称
            if (StringUtils.equalsAny(brandEnum.getCode(), "GQAA", "NY-LGDP", "NY-LGGP")) {
                // 工单类型名称
                String woTypeIdName = wOrder.getWo_type_id_name();
                String constants = wOrder.getContacts();
                wOrder.setContacts(constants + "(" + woTypeIdName + ")");
            }
            // 创建查询条件，根据品牌名称查询品牌实体
            QueryWrapper<BrandEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("brand_name", brandEnum.getBrandName());
            // 执行查询，获取品牌实体
            BrandEntity one = brandService.getOne(queryWrapper);
            // 如果找到对应的品牌实体，则返回其ID，否则返回0
            return one != null ? one.getId() : 0;
        } else {
            // 如果品牌名称为空，则直接返回0
            return 0;
        }
    }

    @Override
    protected Integer determineTemplate(WOrderQueryResponse.WOrder wOrder, Integer brandId, WorderTypeEnum worderTypeEnum, GaceRegionInfo regionInfo) {
        List<WorderTemplateDto> worderTemplateDtoList = worderTemplateService.findTemplateInfoByBrandIdAndWorderTypeIdAndRegion(
                brandId,
                worderTypeEnum.getId(),
                regionInfo.getProvinceCode().intValue(),
                regionInfo.getCityCode().intValue()
        );
        if (CollectionUtils.isEmpty(worderTemplateDtoList)) {
            log.error("未找到对应工单模板: {}", wOrder.getBillno());
            throw new RRException("Failed to save order " + wOrder.getBillno());
        }
        return worderTemplateDtoList.get(0).getTemplateId();
    }
}
