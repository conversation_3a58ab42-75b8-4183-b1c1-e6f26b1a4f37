package com.bonc.rrs.gace.client;

import com.baomidou.lock.annotation.Lock4j;
import com.bonc.rrs.gace.config.GaceConfig;
import com.bonc.rrs.gace.dto.request.AccessTokenRequest;
import com.bonc.rrs.gace.dto.request.AppTokenRequest;
import com.bonc.rrs.gace.dto.response.AccessTokenResponse;
import com.bonc.rrs.gace.dto.response.AppTokenResponse;
import com.bonc.rrs.gace.enums.GaceApiEnum;
import com.bonc.rrs.gace.util.GaceResponse;
import com.bonc.rrs.intf.service.IntfLogService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024年10月15日 18:04
 */
@Slf4j
@Component
public class GaceAuthApi {

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private GaceConfig gaceConfig;
    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private ObjectMapper objectMapper;

    public static final String TOKEN_REDIS_KEY = "gace:token";

    public String getToken() throws IOException {
        String token = redisUtils.get(TOKEN_REDIS_KEY);

        if (token != null) {
            // Redis 中存在 token，直接返回
            return token;
        } else {
            // 如果 Redis 中没有 token，则尝试获取新的 token
            return refreshToken();
        }
    }

    @Lock4j
    private String refreshToken() throws IOException {
        // 再次检查 Redis，以防其他线程已获取并更新了 token
        String token = redisUtils.get(TOKEN_REDIS_KEY);
        if (token != null) {
            return token;
        }

        String url = gaceConfig.getBaseUrl() + GaceApiEnum.GET_APPTOKEN.getUri();
        AppTokenRequest appTokenRequest = AppTokenRequest.builder().appId(gaceConfig.getAppId()).appSecret(gaceConfig.getAppSecret()).build();
        HttpEntity<AppTokenRequest> httpEntity = new HttpEntity<>(appTokenRequest);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        checkResponse(responseEntity);
        String bodyStr = responseEntity.getBody();
        GaceResponse<AppTokenResponse> appTokenResponse = objectMapper.readValue(bodyStr, new TypeReference<GaceResponse<AppTokenResponse>>() {
        });
        if (appTokenResponse == null || appTokenResponse.getData() == null) {
            log.error("请求广汽失败, url:{}, response:{}", url, responseEntity);
            throw new RRException("请求广汽失败");
        }

        String appToken = appTokenResponse.getData().getApp_token();

        url = gaceConfig.getBaseUrl() + GaceApiEnum.GET_ACCESSTOKEN.getUri();
        AccessTokenRequest accessTokenRequest = AccessTokenRequest.builder().apptoken(appToken).user(gaceConfig.getUser()).build();
        HttpEntity<AccessTokenRequest> httpEntity1 = new HttpEntity<>(accessTokenRequest);
        responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity1, String.class);
        checkResponse(responseEntity);
        bodyStr = responseEntity.getBody();
        GaceResponse<AccessTokenResponse> accessTokenResponse = objectMapper.readValue(bodyStr, new TypeReference<GaceResponse<AccessTokenResponse>>() {
        });
        if (accessTokenResponse == null || accessTokenResponse.getData() == null) {
            log.error("请求广汽接口失败, url:{}, response:{}", url, responseEntity);
            throw new RRException("请求广汽接口失败");
        }

        String newToken = accessTokenResponse.getData().getAccess_token();
        long expireTime = accessTokenResponse.getData().getExpire_time() - 60*1000;
        log.info("getToken success, expires in {}", expireTime);
        redisUtils.setWithExpireAt(TOKEN_REDIS_KEY, newToken, expireTime);
        return newToken;
    }

    private static <T> void checkResponse(ResponseEntity<T> responseEntity) {
        if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity.getBody() == null) {
            log.error("请求广汽接口失败:{}", responseEntity);
            throw new RRException("请求广汽接口失败" + responseEntity.getStatusCode() + " " + responseEntity.getStatusCode().getReasonPhrase());
        }
    }

}
