package com.bonc.rrs.gace.dto.request;

import com.bonc.rrs.gace.enums.WorkOrderStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PastOrPresent;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "工单实体类")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WOrderUpdateRequest extends RequestDto{

    @ApiModelProperty(value = "工单编号", example = "OyCmv", required = true)
    @NotBlank(message = "工单编号不能为空")
    private String billno;

    @ApiModelProperty(value = "计划开始时间", example = "2024-09-19T16:57:37", required = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    @PastOrPresent(message = "计划开始时间必须是过去或现在的时间")
    private LocalDateTime schedualedStart;

    @ApiModelProperty(value = "计划完成时间", example = "2024-09-19T16:57:37", required = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    // @FutureOrPresent(message = "计划完成时间必须是未来或现在的时间")
    private LocalDateTime schedualedFinish;

    @ApiModelProperty(value = "实际开始时间", example = "2024-09-19T16:57:37", required = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    @PastOrPresent(message = "实际开始时间必须是过去或现在的时间")
    private LocalDateTime actualStart;

    @ApiModelProperty(value = "实际完成时间", example = "2024-09-19T16:57:37", required = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
    @PastOrPresent(message = "实际完成时间必须是过去或现在的时间")
    private LocalDateTime actualFinish;

    @ApiModelProperty(value = "设备/资产编码", example = "DD06g", required = true)
    @NotBlank(message = "设备/资产编码不能为空")
    private String assetIdNumber;

    @ApiModelProperty(value = "工单状态", example = "WSCH", required = false)
    @NotNull(message = "工单状态不能为空")
    private WorkOrderStatusEnum status;

//    log_desc	String	否	日志备注	1	"确认安装完成，关闭工单"
    @ApiModelProperty(value = "日志备注")
    private String logDesc;


    @ApiModelProperty(value = "单据体列表", required = true)
    @NotEmpty(message = "单据体列表不能为空")
    @Valid
    private List<WorkOrderOperation> workorderOperations;

    private List<DigiWoCheckItem> digiWoChecklists;
    private List<Attachment> attachments;
    private List<DigiAttachmentEntry> digiAttachmentEntry;

    @ApiModelProperty(value = "备注--暂存联系方式400-9219898", required = true)
    private String description;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class WorkOrderOperation {

        @ApiModelProperty(value = "单据体ID", example = "2282389356565158912", required = false)
        private Long id;

        @ApiModelProperty(value = "任务编号", example = "aCTjq", required = true)
        @NotBlank(message = "任务编号不能为空")
        private String woopNumber;

        @ApiModelProperty(value = "设备.资产编码（充电桩编码）", example = "aCTjq", required = true)
        @NotBlank(message = "任务编号不能为空")
        private String woopAssetIdNumber;

        //执行组织.编码；如果入参中“woop_workcenter_id_number”字段值与工单目前的字段“woop_workcenter_id_number”值不一致，则接口返回报错，提示“当前工单不在该执行组织下，无法调整”（该场景主要用于工单被改派到其他执行组织后，不允许再由原执行组织调整该工单）
        @ApiModelProperty(value = "执行组织", example = "HERRS", required = true)
        @NotBlank(message = "执行组织不能为空")
        private String woopWorkcenterIdNumber;

        @ApiModelProperty(value = "工单任务状态", example = "WSCH", required = false)
        @NotNull(message = "工单任务状态不能为空")
        private WorkOrderStatusEnum woopStatus;

        @ApiModelProperty(value = "执行人编码", example = "ID-001410", required = false)
        private String woopWcpIdNumber;

        @ApiModelProperty(value = "计划状态", example = "PENDING", required = false)
        @NotNull(message = "计划状态不能为空")
        private PlanningStatus digiSkdStatus;

        // 假设的枚举类来表示计划状态
        public enum PlanningStatus {
            ASAP("尽快执行"),
            PENDING("待排计划"),
            FIXED("计划暂定，待发布"),
            RELEASED("已发布计划");

            private final String description;

            PlanningStatus(String description) {
                this.description = description;
            }

            public String getDescription() {
                return description;
            }
        }
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class DigiWoCheckItem {
        private String ckActualValue;
        private Long id;
        private String ckName;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class DigiAttachmentEntry {
        private String digiAttName;
        private List<DigiTypeAttachment> digiTypeAttachment;
        private String id;
        private String digiAttType;
        private String digiAttTypeCom;
        private Integer seq;


        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class DigiTypeAttachment {
            // private LocalDateTime createtime;
            // private String previewurl;
            // private LocalDateTime modifytime;
            // private String tempfile;
            // private String description;
            // private String type;
            private String url;
            // private Integer filesource;
            private String number;
            // private String masterid;
            // private String uid;
            // private String size;
            private String name;
            private String id;
            // private String status;
        }
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Attachment {
        private String originUrl;
        private String name;
        private String type;
        //private boolean isRequired;
    }
}
