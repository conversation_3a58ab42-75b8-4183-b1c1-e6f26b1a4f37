package com.bonc.rrs.gace.service.impl;

import cn.hutool.http.HttpUtil;
import com.bonc.rrs.gace.config.GaceConfig;
import com.bonc.rrs.gace.dto.request.WOrderQueryRequest;
import com.bonc.rrs.gace.dto.response.WOrderQueryResponse;
import com.bonc.rrs.gace.enums.GaceOrderTypeEnum;
import com.bonc.rrs.gace.service.GacePullService;
import com.bonc.rrs.gace.util.GaceRegionInfo;
import com.bonc.rrs.gace.util.GeoCode;
import com.bonc.rrs.gace.util.GeoCodeResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youngking.lenmoncore.common.constant.WorderTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 维修工单拉取服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GaceRepairPullService extends GacePullService {


    @Override
    public GaceOrderTypeEnum getWorderType() {
        return GaceOrderTypeEnum.RM;
    }

    @Override
    protected WOrderQueryRequest createQueryRequest(String startTime, String endTime, String billNo) {
        return WOrderQueryRequest.builder()
                // 查询维修工单时，【负责组织】传“KFGLZX”，【执行组织】传“HERRS”
                .workcenter_id_number("KFGLZX")
                .woop_workcenter_number("HERRS")
                // 工单类型
                .wo_type_id_digi_basic_type(getWorderType().getCode())
                // 设置分页大小
                .pageSize(gaceConfig.getPageSize()).pageNo(1)
                // 设置开始/结束时间
                .starttime(startTime).endtime(endTime)
                // 设置工单号
                .billno(billNo)
                .build();
    }

    @Override
    protected GaceRegionInfo getReginName(WOrderQueryResponse.WOrder wOrder) {

        GaceRegionInfo regin = new GaceRegionInfo();
        regin.setProvinceName(wOrder.getDigi_attribute3());
        regin.setCityName(wOrder.getDigi_attribute4());
        regin.setAreaName(wOrder.getDigi_attribute5());
        regin.setDetailedAddress(wOrder.getDigi_attribute6());

        final String provinceName = wOrder.getDigi_attribute3();
        final String cityName = wOrder.getDigi_attribute4();
        final String areaName = wOrder.getDigi_attribute5();
        final String fullAddress = wOrder.getDigi_attribute6();
        final String description = wOrder.getDescription();
        // 合并条件判断
        String finalAddress = StringUtils.isNotBlank(fullAddress) ? fullAddress :
                (StringUtils.isNotBlank(description) ? StringUtils.substringAfter(description, "地址") : null);

        if (StringUtils.isAnyBlank(provinceName, cityName, areaName) && StringUtils.isNotBlank(finalAddress)) {

            fillByAmapApi(finalAddress, gaceConfig.getAmapParam(),
                    // Province处理
                    value -> {
                        regin.setProvinceName(StringUtils.defaultIfBlank(provinceName, value.getProvince()));
                    },
                    // City处理
                    value -> {
                        if (!CollectionUtils.isEmpty(value.getCity())) {
                            regin.setCityName(StringUtils.defaultIfBlank(cityName, value.getCity().get(0)));
                        }
                    },
                    // District处理
                    value -> {
                        if (!CollectionUtils.isEmpty(value.getDistrict())) {
                            regin.setAreaName(StringUtils.defaultIfBlank(areaName, value.getDistrict().get(0)));
                        }
                    },
                    // Address处理
                    value -> {
                        regin.setDetailedAddress(StringUtils.defaultIfBlank(fullAddress, value.getFormattedAddress()));
                    });
        }

        return regin;
    }

    @SafeVarargs
    protected final void fillByAmapApi(String address, GaceConfig.AmapParam param,
                                       Consumer<GeoCode>... handlers) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("key", param.getKey());
            params.put("address", address);

            // 添加超时配置（示例值，需根据实际情况配置）
            String ampResult = HttpUtil.get(gaceConfig.getAmapParam().getUrl(), params);

            ObjectMapper objectMapper = new ObjectMapper();
            GeoCodeResponse response = objectMapper.readValue(ampResult, GeoCodeResponse.class);
            if (response.getStatus() == 1 && response.getCount() > 0
                    && !CollectionUtils.isEmpty(response.getGeocodes())) {
                GeoCode geoCode = response.getGeocodes().get(0);
                for (Consumer<GeoCode> handler : handlers) {
                    handler.accept(geoCode);
                }
            } else {
                log.error("高德地图接口返回异常, 响应内容: {}", ampResult);
            }
        } catch (JsonProcessingException e) {
            log.error("高德地图响应解析失败 | address:{}", address, e);
        } catch (IOException e) {
            log.error("高德地图接口通信异常 | address:{}", address, e);
        }
    }

    /**
     * 根据订单信息确定品牌ID
     * 此方法通过订单中提供的品牌名称，查询品牌实体的ID返回如果品牌名称为空或对应的品牌实体不存在，则返回0
     *
     * @param wOrder 订单对象，包含品牌信息
     * @return 品牌实体的ID如果找不到对应品牌或品牌名称为空，则返回0
     */
    @Override
    protected Integer determineBrand(WOrderQueryResponse.WOrder wOrder) {
        String woTypeIdNumber = wOrder.getWo_type_id_number();
        //只对接私桩维修
        if (StringUtils.equals(woTypeIdNumber, "SZWX")) {
            return Integer.valueOf(gaceConfig.getRepairTemplateId());
        }
        return 0;
    }

    @Override
    protected Integer determineTemplate(WOrderQueryResponse.WOrder wOrder, Integer brandId, WorderTypeEnum worderTypeEnum, GaceRegionInfo regionInfo) {
        return Integer.valueOf(gaceConfig.getRepairTemplateId());
    }
}
