package com.bonc.rrs.warning.runnable;

import com.bonc.rrs.message.entity.WorderMessageEntity;
import com.bonc.rrs.message.service.MsgService;
import com.bonc.rrs.warning.schedule.SchedulerRegistry;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 项目启动初始化启动延时的预警信息
 * @Author: liujunpeng
 * @Date: 2021/6/29 16:57
 * @Version: 1.0
 */
@Component
@AllArgsConstructor
@Log4j2
public class WarningPostConstruct {

    private final MsgService msgService;

    private final SchedulerRegistry schedulerRegistry;

    @PostConstruct
    public void initWarningMessage() {
        //查询预警延迟消息状态记录中未处理的延迟消息
        List<WorderMessageEntity> worderMessageList = msgService.selectwArningDelayMessage("0");
        if (Objects.nonNull(worderMessageList) && worderMessageList.size() > 0) {
            worderMessageList.forEach(worderMessageEntity -> {
                //执行
                schedulerRegistry.addSendTriggerCronTask(worderMessageEntity, worderMessageEntity.getSendTimeStamp(), worderMessageEntity.getId());
            });
        }
    }
}
