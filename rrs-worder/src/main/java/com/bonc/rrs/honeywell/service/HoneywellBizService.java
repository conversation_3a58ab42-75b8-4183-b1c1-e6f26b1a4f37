package com.bonc.rrs.honeywell.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.honeywell.entity.FinishDispatchRequest;
import com.bonc.rrs.honeywell.entity.HoneywellOrderDetail;
import com.bonc.rrs.honeywell.entity.HoneywellPushOrderRequest;
import com.bonc.rrs.honeywell.entity.UpdateDispatchRequest;
import com.bonc.rrs.serviceprovider.po.RegionCode;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.util.SmsUtil;
import com.bonc.rrs.util.UserUtil;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.service.*;
import com.youngking.lenmoncore.common.constant.WorderTypeEnum;
import com.youngking.lenmoncore.common.entity.UpstreamSystemIdEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.util.Pair;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class HoneywellBizService {

    private final HoneywellApiService honeywellApiService;
    private final BizRegionService bizRegionService;
    private final WorderTemplateService worderTemplateService;
    private final WorderInformationService worderInformationService;
    private final WorderIntfMessageService worderIntfMessageService;
    private final WorderInformationAttributeService worderInformationAttributeService;
    private final SysDictionaryDetailService sysDictionaryDetailService;

    private static final int HONEYWELL_BRAND_ID = 111;

    public void saveOrder(HoneywellPushOrderRequest pushOrderRequest) {
        if (checkIfSavedAlready(pushOrderRequest)) return;

        UserUtil.createDefaultLoginUser();
        WorderIntfMessageEntity messageEntity = WorderIntfMessageEntity.builder()
                .intfCode("pushOrder")
                .worderId(0)
                .bid(UpstreamSystemIdEnum.Honeywell.getCode())
                .data(JSON.toJSONString(pushOrderRequest))
                .createTime(new Date())
                .isTransfer(0)
                .messageType(0)
                .orderCode(pushOrderRequest.getOrderCode())
                .build();
        worderIntfMessageService.save(messageEntity);

        Pair<Long, String> worderIdNoPair = null;
        String worderNo = null;
        try {
            worderIdNoPair = ((HoneywellBizService) AopContext.currentProxy()).doParseDataAndSaveOrder(pushOrderRequest);
            worderIntfMessageService.updateWorderIdById(messageEntity.getId(), Math.toIntExact(worderIdNoPair.getFirst()));
            worderNo = worderIdNoPair.getSecond();
        } catch (Exception e) {
            log.error("创建霍尼韦尔工单失败", e);
            SmsUtil.sendSms("15910305046", "霍尼韦尔推送工单失败,车企订单号:" + pushOrderRequest.getOrderCode() + " " + e.getMessage(), "【到每家科技服务】");
            worderIntfMessageService.updateMessageTypeById(messageEntity.getId(), 2, e.getMessage());
            throw e;
        }

        try {
            Results results = worderInformationService.goAutoSendWorder(worderNo, ConstantPool.NEWS_OPERATOR_NAME, null);
            if (results.getCode() != 0) {
                throw new RRException(results.getCode() + " " + results.getMsg());
            }
            // 修改工单状态 0
            worderInformationService.updateWorderStatus(worderNo);
        } catch (Exception e) {
            log.error("{}派单失败", worderNo, e);
            SmsUtil.sendSms("15910305046", "霍尼韦尔工单派单失败,原因:" + e.getMessage() + ",车企订单号:" + pushOrderRequest.getOrderCode(), "【到每家科技服务】");
        }
    }

    private boolean checkIfSavedAlready(HoneywellPushOrderRequest pushOrderRequest) {
        if (worderInformationService.validateCompanyOrderNumberAndBrandExsitByBrandId(pushOrderRequest.getOrderCode(), String.valueOf(HONEYWELL_BRAND_ID))) {
            try {
                WorderIntfMessageEntity intfMessageEntity = worderIntfMessageService.getOneByOrderCode(String.valueOf(UpstreamSystemIdEnum.Honeywell.getCode()), pushOrderRequest.getOrderCode());
                if (intfMessageEntity == null) {
                    WorderIntfMessageEntity messageEntity = WorderIntfMessageEntity.builder()
                            .intfCode("pushOrder")
                            .worderId(0)
                            .bid(UpstreamSystemIdEnum.Honeywell.getCode())
                            .data(JSON.toJSONString(pushOrderRequest))
                            .createTime(new Date())
                            .isTransfer(0)
                            .messageType(1)
                            .orderCode(pushOrderRequest.getOrderCode())
                            .build();
                    worderIntfMessageService.save(messageEntity);
                } else if (intfMessageEntity.getMessageType() != 1 || intfMessageEntity.getWorderId() == null) {
                    if (intfMessageEntity.getWorderId() == null) {
                        WorderInformationEntity byCompanyWorderNo = worderInformationService.getByCompanyWorderNo(pushOrderRequest.getOrderCode());
                        intfMessageEntity.setWorderId(byCompanyWorderNo.getWorderId());
                    }
                    intfMessageEntity.setMessageType(1);
                    worderIntfMessageService.updateById(intfMessageEntity);
                }
                return true;
            } catch (Exception e) {
                log.error("保存报文失败:{}", JSON.toJSONString(pushOrderRequest), e);
                return false;
            }
        }
        return false;
    }

    @Lock4j(keys = "#pushOrderRequest.orderCode")
    public Pair<Long, String> doParseDataAndSaveOrder(HoneywellPushOrderRequest pushOrderRequest) {
        // Map<String, String> honeywellPileSkuList = sysDictionaryDetailService.findByDictNumber("honeywell_pile_sku_list")
        //         .stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName));

        // check
        // String skuSpec = honeywellPileSkuList.get(pushOrderRequest.getPileInfo().getSkuCode());

        HoneywellOrderDetail pileOrderDetail = pushOrderRequest.getPileInfo();

        RegionCode regionCode = bizRegionService.determineRegion(pushOrderRequest.getProvinceCode(), pushOrderRequest.getCityCode(),
                pushOrderRequest.getAreaCode());
        List<WorderTemplateDto> worderTemplateDtoList = worderTemplateService.findTemplateInfoByBrandIdAndWorderTypeIdAndRegion(
                HONEYWELL_BRAND_ID,
                WorderTypeEnum.SERVE_CONVEY_INSTALL.getId(),
                regionCode.getProvinceCode().intValue(),
                regionCode.getCityCode().intValue()
        );

        if (CollectionUtils.isEmpty(worderTemplateDtoList)) {
            log.info("message save order {} 没有对应的工单模板", pushOrderRequest.getOrderCode());
            throw new RRException("Failed to save order " + pushOrderRequest.getOrderCode());
        }

        Map<String, String> honeywellSuitTemplateMap = sysDictionaryDetailService.findByDictNumber("honeywell_suit_template_map")
                .stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName));

        Integer templateId = null;
        for (HoneywellOrderDetail honeywellOrderDetail : pushOrderRequest.getInstallInfo()) {
            if (honeywellSuitTemplateMap.containsKey(honeywellOrderDetail.getSkuCode())) {
                String templateIdStr = honeywellSuitTemplateMap.get(honeywellOrderDetail.getSkuCode());
                templateId = Integer.valueOf(templateIdStr);
            }
        }

        WorderTemplateDto worderTemplateDto;
        if (templateId == null) {
            worderTemplateDto = worderTemplateDtoList.get(0);
        } else {
            Integer finalTemplateId = templateId;
            worderTemplateDto = worderTemplateDtoList.stream().filter(e -> Objects.equals(e.getTemplateId(), finalTemplateId)).findFirst().orElse(worderTemplateDtoList.get(0));
        }

        // List<HoneywellOrderDetail> collect = pushOrderRequest.getInstallInfo().stream().filter(o -> honeywellSuitTemplateMap.containsKey(o.getSkuCode())).collect(Collectors.toList());
        // if (CollectionUtils.isEmpty(collect) || collect.get(0) == null) {
        //     log.info("message save order {} 没有对应的套包信息", pushOrderRequest.getOrderCode());
        //     throw new RRException("Failed to save order " + pushOrderRequest.getOrderCode() + ", because no install package info");
        // }

        if (worderInformationService.validateCompanyOrderNumberAndBrandExsit(pushOrderRequest.getOrderCode(), worderTemplateDto.getTemplateId())) {
            log.info("message save order " + pushOrderRequest.getOrderCode() + " 车企订单号已存在，无法创建工单");
            throw new RRException("Failed to save order " + pushOrderRequest.getOrderCode());
        }

        // 匹配表情符
        String regex = "([\\u20A0-\\u32FF\\uD83C-\\uDFFF\\u2600-\\u27FF])|([\\uD830-\\uD83F][\\uDC00-\\uDFFF])";

        // todo
        Integer companyId = 856;

        WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

        String address =
                regionCode.getProvinceCode() + "_" + regionCode.getCityCode() + "_" + regionCode.getAreaCode() + "_" + pushOrderRequest.getContactAddress();
        address = address.replaceAll(regex, "");

        String userName = pushOrderRequest.getContactName();
        userName = userName.replaceAll(regex, "");

        String dispatchTime = DateUtils.parseTimestamp(String.valueOf(pushOrderRequest.getOrderTime()));
        String contactRemark = "";
        if (StringUtils.isNotBlank(pushOrderRequest.getOrderRemark())) {
            contactRemark = pushOrderRequest.getOrderRemark().replaceAll(regex, "");
        }
        worderInfoEntity.setPushOrderWorderSource("honeywell");
        worderInfoEntity.setUserName(userName);
        worderInfoEntity.setUserPhone(pushOrderRequest.getContactMobile());
        worderInfoEntity.setAddress(address);
        worderInfoEntity.setCompanyOrderNumber(pushOrderRequest.getOrderCode());
        worderInfoEntity.setTemplateId(worderTemplateDto.getTemplateId());

        worderInfoEntity.setCarBrand(String.valueOf(HONEYWELL_BRAND_ID));
        worderInfoEntity.setCarModel("4");
        worderInfoEntity.setCompanyId(companyId);

        worderInfoEntity.setPostcode("");
        worderInfoEntity.setWorderSourceTypeValue("");
        worderInfoEntity.setWorderTypeId(5);

        worderInfoEntity.setCandidate(ConstantPool.NEWS_OPERATOR_NAME);
        worderInfoEntity.setCreator(ConstantPool.NEWS_OPERATOR);

        List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();

        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1, "工单类型", worderInfoEntity.getWorderTypeId()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(5, "车企订单号", worderInfoEntity.getCompanyOrderNumber()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(101, "车企名称", companyId));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(154, "车企派单日期", dispatchTime));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(153, "VIN 车架号", ""));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(305, "备注-创建", contactRemark));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(306, "工单来源", ""));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(902, "客户姓名", worderInfoEntity.getUserName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(903, "安装地址", address));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(904, "客户邮箱", ""));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(905, "客户手机", worderInfoEntity.getUserPhone()));

        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1694, "充电桩功率", pileOrderDetail.getMaterialCode()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1787, "商品名称", pileOrderDetail.getMaterialName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1781, "商品编号", pileOrderDetail.getSkuCode()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1782, "商品sku编号", pileOrderDetail.getSkuName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1783, "商品sku名称", pileOrderDetail.getSpecColor()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1784, "商品sku价格", pileOrderDetail.getNum()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(911, "联系信息备注", pushOrderRequest.getOrderRemark()));

        worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);

        R r = worderInformationService.saveWorderInformationByServiceProvider(worderInfoEntity);
        if (!r.isOk()) {
            throw new RRException("save worder information error");
        }
        return new Pair<>((Long) r.get("worderId"), (String) r.get("worderNo"));
    }

    public void updateDispatch(UpdateDispatchRequest request) {
        if (checkIfPushToHoneywell(null, request.getOrderCode())) {
            honeywellApiService.updateDispatch(request);
        }
    }

    public void updateDispatch(Supplier<UpdateDispatchRequest> requestSupplier, String orderCode) {
        if (checkIfPushToHoneywell(null, orderCode)) {
            honeywellApiService.updateDispatch(requestSupplier.get());
        }
    }

    public void finishDispatch(FinishDispatchRequest finishDispatchRequest) {
        if (checkIfPushToHoneywell(null, finishDispatchRequest.getOrderCode())) {
            honeywellApiService.finishDispatch(finishDispatchRequest);
        }
    }

    boolean checkIfPushToHoneywell(String worderNo, String companyOrderNo) {
        if (StringUtils.isAllBlank(worderNo, companyOrderNo)) {
            return false;
        }
        WorderInformationAttributeEntity worderInformationAttributeEntity;
        // 查询是否存在订单属性信息
        if (StringUtils.isNotBlank(worderNo)) {
            worderInformationAttributeEntity = worderInformationAttributeService.selectAttributeByWorderNo(worderNo,
                    "worder_source", "pushOrder");
        } else {
            worderInformationAttributeEntity = worderInformationAttributeService.selectAttributeByCompanyOrderNo(companyOrderNo,
                    "worder_source", "pushOrder");
        }

        if (worderInformationAttributeEntity == null) {
            return false;
        }
        return "honeywell".equals(worderInformationAttributeEntity.getAttributeValue());
    }
}
