package com.bonc.rrs.workManager.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.worder.entity.BizRegionEntity;
import com.bonc.rrs.workManager.entity.BizRegion;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface BizRegionMapper extends BaseMapper<BizRegionEntity> {

    List<BizRegion> selectArealist(@Param(value = "pid") Integer pid);
    BizRegion selectArealistById(@Param(value = "id") Integer id);

    String areaName(@Param(value = "id") Integer id);

    String selectAreaName(@Param(value = "id") Integer id);

    List<Map<String,String>> selectRegionlist(@Param(value = "code") String code);

    List<Map<String,Object>> selctDotFuSheInfo(@Param(value = "dotId") Integer dotId);

    String findParentName(@Param(value = "id") Integer id);

    List<BizRegionEntity> selectAreaByUserId(@Param("userId") Long userId);

    List<BizRegionEntity> selectListByDot(@Param("dotId") Integer dotId);
}