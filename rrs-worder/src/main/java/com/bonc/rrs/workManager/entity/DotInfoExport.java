package com.bonc.rrs.workManager.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 网点信息
 * @Author: liujunpeng
 * @Date: 2022/1/27 15:12
 * @Version: 1.0
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@HeadRowHeight(20)
@ContentRowHeight(15)
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER)
public class DotInfoExport implements Serializable {

    @ExcelProperty(value = "主键ID")
    @ColumnWidth(25)
    private Integer dotId;

    @ExcelProperty(value = "网点星级评分")
    @ColumnWidth(25)
    private String dotStar;

    @ExcelProperty(value = "网点名称")
    @ColumnWidth(50)
    private String dotName;


    @ExcelProperty(value = "网点简称")
    @ColumnWidth(50)
    private String dotShortName;

    @ExcelProperty(value = "网点省")
    @ColumnWidth(25)
    private String dotArea;

    @ExcelProperty(value = "网点城市")
    @ColumnWidth(25)
    private String dotCity;

    @ExcelProperty(value = "网点行政区")
    @ColumnWidth(25)
    private String dotDistrict;

    @ExcelProperty(value = "网点地址")
    @ColumnWidth(40)
    private String dotAddress;

    @ExcelProperty(value = "网点营业执照")
    @ColumnWidth(25)
    private String dotCertificate;

    @ExcelProperty(value = "网点性质")
    @ColumnWidth(25)
    private String dotQuality;

    @ExcelProperty(value = "网点邮编")
    @ColumnWidth(25)
    private String dotPostcode;

    @ExcelProperty(value = "网点种类")
    @ColumnWidth(25)
    private String dotClass;

    @ExcelProperty(value = "网点特性")
    @ColumnWidth(25)
    private String dotFeatures;

    @ExcelProperty(value = "建点时间")
    @ColumnWidth(25)
    private Date dotCreateTime;

    @ExcelProperty(value = "建点人")
    @ColumnWidth(25)
    private String dotCreatePer;

    @ExcelProperty(value = "网点编码")
    @ColumnWidth(25)
    private String dotCode;

    @ExcelProperty(value = "网点计算得分")
    @ColumnWidth(25)
    private Integer dotScore;

    @ExcelProperty(value = "网点签退状态  1.允许强制签退 2.允许正常签退 3.允许有理由签退")
    @ColumnWidth(25)
    private Integer dotSignOut;

    @ExcelProperty(value = "市场级别")
    @ColumnWidth(25)
    private String marketLevel;

    @ExcelProperty(value = "记录创建时间")
    @ColumnWidth(25)
    private Date createTime;

    @ExcelProperty(value = "法人名称")
    @ColumnWidth(25)
    private String legalPersonName;

    @ExcelProperty(value = "法人性别")
    @ColumnWidth(25)
    private String legalPersonSex;

    @ExcelProperty(value = "文化程度")
    @ColumnWidth(25)
    private String degreeEducation;

    @ExcelProperty(value = "电话")
    @ColumnWidth(25)
    private String telephone;

    @ExcelProperty(value = "备件库面积")
    @ColumnWidth(25)
    private String storageSpare;

    @ExcelProperty(value = "记录修改时间")
    @ColumnWidth(25)
    private Date modifyTime;

    @ExcelProperty(value = "交通工具数量")
    @ColumnWidth(25)
    private Integer vehiclesNums;

    @ExcelProperty(value = "合同开始")
    @ColumnWidth(25)
    private Date contractStart;

    @ExcelProperty(value = "合同结束")
    @ColumnWidth(25)
    private Date contractEnd;

    @ExcelProperty(value = "税号")
    @ColumnWidth(25)
    private String taxNo;

    @ExcelProperty(value = "企业性质")
    @ColumnWidth(25)
    private String companyQuality;

    @ExcelProperty(value = "注册资金")
    @ColumnWidth(25)
    private String registFund;

    @ExcelProperty(value = "工商登记时间")
    @ColumnWidth(25)
    private Date buseRegistTime;

    @ExcelProperty(value = "税务登记时间")
    @ColumnWidth(25)
    private Date taxRegistTime;

    @ExcelProperty(value = "税务有效截止时间")
    @ColumnWidth(25)
    private Date taxDeadline;

    @ExcelProperty(value = "工商有效截止时间")
    @ColumnWidth(25)
    private Date industryDeadline;

    @ExcelProperty(value = "工商注册号")
    @ColumnWidth(25)
    private String buseRegistNumber;

    @ExcelProperty(value = "认证时间")
    @ColumnWidth(25)
    private Date approveTime;

    @ExcelProperty(value = "网点税点")
    @ColumnWidth(25)
    private String taxPoInteger;

    @ExcelProperty(value = "辐射区域")
    @ColumnWidth(25)
    private String radiateArea;

    @ExcelProperty(value = "网点状态  1.有效，2.无效，3.冻结，4.停工 5.撤点 ")
    @ColumnWidth(25)
    private String dotState;

    @ExcelProperty(value = "营业执照")
    @ColumnWidth(25)
    private String buseLicenseUrl;

    @ExcelProperty(value = "办公场地面积")
    @ColumnWidth(25)
    private String officeSpaceUrl;

    @ExcelProperty(value = "备件库面积")
    @ColumnWidth(25)
    private String sparesSpaceUrl;

    @ExcelProperty(value = "门头")
    @ColumnWidth(25)
    private String doorHeadUrl;

    @ExcelProperty(value = "交通工具")
    @ColumnWidth(25)
    private String vehicleUrl;

    @ExcelProperty(value = "服务工具")
    @ColumnWidth(25)
    private String serviceAidUrl;

    @ExcelProperty(value = "耗材")
    @ColumnWidth(25)
    private String consumableUrl;

    @ExcelProperty(value = "建点基础数据基础表")
    @ColumnWidth(25)
    private String basicDataUrl;

    @ExcelProperty(value = "房屋合同")
    @ColumnWidth(25)
    private String houseContractUrl;

    @ExcelProperty(value = "合同url")
    @ColumnWidth(25)
    private String contractUrl;

    @ExcelProperty(value = "法人身份证")
    @ColumnWidth(25)
    private String corporateIdCard;

    @ExcelProperty(value = "电工证")
    @ColumnWidth(25)
    private String electLicense;

    @ExcelProperty(value = "资质")
    @ColumnWidth(25)
    private String aptitude;

    @ExcelProperty(value = "一般纳税人")
    @ColumnWidth(25)
    private String commonTaxPer;

    @ExcelProperty(value = "车小鳐数量")
    @ColumnWidth(25)
    private Integer smallCarRays;

    @ExcelProperty(value = "普通车数量")
    @ColumnWidth(25)
    private Integer commonCarNum;

    @ExcelProperty(value = "网点联系人")
    @ColumnWidth(25)
    private String contactName;

    @ExcelProperty(value = "网点手机号")
    @ColumnWidth(25)
    private String dotTelephone;

    @ExcelProperty(value = "网点邮箱")
    @ColumnWidth(25)
    private String dotEmail;

    @ExcelProperty(value = "是否删除 1是0否")
    @ColumnWidth(25)
    private Integer isDelete;

    @ExcelProperty(value = "网点星级ID")
    @ColumnWidth(25)
    private Integer starId;

    @ExcelProperty(value = "客户码86码")
    @ColumnWidth(25)
    private String customerCode;

    @ExcelProperty(value = "网点V码")
    @ColumnWidth(25)
    private String vCode;

    @ExcelProperty(value = "工贸编号")
    @ColumnWidth(25)
    private String branchCode;

    @ExcelProperty(value = "工贸名称")
    @ColumnWidth(25)
    private String branchName;

    @ExcelProperty(value = "开户行")
    @ColumnWidth(25)
    private String dotBank;

    @ExcelProperty(value = "开户行账号")
    @ColumnWidth(25)
    private String bankAccount;

    @ExcelProperty(value = "开户行行号")
    @ColumnWidth(25)
    private String bankNumber;

    @ExcelProperty(value = "一个周期中获得的订单数量")
    @ColumnWidth(25)
    private Integer count;

    @ExcelProperty(value = "厂商id")
    @ColumnWidth(25)
    private Integer companyId;

    @ExcelProperty(value = "厂商类型  10:车企 11：经销商  12：分中心")
    @ColumnWidth(25)
    private Integer companyType;

    @ExcelProperty(value = "户头")
    @ColumnWidth(25)
    private String bankName;

    public Integer getDotId() {
        return dotId;
    }

    public void setDotId(Integer dotId) {
        this.dotId = dotId;
    }

    public String getDotStar() {
        return dotStar;
    }

    public void setDotStar(String dotStar) {
        this.dotStar = dotStar;
    }

    public String getDotName() {
        return dotName;
    }

    public void setDotName(String dotName) {
        this.dotName = dotName;
    }

    public String getDotArea() {
        return dotArea;
    }

    public void setDotArea(String dotArea) {
        this.dotArea = dotArea;
    }

    public String getDotCity() {
        return dotCity;
    }

    public void setDotCity(String dotCity) {
        this.dotCity = dotCity;
    }

    public String getDotDistrict() {
        return dotDistrict;
    }

    public void setDotDistrict(String dotDistrict) {
        this.dotDistrict = dotDistrict;
    }

    public String getDotAddress() {
        return dotAddress;
    }

    public void setDotAddress(String dotAddress) {
        this.dotAddress = dotAddress;
    }

    public String getDotCertificate() {
        return dotCertificate;
    }

    public void setDotCertificate(String dotCertificate) {
        this.dotCertificate = dotCertificate;
    }

    public String getDotQuality() {
        return dotQuality;
    }

    public void setDotQuality(String dotQuality) {
        this.dotQuality = dotQuality;
    }

    public String getDotPostcode() {
        return dotPostcode;
    }

    public void setDotPostcode(String dotPostcode) {
        this.dotPostcode = dotPostcode;
    }

    public String getDotClass() {
        return dotClass;
    }

    public void setDotClass(String dotClass) {
        this.dotClass = dotClass;
    }

    public String getDotFeatures() {
        return dotFeatures;
    }

    public void setDotFeatures(String dotFeatures) {
        this.dotFeatures = dotFeatures;
    }

    public Date getDotCreateTime() {
        return dotCreateTime;
    }

    public void setDotCreateTime(Date dotCreateTime) {
        this.dotCreateTime = dotCreateTime;
    }

    public String getDotCreatePer() {
        return dotCreatePer;
    }

    public void setDotCreatePer(String dotCreatePer) {
        this.dotCreatePer = dotCreatePer;
    }

    public String getDotCode() {
        return dotCode;
    }

    public void setDotCode(String dotCode) {
        this.dotCode = dotCode;
    }

    public Integer getDotScore() {
        return dotScore;
    }

    public void setDotScore(Integer dotScore) {
        this.dotScore = dotScore;
    }

    public Integer getDotSignOut() {
        return dotSignOut;
    }

    public void setDotSignOut(Integer dotSignOut) {
        this.dotSignOut = dotSignOut;
    }

    public String getMarketLevel() {
        return marketLevel;
    }

    public void setMarketLevel(String marketLevel) {
        this.marketLevel = marketLevel;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getLegalPersonName() {
        return legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public String getLegalPersonSex() {
        return legalPersonSex;
    }

    public void setLegalPersonSex(String legalPersonSex) {
        this.legalPersonSex = legalPersonSex;
    }

    public String getDegreeEducation() {
        return degreeEducation;
    }

    public void setDegreeEducation(String degreeEducation) {
        this.degreeEducation = degreeEducation;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getStorageSpare() {
        return storageSpare;
    }

    public void setStorageSpare(String storageSpare) {
        this.storageSpare = storageSpare;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getVehiclesNums() {
        return vehiclesNums;
    }

    public void setVehiclesNums(Integer vehiclesNums) {
        this.vehiclesNums = vehiclesNums;
    }

    public Date getContractStart() {
        return contractStart;
    }

    public void setContractStart(Date contractStart) {
        this.contractStart = contractStart;
    }

    public Date getContractEnd() {
        return contractEnd;
    }

    public void setContractEnd(Date contractEnd) {
        this.contractEnd = contractEnd;
    }

    public String getTaxNo() {
        return taxNo;
    }

    public void setTaxNo(String taxNo) {
        this.taxNo = taxNo;
    }

    public String getCompanyQuality() {
        return companyQuality;
    }

    public void setCompanyQuality(String companyQuality) {
        this.companyQuality = companyQuality;
    }

    public String getRegistFund() {
        return registFund;
    }

    public void setRegistFund(String registFund) {
        this.registFund = registFund;
    }

    public Date getBuseRegistTime() {
        return buseRegistTime;
    }

    public void setBuseRegistTime(Date buseRegistTime) {
        this.buseRegistTime = buseRegistTime;
    }

    public Date getTaxRegistTime() {
        return taxRegistTime;
    }

    public void setTaxRegistTime(Date taxRegistTime) {
        this.taxRegistTime = taxRegistTime;
    }

    public Date getTaxDeadline() {
        return taxDeadline;
    }

    public void setTaxDeadline(Date taxDeadline) {
        this.taxDeadline = taxDeadline;
    }

    public Date getIndustryDeadline() {
        return industryDeadline;
    }

    public void setIndustryDeadline(Date industryDeadline) {
        this.industryDeadline = industryDeadline;
    }

    public String getBuseRegistNumber() {
        return buseRegistNumber;
    }

    public void setBuseRegistNumber(String buseRegistNumber) {
        this.buseRegistNumber = buseRegistNumber;
    }

    public Date getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public String getTaxPoInteger() {
        return taxPoInteger;
    }

    public void setTaxPoInteger(String taxPoInteger) {
        this.taxPoInteger = taxPoInteger;
    }

    public String getRadiateArea() {
        return radiateArea;
    }

    public void setRadiateArea(String radiateArea) {
        this.radiateArea = radiateArea;
    }

    public String getDotState() {
        return dotState;
    }

    public void setDotState(String dotState) {
        this.dotState = dotState;
    }

    public String getBuseLicenseUrl() {
        return buseLicenseUrl;
    }

    public void setBuseLicenseUrl(String buseLicenseUrl) {
        this.buseLicenseUrl = buseLicenseUrl;
    }

    public String getOfficeSpaceUrl() {
        return officeSpaceUrl;
    }

    public void setOfficeSpaceUrl(String officeSpaceUrl) {
        this.officeSpaceUrl = officeSpaceUrl;
    }

    public String getSparesSpaceUrl() {
        return sparesSpaceUrl;
    }

    public void setSparesSpaceUrl(String sparesSpaceUrl) {
        this.sparesSpaceUrl = sparesSpaceUrl;
    }

    public String getDoorHeadUrl() {
        return doorHeadUrl;
    }

    public void setDoorHeadUrl(String doorHeadUrl) {
        this.doorHeadUrl = doorHeadUrl;
    }

    public String getVehicleUrl() {
        return vehicleUrl;
    }

    public void setVehicleUrl(String vehicleUrl) {
        this.vehicleUrl = vehicleUrl;
    }

    public String getServiceAidUrl() {
        return serviceAidUrl;
    }

    public void setServiceAidUrl(String serviceAidUrl) {
        this.serviceAidUrl = serviceAidUrl;
    }

    public String getConsumableUrl() {
        return consumableUrl;
    }

    public void setConsumableUrl(String consumableUrl) {
        this.consumableUrl = consumableUrl;
    }

    public String getBasicDataUrl() {
        return basicDataUrl;
    }

    public void setBasicDataUrl(String basicDataUrl) {
        this.basicDataUrl = basicDataUrl;
    }

    public String getHouseContractUrl() {
        return houseContractUrl;
    }

    public void setHouseContractUrl(String houseContractUrl) {
        this.houseContractUrl = houseContractUrl;
    }

    public String getContractUrl() {
        return contractUrl;
    }

    public void setContractUrl(String contractUrl) {
        this.contractUrl = contractUrl;
    }

    public String getCorporateIdCard() {
        return corporateIdCard;
    }

    public void setCorporateIdCard(String corporateIdCard) {
        this.corporateIdCard = corporateIdCard;
    }

    public String getElectLicense() {
        return electLicense;
    }

    public void setElectLicense(String electLicense) {
        this.electLicense = electLicense;
    }

    public String getAptitude() {
        return aptitude;
    }

    public void setAptitude(String aptitude) {
        this.aptitude = aptitude;
    }

    public String getCommonTaxPer() {
        return commonTaxPer;
    }

    public void setCommonTaxPer(String commonTaxPer) {
        this.commonTaxPer = commonTaxPer;
    }

    public Integer getSmallCarRays() {
        return smallCarRays;
    }

    public void setSmallCarRays(Integer smallCarRays) {
        this.smallCarRays = smallCarRays;
    }

    public Integer getCommonCarNum() {
        return commonCarNum;
    }

    public void setCommonCarNum(Integer commonCarNum) {
        this.commonCarNum = commonCarNum;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getDotTelephone() {
        return dotTelephone;
    }

    public void setDotTelephone(String dotTelephone) {
        this.dotTelephone = dotTelephone;
    }

    public String getDotEmail() {
        return dotEmail;
    }

    public void setDotEmail(String dotEmail) {
        this.dotEmail = dotEmail;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getStarId() {
        return starId;
    }

    public void setStarId(Integer starId) {
        this.starId = starId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getvCode() {
        return vCode;
    }

    public void setvCode(String vCode) {
        this.vCode = vCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getDotBank() {
        return dotBank;
    }

    public void setDotBank(String dotBank) {
        this.dotBank = dotBank;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBankNumber() {
        return bankNumber;
    }

    public void setBankNumber(String bankNumber) {
        this.bankNumber = bankNumber;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Integer getCompanyType() {
        return companyType;
    }

    public void setCompanyType(Integer companyType) {
        this.companyType = companyType;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }
}
