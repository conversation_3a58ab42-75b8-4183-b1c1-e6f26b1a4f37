package org.ichart.demo.module.ichartform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.ichart.demo.module.ichartform.model.entity.RptDataSourceItem;

import java.util.List;
import java.util.Map;

public interface RptDataSourceItemService extends IService<RptDataSourceItem> {
    List<RptDataSourceItem> selectItemByParam(String tableName, String tableCode);

    void saveEntity(Map<String, Object> params);

    void deleteDataSourceItem(Map<String, Object> params);

    List<RptDataSourceItem> getDataSourceByNameAndCode(String tableCode, String type, Long userId);
}
