<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.ichart.demo.module.ichartform.dao.RptReportDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="org.ichart.demo.module.ichartform.model.entity.RptReport" id="rptReportMap">
        <result property="id" column="ID"/>
        <result property="deleteFlag" column="DELETE_FLAG"/>
        <result property="status" column="STATUS"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="name" column="NAME"/>
        <result property="isPublish" column="Is_Publish"/>
        <result property="isShare" column="is_share"/>
        <result property="layoutHtml" column="layout_html"/>
        <result property="config" column="config"/>
        <result property="setting" column="setting"/>
        <result property="userId" column="user_id"/>
    </resultMap>


</mapper>
