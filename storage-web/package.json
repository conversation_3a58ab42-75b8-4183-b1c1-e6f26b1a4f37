{"private": true, "scripts": {"start": "umi dev", "start-yapi": "cross-env MOCK=none UMI_ENV=yapi umi dev", "build": "umi build", "test": "umi test", "lint": "eslint --ext .js src mock tests"}, "dependencies": {"antd": "^3.12.1", "axios": "^0.18.0", "bonc-element": "^1.0.19", "classnames": "^2.2.6", "dva": "^2.5.0-beta.2", "lodash": "^4.17.11", "moment": "^2.24.0", "nprogress": "^0.2.0", "path-to-regexp": "^3.0.0", "react": "^16.8.6", "react-container-query": "^0.11.0", "react-dom": "^16.8.6", "dva-loading": "2.1.0-beta.2"}, "devDependencies": {"babel-eslint": "^9.0.0", "cross-env": "^5.2.0", "eslint": "^5.4.0", "eslint-config-umi": "^1.4.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-react": "^7.11.1", "husky": "^0.14.3", "lint-staged": "^7.2.2", "memoize-one": "^5.0.0", "path-to-regexp": "^3.0.0", "react-media": "^1.9.2", "react-test-renderer": "^16.7.0", "umi": "^2.6.13", "umi-plugin-ga": "^1.1.3", "umi-plugin-react": "^1.7.4"}, "lint-staged": {"*.{js,jsx}": ["eslint --fix", "git add"]}, "engines": {"node": ">=8.0.0"}}