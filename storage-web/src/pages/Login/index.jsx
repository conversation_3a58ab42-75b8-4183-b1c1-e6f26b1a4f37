/*
 * @Author: nightowl
 * @Date: 2019-08-08 09:29:42
 * @LastEditors: nightowl
 * @LastEditTime: 2019-09-23 10:29:49
 * @Description:
 */
import { useEffect, useState } from 'react'
import { View } from 'bonc-element'
import { Form, Icon, Input, Button, Message, Row, Col } from 'antd'
import { connect } from 'dva'
import Style from './index.less'
import { saveAuthority } from '@/utils/appData'
import getUUID from '@/utils/uuid'
import { getImg } from '@/utils/request'
import logo from '@/assets/imgs/logo.png'

const FormItem = Form.Item;
const Login = ({ form, history, dispatch, loading }) => {
  const [uuid, setUUID] = useState('')
  const [captcha, setCaptcha] = useState(null)
  useEffect(() => {
    createCode()
  }, [])
  const handleSubmit = e => {
    e.preventDefault()
    form.validateFields((err, params) => {
      if (err) return Message.warning('请输入用户名、密码再提交')
      login(params)
    })
  }
  const login = async (params) => {
    let data = { username: params.user_name, password: params.user_pwd, captcha: params.captcha, uuid }
    let response = await dispatch({ type: 'login/postData', payload: { data } })
    if (response.code === 0) {
      saveAuthority({
        isLogin: true,
        // currentAuthority: response.user.username,
        token: response.token,
        // user: response.user
      })
      history.push('/home')
    } else {
      createCode();
    }
  }

  const createCode = async () => {
    let uuid = getUUID();
    setUUID(uuid);
    // let captcha = getImg(`/captcha.jpg?uuid=${uuid}`);
    // setCaptcha(captcha);
  }
  const { getFieldDecorator } = form
  return <div>
    <div className={Style.content}>
      <img className={Style.logo} src={logo} alt="logo" />
      <div className={Style.foot}>日日顺科技仓储管理系统</div>
    </div>
    <div className={Style.panel}>
      <View column className={Style.box}>
        <div className={Style.title}>日日顺科技仓储管理系统</div>
        <Form layout='vertical' onSubmit={handleSubmit}>
          <FormItem label="用户名">
            {getFieldDecorator('user_name', {
              initialValue: '',
              rules: [{ required: true, message: '请输入用户名' }]
            })(
              <Input className={Style.input} placeholder='用户名' />
            )}
          </FormItem>
          <FormItem label="密码">
            {getFieldDecorator('user_pwd', {
              initialValue: '',
              rules: [{ required: true, message: '请输入密码' }]
            })(
              <Input className={Style.input} type='password' placeholder='密码' />
            )}
          </FormItem>
          {/*<FormItem label="验证码">
            <Row gutter={8}>
              <Col span={15}>
                {getFieldDecorator('captcha', {
                  // rules: [{ required: true, message: '请输入验证码' },{
                  //   validator: (rule, value, callback) => {
                  //     if (!value || value.trim().length !== 5)
                  //       callback('验证码错误')
                  //     callback()
                  //   }
                  // }],
                })(
                  <Input maxLength={5} className={Style.input} type="text" />
                )}
              </Col>
              <Col span={9}>
                <img className={Style.captcha} src={captcha} alt="验证码" onClick={createCode.bind(this)} />
              </Col>
            </Row>
          </FormItem>*/}
          <FormItem>
            <Button className={Style.btn} type='primary' icon="login" htmlType='submit' loading={loading}>登录</Button>
          </FormItem>
        </Form>
      </View>
    </div>
  </div>
}
export default connect(({ loading }) => ({
  loading: loading.effects['login/postData']
}))(Form.create()(Login))
