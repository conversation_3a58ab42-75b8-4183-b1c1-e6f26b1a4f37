/*
 * @Author: 董子江
 * @Date: 2019-09-04 14:16:46
 * @LastEditors: 董子江
 * @LastEditTime: 2019-09-17 16:31:08
 * @Description: 厂商配置信息列表
 */

import { useEffect, useState } from 'react'
import { View } from 'bonc-element'
import { connect } from 'dva'
import { Form, Input, Button, Table, Modal, Message, Divider, Upload, Icon, Popconfirm, Tabs, Badge } from 'antd'
import router from 'umi/router';

const FormItem = Form.Item
const FirmsListView = ({ form, list, totalCount, dispatch, loading, history }) => {
  const { getFieldDecorator } = form
  const pageLimit = 10;

  //组件挂载
  useEffect(() => {
    // resetForm();
    getList()
  }, [])

  const getList = () => {
    

    dispatch({
      type: 'ordersetting/getExtfieldfactoryConfiglist',
    })
  }

  //打开
  const handleWinToggle = (companyNo, companyName) => {

    history.push({pathname:'/order/setting/firms-add',state:{edith:false,companyNo:companyNo,companyName:companyName}})
  }


  //删除
  const handleDelete = id => {
    // dispatch({
    //     type: "orderfield/delete",
    //     payload: [id]
    // })

  }
  const handleCheck = (companyNo, companyName,isEdith) => {

    // history.push(`/order/setting/firms-add?edith=true&companyNo=${companyNo}&companyName=${companyName}`)
 history.push({pathname:'/order/setting/firms-add',state:{edith:true,companyNo:companyNo,companyName:companyName}})
  }
  const columns = [
    {
      dataIndex: 'key', title: '序号', render: (text) => {
        return (<span>{text + 1}</span>)
      }
    },
    { dataIndex: 'companyName', title: '厂商名称' },
    { dataIndex: 'configYet', title: '配置情况' ,render: (text) => {
          return (<span>{text ? '是' : '否'}</span>)
        }},
    // { dataIndex: 'fieldPurposeValue', title: '备注' },
    // {
    //   dataIndex: 'isNessary', title: '是否必须选', render: (text) => {
    //     return (<span>{text ? '是' : '否'}</span>)
    //   }
    // },
    {
      title: '操作',
      dataIndex: 'id',
      width: 300,
      // fixed: 'right',
      render: (_, record) => {
        return <View wrap>
          <div>
          {record.configYet?<span className="handle-2" onClick={(vaule) => { handleCheck(record.companyNo,record.companyName) }} >修改</span>:''}
          </div>
          <div>
            <span className="handle-2" onClick={() => { handleDelete(record.companyNo) }} >删除</span>
          </div>
          <div>
          {!record.configYet?<span className="handle-2" onClick={() => { handleWinToggle(record.companyNo,record.companyName) }} >配置规则</span>:''}
          </div>
        </View>
      }

    }
  ]

  return <View column>
    <Form layout='inline'>
      <FormItem>
        <Button icon='plus' type='primary' ghost onClick={handleWinToggle} >新建规则</Button>
      </FormItem>
    </Form>
    <View column style={{ marginTop: 10 }}>
      <Table columns={columns} dataSource={list} rowKey={item => item.key} loading={loading} />
    </View>
  </View>

}
export default connect(({ ordersetting, loading, }) => ({
  list: ordersetting.extfieldfactoryConfiglist,
}))(Form.create()(FirmsListView))