import { useEffect, useState } from 'react'
import { View } from 'bonc-element'
import { connect } from 'dva'
import { Form, Select, Input, Button, Table, Modal, Message, Divider, Upload, Icon, DatePicker, Popconfirm } from 'antd'
import Service from './service'
import moment from 'moment'
import locale from 'antd/lib/date-picker/locale/zh_CN'
import 'moment/locale/zh-cn'
moment.locale('zh-cn')
const FormItem = Form.Item
const confirm = Modal.confirm;
const Option = Select.Option
const { MonthPicker, RangePicker } = DatePicker;
const WarnListPage = ({ form, list, totalCount, storeList, dispatch, visible, loading, history }) => {
  const { getFieldDecorator } = form
  const pageLimit = 10;
  const [searchParams, setSearchParams] = useState({ page: 1, limit: 10 });
  const [currentPage, setPage] = useState(1)
  const [selectedRowKeys, setSelectedRowKeys] = useState({ ids: [] })
  //组件挂载
  useEffect(() => {
    getList({ page: currentPage, limit: pageLimit })
    getStoreList()
  }, [])
  const handleCheck = (record)=>{
    history.push({ pathname: '/storage/editTransfer'});
  }
  const handkeDelete = (record)=>{
    dispatch({
      type: 'warnHome/deleteList',
      payload: record
    })
    console.log('测试删除',record)
  }
  const getList = (params) => {
    dispatch({
      type: 'warnHome/list',
      payload: params
    })
  }

  const getStoreList = () => {
    dispatch({
      type: 'warnHome/storeList',
      payload: {}
    })
  }
  const query = () => {
    let data = form.getFieldsValue()
    console.log(data)
    //getList({...data, beginTime, endTime, page: currentPage, limit: pageLimit })
  }
  const addPreWarn = () => {
    history.push({ pathname: '/storage/addTransfer' });
  }
  const onChange = (page) => {
    setPage(page)
    setSearchParams({ ...searchParams, page });
    getList({ ...searchParams, page });
  }
  const handleAll = (idx) => {
    handleSchedule(selectedRowKeys.ids, idx)
  }
  
  const columns = [
    { dataIndex: 'fittingsNo', title: '序号' , render: (text, record, index) => {
        return ((currentPage-1) * pageLimit + index + 1)
      }
    },
    { dataIndex: 'applyCode', title: '仓库名称'},
    { dataIndex: 'applyTime', title: '仓库类型' },
    { dataIndex: 'storeNameOne', title: '仓库所在地' },
    { dataIndex: 'storeNameTwo', title: '联系方式' },
    { dataIndex: 'actualNum', title: '联系人' },
    {
      title: '操作',
      dataIndex: 'id',
      width: 150,
      render: (_, record) => {
        return <span>
        <span className="handle" onClick={() => { handleCheck(record) }} style={{marginRight:10}}>修改</span>
        <span className="handle" style={{marginRight:10}}>|</span>
        <span className="handle" onClick={() => { handkeDelete(record) }} style={{marginRight:10}}>删除</span>
        </span>
      }
    },
  ]
  const rowSelection = {
    onChange: (selectedRowKeys) => {
      setSelectedRowKeys({ ids: selectedRowKeys });
    },
  };
  return <View column>
    <View style={{ marginBottom: '20px', fontWeight: 'bold' }}>库存/库存预警</View>
    <Form layout='inline'>
      <FormItem label=''>
        {getFieldDecorator('applyCode')(
          <Input className="con-width" placeholder='根据仓位名称查找' />
        )}
      </FormItem>
      <FormItem style={{ marginLeft: '50px' }}>
        <Button icon='search' type='primary' ghost style={{ margin: '0 5px',marginRight:20}} onClick={query}>查询</Button>
        <Button type='primary' onClick={addPreWarn}>新增预警</Button>
      </FormItem>
    </Form>
    <View column style={{ marginTop: 10 }}>
      <Table columns={columns} dataSource={list} rowKey={row => row.key}
        pagination={{ current: currentPage, total: totalCount, onChange: onChange }} />
    </View>
  </View>
}
export default connect(({ warnHome, loading }) => ({
  list: warnHome.list,
  storeList: warnHome.storeList,
  totalCount: warnHome.totalCount,
  visible: warnHome.visible,
  loading: loading.effects['warnHome/list']
}))(Form.create()(WarnListPage))
