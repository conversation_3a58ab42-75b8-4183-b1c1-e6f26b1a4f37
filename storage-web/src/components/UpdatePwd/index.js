import {Modal, Form, Input, Message, Button} from 'antd'
import {connect} from 'dva'
import {removeAuthority} from '@/utils/appData'

const FormItem = Form.Item
const UpdatePwd = ({pwdVisible, dispatch, form, form: {getFieldDecorator},history, loading}) => {
  const formItemLayout = {
    labelCol: {
      xs: {span: 24},
      sm: {span: 5}
    },
    wrapperCol: {
      xs: {span: 24},
      sm: {span: 19}
    }
  }
  //提交表单
  const handleSubmit = e => {
    e.preventDefault()
    form.validateFields((err, values) => {
      if (err) return
      postData(values)
    })
  }
  // 提交数据
  const postData = async (params)=>{
    let res = await dispatch({type:'global/updatePwd',payload:{data:params}})
    if(res && res.code===0){
      Message.info('修改成功！',1).then(()=>{
        removeAuthority()
        history.push('/user/login')
      })
    }
  }
  // 关闭弹窗
  const hideWin = e => {
    form.resetFields()
    dispatch({type: 'global/pwdWinToggle'})
  }
  // 验证确认密码
  const confirmPwd = (rule, value, callback) => {
    if (value && value !== form.getFieldValue('newPassword')) {
      callback('两次输入的密码不一致');
    } else {
      callback();
    }
  }
  return <Modal visible={pwdVisible}
                title='修改密码'
                width={450}
                maskClosable={false}
                onOk={handleSubmit}
                onCancel={hideWin}
                footer={[
                  <Button key="back" onClick={hideWin}>
                    取消
                  </Button>,
                  <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
                    确定
                  </Button>,
                ]}>
    <Form layout='horizontal'>
      <FormItem {...formItemLayout} label='原密码'>
        {getFieldDecorator('password', {
          rules: [{required: true, message: '请输入原密码'}]
        })(
          <Input.Password maxLength={20} placeholder='请输入原密码'/>
        )}
      </FormItem>
      <FormItem {...formItemLayout} label='新密码'>
        {getFieldDecorator('newPassword', {
          rules: [
            {required: true, message: '请输入新密码'},
            {whitespace: true, message: '不能以空格开头',},
            {
              pattern: /^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)]|[\(\)])+$)([^(0-9a-zA-Z)]|[\(\)]|[a-z]|[A-Z]|[0-9]){8,20}$/,
              message: '密码必须是包含大小写、数字、特殊字符中两个的8-20位字符'
            }
          ]
        })(
          <Input.Password maxLength={20} placeholder='请输入新密码'/>
        )}
      </FormItem>
      <FormItem {...formItemLayout} label='重复密码'>
        {getFieldDecorator('confirm', {
          rules: [
            {required: true, message: '请再次输入密码'},
            {validator: confirmPwd}
          ]
        })(
          <Input.Password maxLength={20} placeholder='请再次输入密码'/>
        )}
      </FormItem>
    </Form>
  </Modal>
}

export default connect(({global,loading}) => ({
  pwdVisible: global.pwdVisible,
  loading:loading.effects['global/updatePwd']
}))(Form.create()(UpdatePwd))
