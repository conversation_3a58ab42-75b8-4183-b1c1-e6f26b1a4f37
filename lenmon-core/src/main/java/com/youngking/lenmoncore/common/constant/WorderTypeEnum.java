package com.youngking.lenmoncore.common.constant;

/**
 * Created by zhangyibo on 2020-08-17 14:36
 */

public enum WorderTypeEnum {

    /**
     * 服务-勘测工单类型
     */
    SERVE_CONVEY(4),
    /**
     * 服务-安装工单类型
     */
    SERVE_INSTALL(2),
    /**
     * 服务-勘安工单类型
     */
    SERVE_CONVEY_INSTALL(5),
    /**
     * 服务-维修工单类型
     */
    SERVE_REPAIR(6);

    private final Integer id;

    WorderTypeEnum(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }
}
