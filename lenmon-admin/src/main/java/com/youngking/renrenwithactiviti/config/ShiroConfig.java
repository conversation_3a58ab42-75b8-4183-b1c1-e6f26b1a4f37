/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.youngking.renrenwithactiviti.config;


import com.youngking.renrenwithactiviti.modules.sys.oauth2.OAuth2Filter;
import com.youngking.renrenwithactiviti.modules.sys.oauth2.OAuth2Realm;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.spring.LifecycleBeanPostProcessor;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Shiro配置
 *
 * <AUTHOR>
 */
@Configuration
public class ShiroConfig {

    @Bean("securityManager")
    public SecurityManager securityManager(OAuth2Realm oAuth2Realm) {
        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
        securityManager.setRealm(oAuth2Realm);
        securityManager.setRememberMeManager(null);
        return securityManager;
    }

    @Bean("shiroFilter")
    public ShiroFilterFactoryBean shirFilter(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
        shiroFilter.setSecurityManager(securityManager);

        //oauth过滤
        Map<String, Filter> filters = new HashMap<>();
        filters.put("oauth2", new OAuth2Filter());
        shiroFilter.setFilters(filters);

        Map<String, String> filterMap = new LinkedHashMap<>();
        filterMap.put("/h5/**", "anon");
        filterMap.put("/webjars/**", "anon");
        filterMap.put("/druid/**", "anon");
        filterMap.put("/activititest/**", "anon");
        filterMap.put("/service/**", "anon");
        filterMap.put("/modeler.html","anon");
        filterMap.put("/act/**", "anon");
        filterMap.put("/static/plugin/**", "anon");
        filterMap.put("/plugin/**", "anon");
        filterMap.put("/editor-app/**", "anon");
        //filterMap.put("/app/**", "anon");
        filterMap.put("/sys/login", "anon");
        filterMap.put("/sys/captcha", "anon");
        filterMap.put("/swagger/**", "anon");
        filterMap.put("/v2/api-docs", "anon");
        filterMap.put("/swagger-ui.html", "anon");
        filterMap.put("/swagger-resources/**", "anon");
        filterMap.put("/captcha.jpg", "anon");
        filterMap.put("/aaa.txt", "anon");

        filterMap.put("/app/attendant/**", "anon");
        filterMap.put("/dot/information/**", "anon");

        filterMap.put("/sys/file/upload", "anon");
        filterMap.put("/sys/file/getOssThumbnailOrBank", "anon");
        filterMap.put("/worder/appVersion/info", "anon");
        filterMap.put("/worder/bizattendant/getTechnicianLevel", "anon");
        filterMap.put("/dmj/openapi/bydApi/**", "anon");
        filterMap.put("/dmj/openapi/news/**", "anon");

        filterMap.put("/dmj/openapi/caApi/**", "anon");
        filterMap.put("/dmj/openapi/honeywellApi/**", "anon");
        filterMap.put("/dmj/openapi/xkApi/**", "anon");

        filterMap.put("/dmj/openapi/ccApi/**", "anon");

        filterMap.put("/dmj/api/**", "anon");


        // 手机号登陆短信验证接口
        filterMap.put("/sms/send","anon");
        //计算用户增项费用接口
        filterMap.put("/worder/branchbalance/userbalance","anon");
        //增项结算推送ACS记账的接口
        filterMap.put("/worderinformationaccount/pushAcsAccountIncre","anon");

        filterMap.put("/callBack/invoiceCallback", "anon");
        filterMap.put("/callBack/billingCallback", "anon");
        filterMap.put("/payCallback/synchronouNotify", "anon");
        filterMap.put("/payCallback/alipayCallback", "anon");
        filterMap.put("/companyInvoice/callbackAuditNotify", "anon");
        filterMap.put("/companyInvoice/callbackReceivableNotice", "anon");
        filterMap.put("/companyInvoice/callbackReceivableNoticeInvalid", "anon");
        filterMap.put("/companyInvoice/callbackInfoSync", "anon");
        filterMap.put("/companyInvoice/callbackReceivableAudit", "anon");
        filterMap.put("/companyInvoice/callbackSettlementAuditNotify", "anon");
        filterMap.put("/**", "oauth2");
        shiroFilter.setFilterChainDefinitionMap(filterMap);

        return shiroFilter;
    }

    @Bean("lifecycleBeanPostProcessor")
    public LifecycleBeanPostProcessor lifecycleBeanPostProcessor() {
        return new LifecycleBeanPostProcessor();
    }

    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(SecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor advisor = new AuthorizationAttributeSourceAdvisor();
        advisor.setSecurityManager(securityManager);
        return advisor;
    }

}
