spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************************************************************************
      username: root
      password: G5#df87rt!qw
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: false
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

  redis:
    open: true  # 是否开启redis缓存  true开启   false关闭
    database: 0
    host: *************
    port: 6379
    password: rrsadmin   # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接

dataType: mysql
##多数据源的配置
dynamic:
  datasource:
    slave1:
      driver-class-name: com.mysql.jdbc.Driver
      url: *******************************************************************************************************************
      username: root
      password: G5#df87rt!qw
#    slave2:
#      driver-class-name: org.postgresql.Driver
#      url: ************************************************
#      username: renren
#      password: 123456

logging:
  path: /mnt2/logs/rrs-8081/dev/springlog
  level:
    com.bonc.rrs.worder.dao: debug
    com.bonc.rrs.worderapp.dao: debug
    com.bonc.rrs.workManager.dao: debug
    com.bonc.rrs.worderinformationaccount.dao: debug
    com.bonc.rrs.branchbalance.dao: debug
    com.bonc.rrs.balanceprocess.dao: debug
    com.youngking.renrenwithactiviti.modules.sys.dao: debug
    com.bonc.rrs.worderinvoice.dao.WorderWaitAccountDao: debug

ip:
  address: ************,*************,************

oss:
  bucketName: sh-rrs
baseUrl: https://uat.rrskjfw.com.cn/
# 360额度
quotawsdl: TransEffectiveLimitFromCBSTo360.wsdl
quotalocation: classpath:TransEffectiveLimitFromCBSTo360.wsdl
# sap余额
sapwsdl: TransSupplierBalanceFromEVSToSAP.wsdl
saplocation: classpath:TransSupplierBalanceFromEVSToSAP.wsdl
# 金税
headwsdl: InsertLSMallZssHead.wsdl
headlocation: classpath:InsertLSMallZssHead.wsdl
itemwsdl: InsertLSMallZssItemNew.wsdl
itemlocation: classpath:InsertLSMallZssItemNew.wsdl
zsszfwsdl: InsertXwsqZssZF.wsdl
zsszflocation: classpath:InsertXwsqZssZF.wsdl
cacelwsdl: QueryCancelInterface.wsdl
cacelocation: classpath:QueryCancelInterface.wsdl
caceltable: qdtest.xwsq_zzs_zfhx
table: qdtest.xwsq_zzs_kphx
#ACS
acsurl: http://************:9001/12CTEST/EAI/RoutingProxyService/EAI_REST_POST_ServiceRoot?INT_CODE=EAI_INT_2552
#商户通
busicvp: http://cvp.hoptest.haier.net/services/busiCommon/Busi_Common_Srv?wsdl
hcspcvp: http://cvp.hoptest.haier.net/services/hcsp/HCSP_Sheet_Srv?wsdl
#海尔能力平台接口配置
hscapi:
  application:
    #系统编码
    key: 5f3e05613722cc046cfae6dbd3922743
    #系统秘钥
    secret: 5a00de01bebda4332d4bebccbfc6f6ca
  #票税云
  blade-seller-business:
    sellerinvoice:
      #票税云开票申请
      setXwsqZzsHeadVO:
        url: http://hscapi.haier.net/blade-seller-business/seller-apply-master/setXwsqZzsHeadVOCs
        methodName: setXwsqZzsHeadVO
      #票税云开票结果查询
      getSellerInvoice:
        url: http://hscapi.haier.net/blade-seller-business/sellerinvoice/getSellerInvoiceCs
        methodName: getSellerInvoice
byd:
  url: http://weixin109.bydauto.com.cn:18081
  AppSecret: k2hd83nsh4482jak261nsk3k382ha9g4
  APP_KEY: hd778h3h65k6978324nb2j1k3k7326hs
xk:
  url: https://xkdg.test.pc.rrskjfw.com.cn/prod-api
  AppSecret: k2hd83nsh4482jak261nsk3k382ha9g4
  APP_KEY: hd778h3h65k6978324nb2j1k3k7326hs