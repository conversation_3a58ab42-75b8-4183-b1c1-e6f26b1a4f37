spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.jdbc.Driver
      url: ****************************************************************************************************************************************************************************************
      username: bonc
      password: Bonc1234*
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      log-abandoned: true
      remove-abandoned: true
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

  redis:
    open: true  # 是否开启redis缓存  true开启   false关闭
    database: 0
    host: r-uf6ctchwapkrzmvgpx.redis.rds.aliyuncs.com
    port: 6379
    password: Rrs@admin   # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    lettuce:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 1000      # 连接池中的最大空闲连接
        min-idle: 200       # 连接池中的最小空闲连接
send-msg:
  enable: true
ip:
  address: *************,************,*************

oss:
  bucketName: ah-rrs
baseUrl: https://gd.rrskjfw.com.cn/

dataType: mysql
##多数据源的配置
dynamic:
  datasource:
    slave1:
      driver-class-name: com.mysql.jdbc.Driver
      url: ******************************************************************************************************************************************
      username: yhxx
      password: Yhxx@123
      #超过时间限制是否回收
      remove-abandoned: true
      #超时时间；单位为秒
      remove-abandoned-timeout: 1800
#    slave2:
#      driver-class-name: org.postgresql.Driver
#      url: ************************************************
#      username: renren
#      password: 123456

logging:
  level:
    com.bonc.rrs.worder.dao: debug
    com.bonc.rrs.worderapp.dao: debug
    com.bonc.rrs.workManager.dao: debug
    com.bonc.rrs.supervise.dao: debug
    com.bonc.rrs.worderinformationaccount.dao: debug
    com.bonc.rrs.branchbalance.dao.BranchBalanceDao: debug
    com.bonc.rrs.util.orderlog: debug
  path: /mnt2/rrs-8081/logs/springlog

#wsdl配置文件
# 360额度
quotawsdl: prodwsdl/TransEffectiveLimitFromCBSTo360.wsdl
quotalocation: classpath:prodwsdl/TransEffectiveLimitFromCBSTo360.wsdl
# sap余额
sapwsdl: prodwsdl/TransSupplierBalanceFromEVSToSAP.wsdl
saplocation: classpath:prodwsdl/TransSupplierBalanceFromEVSToSAP.wsdl
# 金税
headwsdl: prodwsdl/InsertLSMallZssHead.wsdl
headlocation: classpath:prodwsdl/InsertLSMallZssHead.wsdl
itemwsdl: prodwsdl/InsertLSMallZssItemNew.wsdl
itemlocation: classpath:prodwsdl/InsertLSMallZssItemNew.wsdl
zsszfwsdl: prodwsdl/InsertXwsqZssZF.wsdl
zsszflocation: classpath:prodwsdl/InsertXwsqZssZF.wsdl
cacelwsdl: prodwsdl/QueryCancelInterface.wsdl
cacelocation: classpath:prodwsdl/QueryCancelInterface.wsdl
caceltable: RWKJ.xwsq_zzs_zfhx
table: RWKJ.xwsq_zzs_kphx
# acs
acsurl: http://58.56.128.10:19001/EAI/service/ACS/TransInfoToACSPost/TransInfoToACSPost?INT_CODE=EAI_INT_2552
#商户通
busicvp: http://cvp.haier.net/services/busiCommon/Busi_Common_Srv?wsdl
hcspcvp: http://cvp.haier.net/services/hcsp/HCSP_Sheet_Srv?wsdl
#海尔能力平台接口配置
hscapi:
  application:
    #系统编码
    key: 5f3e05613722cc046cfae6dbd3922743
    #系统秘钥
    secret: 5a00de01bebda4332d4bebccbfc6f6ca
  #票税云
  blade-seller-business:
    sellerinvoice:
      #票税云开票申请
      setXwsqZzsHeadVO:
        url: http://hscapi.haier.net/blade-seller-business/seller-apply-master/setXwsqZzsHeadVO
        methodName: setXwsqZzsHeadVO
      #票税云开票结果查询
      getSellerInvoice:
        url: http://hscapi.haier.net/blade-seller-business/sellerinvoice/getSellerInvoice
        methodName: getSellerInvoice
      #税票云凭证归档
      setXwsqZzsArchive:
        url: http://hscapi.haier.net/blade-seller-business/sellerinvoice/setXwsqZzsArchive
        methodName: setXwsqZzsArchive

finance:
  business:
    appId: news
    key: XA12#Da
    insertOrUpdateUrl: http://cw.rrskjfw.com.cn/prod-api/finance/carMergerOrder/insertOrUpdate
    deleteOrderUrl: http://cw.rrskjfw.com.cn/prod-api/finance/carMergerOrder/deleteOrder
    transferSubOrderUrl: http://cw.rrskjfw.com.cn/prod-api/finance/details/transferSubOrder
    detailsEditUrl: http://cw.rrskjfw.com.cn/prod-api/finance/details/edit
    invoiceEditUrl: http://cw.rrskjfw.com.cn/prod-api/finance/headers/invoiceEdit
    searchOrderUrl: http://cw.rrskjfw.com.cn/prod-api/finance/carMergerOrder/selectOrder
    transferApprovalFormUrl: http://cw.rrskjfw.com.cn/prod-api/finance/approval/transferApprovalForm
    selectSettlementStatusUrl: http://cw.rrskjfw.com.cn/prod-api/lfs/hcsp/selectSettlementStatus
    settlementCancelUrl: http://cw.rrskjfw.com.cn/prod-api/lfs/hcsp/SettlementCancel
    newsDataAccessUrl: http://cw.rrskjfw.com.cn/prod-api/lfs/hcsp/newsDataAccess
    settleAuditAddUrl: http://cw.rrskjfw.com.cn/prod-api/settleAudit/add
    advAuditAddUrl: http://cw.rrskjfw.com.cn/prod-api/lfs/advAudit/add
    lfsSettleAuditAddUrl: http://cw.rrskjfw.com.cn/prod-api/lfs/settleAudit/add
    acAddUrl: http://cw.rrskjfw.com.cn/prod-api/finance/ac/add
    newAdvanceSettlementUrl: http://cw.rrskjfw.com.cn/prod-api/fncSettleAuditNew/newAdvanceSettlement
    callback:
      appId: finance
      appSecret: hrlLLGEaShHP6yE72he1l45T8jj7SVuU
storage:
  business:
    appId: storage
    secret: hZur4Kac
    leaveStoreUrl: https://gd.rrskjfw.com.cn/rrs-storage/smt/RRSGoodsLeaveOrder/leaveStoreIntf
    handleOutCompleteUrl: https://gd.rrskjfw.com.cn/rrs-storage/smt/RRSGoodsLeaveOrder/handleOutCompleteIntf
    handleInvalidUrl: https://gd.rrskjfw.com.cn/rrs-storage/smt/RRSGoodsLeaveOrder/handleInvalidIntf
byd:
  url: https://cpim.byd.com
  AppSecret: k1982eu9vtrooj9ehjwzh2gtmu2876cn
  APP_KEY: rnfkw51hjk87f3ur8932h8d38g233h65
xk:
  url: https://xkdg.prod.pc.rrskjfw.com.cn/prod-api
  AppSecret: k1982eu9vtrooj9ehjwzh2gtmu2876cn
  APP_KEY: rnfkw51hjk87f3ur8932h8d38g233h65

# 长安接口地址
cacfg:
  ACC_KEY: 2NS5XFLJ91AKMSN90776
  SECRE_KEY: 2BJZTFJGC89103M2RZAFARNQXV7ZGFO9
  SUPPLIER_CODE: *********
  baseUrl: https://dtd.changan.com.cn/gateway/api/
  ##车企离线对接
bridgehub:
  jlUrl: https://sp-recharge.geely.com/InstallManage/orderDetail/
  url: http://10.10.10.128:8080/bridgehub/
  token: aaaaaa
  paramToken: 4df6999b3e91466ca9e195b55b205ca4


honeywell:
  # prod
  baseUrl: https://dms-api.hopeful.company
  client_id: dmj_system
  client_secret: x93hzuAXBR56Xfnv1
  grant_type: client_credentials password_sms
  scope: read

news:
  gace:
    app-id: DMJ
    app-secret: DMJ@202410150001123a
    base-url: https://srm.gace.com.cn/ierp
    user: 12341111234
    id-number: HERRS
    page-size: 50
    company-id: 684
    query-hours: 3
    cancel-days: 1
    audit-days: 30
    field:
      woop-id: 1988
      asset-id: 1989
      woop-no: 1990
      act-finish-time: 1197
      act-check-time: 1062
      fault-tree-type: 2033
    tel: 400-9219898
    template-ids:
      - 713
      - 714
      - 715
      - 716
      - 717
      - 826
      - 827
    woop-wcp-id-number: DMJ
    repair-template-id: 378
    amap-param:
      key: d3f2628800113145b90dd8c81df8e3dd
      url: https://restapi.amap.com/v3/geocode/geo
  lx:
    report-temp-path: /mnt2/file/

changcheng:
  persistentCcToken: J1MizRUsq3pG0JeqomCDz6rY08GipEvM

# API接口安全配置
api:
  security:
    # 是否启用接口安全认证
    enabled: true
    # 客户端密钥过期时间（小时）
    token-expire: 24
    # 客户端密钥配置
    clients:
      # 企业内部系统客户端 生成随机密钥
      service_card_id: q9HjWbLv5tR3yU7oP1xKzJ6mNc8gT0Xa
